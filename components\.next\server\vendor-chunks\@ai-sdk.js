"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk";
exports.ids = ["vendor-chunks/@ai-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@ai-sdk/provider-utils/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asValidator: () => (/* binding */ asValidator),\n/* harmony export */   combineHeaders: () => (/* binding */ combineHeaders),\n/* harmony export */   convertAsyncIteratorToReadableStream: () => (/* binding */ convertAsyncIteratorToReadableStream),\n/* harmony export */   convertBase64ToUint8Array: () => (/* binding */ convertBase64ToUint8Array),\n/* harmony export */   convertUint8ArrayToBase64: () => (/* binding */ convertUint8ArrayToBase64),\n/* harmony export */   createBinaryResponseHandler: () => (/* binding */ createBinaryResponseHandler),\n/* harmony export */   createEventSourceParserStream: () => (/* binding */ createEventSourceParserStream),\n/* harmony export */   createEventSourceResponseHandler: () => (/* binding */ createEventSourceResponseHandler),\n/* harmony export */   createIdGenerator: () => (/* binding */ createIdGenerator),\n/* harmony export */   createJsonErrorResponseHandler: () => (/* binding */ createJsonErrorResponseHandler),\n/* harmony export */   createJsonResponseHandler: () => (/* binding */ createJsonResponseHandler),\n/* harmony export */   createJsonStreamResponseHandler: () => (/* binding */ createJsonStreamResponseHandler),\n/* harmony export */   createStatusCodeErrorResponseHandler: () => (/* binding */ createStatusCodeErrorResponseHandler),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   extractResponseHeaders: () => (/* binding */ extractResponseHeaders),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   getFromApi: () => (/* binding */ getFromApi),\n/* harmony export */   isAbortError: () => (/* binding */ isAbortError),\n/* harmony export */   isParsableJson: () => (/* binding */ isParsableJson),\n/* harmony export */   isValidator: () => (/* binding */ isValidator),\n/* harmony export */   loadApiKey: () => (/* binding */ loadApiKey),\n/* harmony export */   loadOptionalSetting: () => (/* binding */ loadOptionalSetting),\n/* harmony export */   loadSetting: () => (/* binding */ loadSetting),\n/* harmony export */   parseJSON: () => (/* binding */ parseJSON),\n/* harmony export */   parseProviderOptions: () => (/* binding */ parseProviderOptions),\n/* harmony export */   postFormDataToApi: () => (/* binding */ postFormDataToApi),\n/* harmony export */   postJsonToApi: () => (/* binding */ postJsonToApi),\n/* harmony export */   postToApi: () => (/* binding */ postToApi),\n/* harmony export */   removeUndefinedEntries: () => (/* binding */ removeUndefinedEntries),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   safeValidateTypes: () => (/* binding */ safeValidateTypes),\n/* harmony export */   validateTypes: () => (/* binding */ validateTypes),\n/* harmony export */   validator: () => (/* binding */ validator),\n/* harmony export */   validatorSymbol: () => (/* binding */ validatorSymbol),\n/* harmony export */   withoutTrailingSlash: () => (/* binding */ withoutTrailingSlash),\n/* harmony export */   zodValidator: () => (/* binding */ zodValidator)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/./node_modules/nanoid/non-secure/index.js\");\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/secure-json-parse/index.js\");\n// src/combine-headers.ts\nfunction combineHeaders(...headers) {\n  return headers.reduce(\n    (combinedHeaders, currentHeaders) => ({\n      ...combinedHeaders,\n      ...currentHeaders != null ? currentHeaders : {}\n    }),\n    {}\n  );\n}\n\n// src/convert-async-iterator-to-readable-stream.ts\nfunction convertAsyncIteratorToReadableStream(iterator) {\n  return new ReadableStream({\n    /**\n     * Called when the consumer wants to pull more data from the stream.\n     *\n     * @param {ReadableStreamDefaultController<T>} controller - The controller to enqueue data into the stream.\n     * @returns {Promise<void>}\n     */\n    async pull(controller) {\n      try {\n        const { value, done } = await iterator.next();\n        if (done) {\n          controller.close();\n        } else {\n          controller.enqueue(value);\n        }\n      } catch (error) {\n        controller.error(error);\n      }\n    },\n    /**\n     * Called when the consumer cancels the stream.\n     */\n    cancel() {\n    }\n  });\n}\n\n// src/delay.ts\nasync function delay(delayInMs) {\n  return delayInMs == null ? Promise.resolve() : new Promise((resolve2) => setTimeout(resolve2, delayInMs));\n}\n\n// src/event-source-parser-stream.ts\nfunction createEventSourceParserStream() {\n  let buffer = \"\";\n  let event = void 0;\n  let data = [];\n  let lastEventId = void 0;\n  let retry = void 0;\n  function parseLine(line, controller) {\n    if (line === \"\") {\n      dispatchEvent(controller);\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      return;\n    }\n    const colonIndex = line.indexOf(\":\");\n    if (colonIndex === -1) {\n      handleField(line, \"\");\n      return;\n    }\n    const field = line.slice(0, colonIndex);\n    const valueStart = colonIndex + 1;\n    const value = valueStart < line.length && line[valueStart] === \" \" ? line.slice(valueStart + 1) : line.slice(valueStart);\n    handleField(field, value);\n  }\n  function dispatchEvent(controller) {\n    if (data.length > 0) {\n      controller.enqueue({\n        event,\n        data: data.join(\"\\n\"),\n        id: lastEventId,\n        retry\n      });\n      data = [];\n      event = void 0;\n      retry = void 0;\n    }\n  }\n  function handleField(field, value) {\n    switch (field) {\n      case \"event\":\n        event = value;\n        break;\n      case \"data\":\n        data.push(value);\n        break;\n      case \"id\":\n        lastEventId = value;\n        break;\n      case \"retry\":\n        const parsedRetry = parseInt(value, 10);\n        if (!isNaN(parsedRetry)) {\n          retry = parsedRetry;\n        }\n        break;\n    }\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      const { lines, incompleteLine } = splitLines(buffer, chunk);\n      buffer = incompleteLine;\n      for (let i = 0; i < lines.length; i++) {\n        parseLine(lines[i], controller);\n      }\n    },\n    flush(controller) {\n      parseLine(buffer, controller);\n      dispatchEvent(controller);\n    }\n  });\n}\nfunction splitLines(buffer, chunk) {\n  const lines = [];\n  let currentLine = buffer;\n  for (let i = 0; i < chunk.length; ) {\n    const char = chunk[i++];\n    if (char === \"\\n\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n    } else if (char === \"\\r\") {\n      lines.push(currentLine);\n      currentLine = \"\";\n      if (chunk[i] === \"\\n\") {\n        i++;\n      }\n    } else {\n      currentLine += char;\n    }\n  }\n  return { lines, incompleteLine: currentLine };\n}\n\n// src/extract-response-headers.ts\nfunction extractResponseHeaders(response) {\n  const headers = {};\n  response.headers.forEach((value, key) => {\n    headers[key] = value;\n  });\n  return headers;\n}\n\n// src/generate-id.ts\n\n\nvar createIdGenerator = ({\n  prefix,\n  size: defaultSize = 16,\n  alphabet = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\",\n  separator = \"-\"\n} = {}) => {\n  const generator = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(alphabet, defaultSize);\n  if (prefix == null) {\n    return generator;\n  }\n  if (alphabet.includes(separator)) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"separator\",\n      message: `The separator \"${separator}\" must not be part of the alphabet \"${alphabet}\".`\n    });\n  }\n  return (size) => `${prefix}${separator}${generator(size)}`;\n};\nvar generateId = createIdGenerator();\n\n// src/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/get-from-api.ts\n\n\n// src/remove-undefined-entries.ts\nfunction removeUndefinedEntries(record) {\n  return Object.fromEntries(\n    Object.entries(record).filter(([_key, value]) => value != null)\n  );\n}\n\n// src/is-abort-error.ts\nfunction isAbortError(error) {\n  return error instanceof Error && (error.name === \"AbortError\" || error.name === \"TimeoutError\");\n}\n\n// src/get-from-api.ts\nvar getOriginalFetch = () => globalThis.fetch;\nvar getFromApi = async ({\n  url,\n  headers = {},\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"GET\",\n      headers: removeUndefinedEntries(headers),\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: {}\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: {}\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: {}\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: {}\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          isRetryable: true,\n          requestBodyValues: {}\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/load-api-key.ts\n\nfunction loadApiKey({\n  apiKey,\n  environmentVariableName,\n  apiKeyParameterName = \"apiKey\",\n  description\n}) {\n  if (typeof apiKey === \"string\") {\n    return apiKey;\n  }\n  if (apiKey != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  apiKey = process.env[environmentVariableName];\n  if (apiKey == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key is missing. Pass it using the '${apiKeyParameterName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof apiKey !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadAPIKeyError({\n      message: `${description} API key must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return apiKey;\n}\n\n// src/load-optional-setting.ts\nfunction loadOptionalSetting({\n  settingValue,\n  environmentVariableName\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null || typeof process === \"undefined\") {\n    return void 0;\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null || typeof settingValue !== \"string\") {\n    return void 0;\n  }\n  return settingValue;\n}\n\n// src/load-setting.ts\n\nfunction loadSetting({\n  settingValue,\n  environmentVariableName,\n  settingName,\n  description\n}) {\n  if (typeof settingValue === \"string\") {\n    return settingValue;\n  }\n  if (settingValue != null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string.`\n    });\n  }\n  if (typeof process === \"undefined\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter. Environment variables is not supported in this environment.`\n    });\n  }\n  settingValue = process.env[environmentVariableName];\n  if (settingValue == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting is missing. Pass it using the '${settingName}' parameter or the ${environmentVariableName} environment variable.`\n    });\n  }\n  if (typeof settingValue !== \"string\") {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.LoadSettingError({\n      message: `${description} setting must be a string. The value of the ${environmentVariableName} environment variable is not a string.`\n    });\n  }\n  return settingValue;\n}\n\n// src/parse-json.ts\n\n\n\n// src/validate-types.ts\n\n\n// src/validator.ts\nvar validatorSymbol = Symbol.for(\"vercel.ai.validator\");\nfunction validator(validate) {\n  return { [validatorSymbol]: true, validate };\n}\nfunction isValidator(value) {\n  return typeof value === \"object\" && value !== null && validatorSymbol in value && value[validatorSymbol] === true && \"validate\" in value;\n}\nfunction asValidator(value) {\n  return isValidator(value) ? value : zodValidator(value);\n}\nfunction zodValidator(zodSchema) {\n  return validator((value) => {\n    const result = zodSchema.safeParse(value);\n    return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n  });\n}\n\n// src/validate-types.ts\nfunction validateTypes({\n  value,\n  schema: inputSchema\n}) {\n  const result = safeValidateTypes({ value, schema: inputSchema });\n  if (!result.success) {\n    throw _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error });\n  }\n  return result.value;\n}\nfunction safeValidateTypes({\n  value,\n  schema\n}) {\n  const validator2 = asValidator(schema);\n  try {\n    if (validator2.validate == null) {\n      return { success: true, value };\n    }\n    const result = validator2.validate(value);\n    if (result.success) {\n      return result;\n    }\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: result.error })\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.wrap({ value, cause: error })\n    };\n  }\n}\n\n// src/parse-json.ts\nfunction parseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return value;\n    }\n    return validateTypes({ value, schema });\n  } catch (error) {\n    if (_ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TypeValidationError.isInstance(error)) {\n      throw error;\n    }\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error });\n  }\n}\nfunction safeParseJSON({\n  text,\n  schema\n}) {\n  try {\n    const value = secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(text);\n    if (schema == null) {\n      return { success: true, value, rawValue: value };\n    }\n    const validationResult = safeValidateTypes({ value, schema });\n    return validationResult.success ? { ...validationResult, rawValue: value } : validationResult;\n  } catch (error) {\n    return {\n      success: false,\n      error: _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError.isInstance(error) ? error : new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.JSONParseError({ text, cause: error })\n    };\n  }\n}\nfunction isParsableJson(input) {\n  try {\n    secure_json_parse__WEBPACK_IMPORTED_MODULE_2__.parse(input);\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// src/parse-provider-options.ts\n\nfunction parseProviderOptions({\n  provider,\n  providerOptions,\n  schema\n}) {\n  if ((providerOptions == null ? void 0 : providerOptions[provider]) == null) {\n    return void 0;\n  }\n  const parsedProviderOptions = safeValidateTypes({\n    value: providerOptions[provider],\n    schema\n  });\n  if (!parsedProviderOptions.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidArgumentError({\n      argument: \"providerOptions\",\n      message: `invalid ${provider} provider options`,\n      cause: parsedProviderOptions.error\n    });\n  }\n  return parsedProviderOptions.value;\n}\n\n// src/post-to-api.ts\n\nvar getOriginalFetch2 = () => globalThis.fetch;\nvar postJsonToApi = async ({\n  url,\n  headers,\n  body,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers: {\n    \"Content-Type\": \"application/json\",\n    ...headers\n  },\n  body: {\n    content: JSON.stringify(body),\n    values: body\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postFormDataToApi = async ({\n  url,\n  headers,\n  formData,\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n}) => postToApi({\n  url,\n  headers,\n  body: {\n    content: formData,\n    values: Object.fromEntries(formData.entries())\n  },\n  failedResponseHandler,\n  successfulResponseHandler,\n  abortSignal,\n  fetch\n});\nvar postToApi = async ({\n  url,\n  headers = {},\n  body,\n  successfulResponseHandler,\n  failedResponseHandler,\n  abortSignal,\n  fetch = getOriginalFetch2()\n}) => {\n  try {\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: removeUndefinedEntries(headers),\n      body: body.content,\n      signal: abortSignal\n    });\n    const responseHeaders = extractResponseHeaders(response);\n    if (!response.ok) {\n      let errorInformation;\n      try {\n        errorInformation = await failedResponseHandler({\n          response,\n          url,\n          requestBodyValues: body.values\n        });\n      } catch (error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: \"Failed to process error response\",\n          cause: error,\n          statusCode: response.status,\n          url,\n          responseHeaders,\n          requestBodyValues: body.values\n        });\n      }\n      throw errorInformation.value;\n    }\n    try {\n      return await successfulResponseHandler({\n        response,\n        url,\n        requestBodyValues: body.values\n      });\n    } catch (error) {\n      if (error instanceof Error) {\n        if (isAbortError(error) || _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError.isInstance(error)) {\n          throw error;\n        }\n      }\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: \"Failed to process successful response\",\n        cause: error,\n        statusCode: response.status,\n        url,\n        responseHeaders,\n        requestBodyValues: body.values\n      });\n    }\n  } catch (error) {\n    if (isAbortError(error)) {\n      throw error;\n    }\n    if (error instanceof TypeError && error.message === \"fetch failed\") {\n      const cause = error.cause;\n      if (cause != null) {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n          message: `Cannot connect to API: ${cause.message}`,\n          cause,\n          url,\n          requestBodyValues: body.values,\n          isRetryable: true\n          // retry when network error\n        });\n      }\n    }\n    throw error;\n  }\n};\n\n// src/resolve.ts\nasync function resolve(value) {\n  if (typeof value === \"function\") {\n    value = value();\n  }\n  return Promise.resolve(value);\n}\n\n// src/response-handler.ts\n\nvar createJsonErrorResponseHandler = ({\n  errorSchema,\n  errorToMessage,\n  isRetryable\n}) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const responseHeaders = extractResponseHeaders(response);\n  if (responseBody.trim() === \"\") {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n  try {\n    const parsedError = parseJSON({\n      text: responseBody,\n      schema: errorSchema\n    });\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: errorToMessage(parsedError),\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        data: parsedError,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response, parsedError)\n      })\n    };\n  } catch (parseError) {\n    return {\n      responseHeaders,\n      value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n        message: response.statusText,\n        url,\n        requestBodyValues,\n        statusCode: response.status,\n        responseHeaders,\n        responseBody,\n        isRetryable: isRetryable == null ? void 0 : isRetryable(response)\n      })\n    };\n  }\n};\nvar createEventSourceResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(createEventSourceParserStream()).pipeThrough(\n      new TransformStream({\n        transform({ data }, controller) {\n          if (data === \"[DONE]\") {\n            return;\n          }\n          controller.enqueue(\n            safeParseJSON({\n              text: data,\n              schema: chunkSchema\n            })\n          );\n        }\n      })\n    )\n  };\n};\nvar createJsonStreamResponseHandler = (chunkSchema) => async ({ response }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (response.body == null) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.EmptyResponseBodyError({});\n  }\n  let buffer = \"\";\n  return {\n    responseHeaders,\n    value: response.body.pipeThrough(new TextDecoderStream()).pipeThrough(\n      new TransformStream({\n        transform(chunkText, controller) {\n          if (chunkText.endsWith(\"\\n\")) {\n            controller.enqueue(\n              safeParseJSON({\n                text: buffer + chunkText,\n                schema: chunkSchema\n              })\n            );\n            buffer = \"\";\n          } else {\n            buffer += chunkText;\n          }\n        }\n      })\n    )\n  };\n};\nvar createJsonResponseHandler = (responseSchema) => async ({ response, url, requestBodyValues }) => {\n  const responseBody = await response.text();\n  const parsedResult = safeParseJSON({\n    text: responseBody,\n    schema: responseSchema\n  });\n  const responseHeaders = extractResponseHeaders(response);\n  if (!parsedResult.success) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Invalid JSON response\",\n      cause: parsedResult.error,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody,\n      url,\n      requestBodyValues\n    });\n  }\n  return {\n    responseHeaders,\n    value: parsedResult.value,\n    rawValue: parsedResult.rawValue\n  };\n};\nvar createBinaryResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  if (!response.body) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Response body is empty\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0\n    });\n  }\n  try {\n    const buffer = await response.arrayBuffer();\n    return {\n      responseHeaders,\n      value: new Uint8Array(buffer)\n    };\n  } catch (error) {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: \"Failed to read response as array buffer\",\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody: void 0,\n      cause: error\n    });\n  }\n};\nvar createStatusCodeErrorResponseHandler = () => async ({ response, url, requestBodyValues }) => {\n  const responseHeaders = extractResponseHeaders(response);\n  const responseBody = await response.text();\n  return {\n    responseHeaders,\n    value: new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.APICallError({\n      message: response.statusText,\n      url,\n      requestBodyValues,\n      statusCode: response.status,\n      responseHeaders,\n      responseBody\n    })\n  };\n};\n\n// src/uint8-utils.ts\nvar { btoa, atob } = globalThis;\nfunction convertBase64ToUint8Array(base64String) {\n  const base64Url = base64String.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  const latin1string = atob(base64Url);\n  return Uint8Array.from(latin1string, (byte) => byte.codePointAt(0));\n}\nfunction convertUint8ArrayToBase64(array) {\n  let latin1string = \"\";\n  for (let i = 0; i < array.length; i++) {\n    latin1string += String.fromCodePoint(array[i]);\n  }\n  return btoa(latin1string);\n}\n\n// src/without-trailing-slash.ts\nfunction withoutTrailingSlash(url) {\n  return url == null ? void 0 : url.replace(/\\/$/, \"\");\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@ai-sdk/provider/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AISDKError: () => (/* binding */ AISDKError),\n/* harmony export */   APICallError: () => (/* binding */ APICallError),\n/* harmony export */   EmptyResponseBodyError: () => (/* binding */ EmptyResponseBodyError),\n/* harmony export */   InvalidArgumentError: () => (/* binding */ InvalidArgumentError),\n/* harmony export */   InvalidPromptError: () => (/* binding */ InvalidPromptError),\n/* harmony export */   InvalidResponseDataError: () => (/* binding */ InvalidResponseDataError),\n/* harmony export */   JSONParseError: () => (/* binding */ JSONParseError),\n/* harmony export */   LoadAPIKeyError: () => (/* binding */ LoadAPIKeyError),\n/* harmony export */   LoadSettingError: () => (/* binding */ LoadSettingError),\n/* harmony export */   NoContentGeneratedError: () => (/* binding */ NoContentGeneratedError),\n/* harmony export */   NoSuchModelError: () => (/* binding */ NoSuchModelError),\n/* harmony export */   TooManyEmbeddingValuesForCallError: () => (/* binding */ TooManyEmbeddingValuesForCallError),\n/* harmony export */   TypeValidationError: () => (/* binding */ TypeValidationError),\n/* harmony export */   UnsupportedFunctionalityError: () => (/* binding */ UnsupportedFunctionalityError),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isJSONArray: () => (/* binding */ isJSONArray),\n/* harmony export */   isJSONObject: () => (/* binding */ isJSONObject),\n/* harmony export */   isJSONValue: () => (/* binding */ isJSONValue)\n/* harmony export */ });\n// src/errors/ai-sdk-error.ts\nvar marker = \"vercel.ai.error\";\nvar symbol = Symbol.for(marker);\nvar _a;\nvar _AISDKError = class _AISDKError extends Error {\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name: name14,\n    message,\n    cause\n  }) {\n    super(message);\n    this[_a] = true;\n    this.name = name14;\n    this.cause = cause;\n  }\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error) {\n    return _AISDKError.hasMarker(error, marker);\n  }\n  static hasMarker(error, marker15) {\n    const markerSymbol = Symbol.for(marker15);\n    return error != null && typeof error === \"object\" && markerSymbol in error && typeof error[markerSymbol] === \"boolean\" && error[markerSymbol] === true;\n  }\n};\n_a = symbol;\nvar AISDKError = _AISDKError;\n\n// src/errors/api-call-error.ts\nvar name = \"AI_APICallError\";\nvar marker2 = `vercel.ai.error.${name}`;\nvar symbol2 = Symbol.for(marker2);\nvar _a2;\nvar APICallError = class extends AISDKError {\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null && (statusCode === 408 || // request timeout\n    statusCode === 409 || // conflict\n    statusCode === 429 || // too many requests\n    statusCode >= 500),\n    // server error\n    data\n  }) {\n    super({ name, message, cause });\n    this[_a2] = true;\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker2);\n  }\n};\n_a2 = symbol2;\n\n// src/errors/empty-response-body-error.ts\nvar name2 = \"AI_EmptyResponseBodyError\";\nvar marker3 = `vercel.ai.error.${name2}`;\nvar symbol3 = Symbol.for(marker3);\nvar _a3;\nvar EmptyResponseBodyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message = \"Empty response body\" } = {}) {\n    super({ name: name2, message });\n    this[_a3] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker3);\n  }\n};\n_a3 = symbol3;\n\n// src/errors/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/errors/invalid-argument-error.ts\nvar name3 = \"AI_InvalidArgumentError\";\nvar marker4 = `vercel.ai.error.${name3}`;\nvar symbol4 = Symbol.for(marker4);\nvar _a4;\nvar InvalidArgumentError = class extends AISDKError {\n  constructor({\n    message,\n    cause,\n    argument\n  }) {\n    super({ name: name3, message, cause });\n    this[_a4] = true;\n    this.argument = argument;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker4);\n  }\n};\n_a4 = symbol4;\n\n// src/errors/invalid-prompt-error.ts\nvar name4 = \"AI_InvalidPromptError\";\nvar marker5 = `vercel.ai.error.${name4}`;\nvar symbol5 = Symbol.for(marker5);\nvar _a5;\nvar InvalidPromptError = class extends AISDKError {\n  constructor({\n    prompt,\n    message,\n    cause\n  }) {\n    super({ name: name4, message: `Invalid prompt: ${message}`, cause });\n    this[_a5] = true;\n    this.prompt = prompt;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker5);\n  }\n};\n_a5 = symbol5;\n\n// src/errors/invalid-response-data-error.ts\nvar name5 = \"AI_InvalidResponseDataError\";\nvar marker6 = `vercel.ai.error.${name5}`;\nvar symbol6 = Symbol.for(marker6);\nvar _a6;\nvar InvalidResponseDataError = class extends AISDKError {\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`\n  }) {\n    super({ name: name5, message });\n    this[_a6] = true;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker6);\n  }\n};\n_a6 = symbol6;\n\n// src/errors/json-parse-error.ts\nvar name6 = \"AI_JSONParseError\";\nvar marker7 = `vercel.ai.error.${name6}`;\nvar symbol7 = Symbol.for(marker7);\nvar _a7;\nvar JSONParseError = class extends AISDKError {\n  constructor({ text, cause }) {\n    super({\n      name: name6,\n      message: `JSON parsing failed: Text: ${text}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a7] = true;\n    this.text = text;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker7);\n  }\n};\n_a7 = symbol7;\n\n// src/errors/load-api-key-error.ts\nvar name7 = \"AI_LoadAPIKeyError\";\nvar marker8 = `vercel.ai.error.${name7}`;\nvar symbol8 = Symbol.for(marker8);\nvar _a8;\nvar LoadAPIKeyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name7, message });\n    this[_a8] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker8);\n  }\n};\n_a8 = symbol8;\n\n// src/errors/load-setting-error.ts\nvar name8 = \"AI_LoadSettingError\";\nvar marker9 = `vercel.ai.error.${name8}`;\nvar symbol9 = Symbol.for(marker9);\nvar _a9;\nvar LoadSettingError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name8, message });\n    this[_a9] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker9);\n  }\n};\n_a9 = symbol9;\n\n// src/errors/no-content-generated-error.ts\nvar name9 = \"AI_NoContentGeneratedError\";\nvar marker10 = `vercel.ai.error.${name9}`;\nvar symbol10 = Symbol.for(marker10);\nvar _a10;\nvar NoContentGeneratedError = class extends AISDKError {\n  // used in isInstance\n  constructor({\n    message = \"No content generated.\"\n  } = {}) {\n    super({ name: name9, message });\n    this[_a10] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker10);\n  }\n};\n_a10 = symbol10;\n\n// src/errors/no-such-model-error.ts\nvar name10 = \"AI_NoSuchModelError\";\nvar marker11 = `vercel.ai.error.${name10}`;\nvar symbol11 = Symbol.for(marker11);\nvar _a11;\nvar NoSuchModelError = class extends AISDKError {\n  constructor({\n    errorName = name10,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`\n  }) {\n    super({ name: errorName, message });\n    this[_a11] = true;\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker11);\n  }\n};\n_a11 = symbol11;\n\n// src/errors/too-many-embedding-values-for-call-error.ts\nvar name11 = \"AI_TooManyEmbeddingValuesForCallError\";\nvar marker12 = `vercel.ai.error.${name11}`;\nvar symbol12 = Symbol.for(marker12);\nvar _a12;\nvar TooManyEmbeddingValuesForCallError = class extends AISDKError {\n  constructor(options) {\n    super({\n      name: name11,\n      message: `Too many values for a single embedding call. The ${options.provider} model \"${options.modelId}\" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`\n    });\n    this[_a12] = true;\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker12);\n  }\n};\n_a12 = symbol12;\n\n// src/errors/type-validation-error.ts\nvar name12 = \"AI_TypeValidationError\";\nvar marker13 = `vercel.ai.error.${name12}`;\nvar symbol13 = Symbol.for(marker13);\nvar _a13;\nvar _TypeValidationError = class _TypeValidationError extends AISDKError {\n  constructor({ value, cause }) {\n    super({\n      name: name12,\n      message: `Type validation failed: Value: ${JSON.stringify(value)}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a13] = true;\n    this.value = value;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker13);\n  }\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause\n  }) {\n    return _TypeValidationError.isInstance(cause) && cause.value === value ? cause : new _TypeValidationError({ value, cause });\n  }\n};\n_a13 = symbol13;\nvar TypeValidationError = _TypeValidationError;\n\n// src/errors/unsupported-functionality-error.ts\nvar name13 = \"AI_UnsupportedFunctionalityError\";\nvar marker14 = `vercel.ai.error.${name13}`;\nvar symbol14 = Symbol.for(marker14);\nvar _a14;\nvar UnsupportedFunctionalityError = class extends AISDKError {\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`\n  }) {\n    super({ name: name13, message });\n    this[_a14] = true;\n    this.functionality = functionality;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker14);\n  }\n};\n_a14 = symbol14;\n\n// src/json-value/is-json.ts\nfunction isJSONValue(value) {\n  if (value === null || typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n    return true;\n  }\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n  if (typeof value === \"object\") {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n    );\n  }\n  return false;\n}\nfunction isJSONArray(value) {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\nfunction isJSONObject(value) {\n  return value != null && typeof value === \"object\" && Object.entries(value).every(\n    ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/provider/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@ai-sdk/react/dist/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   experimental_useObject: () => (/* binding */ experimental_useObject),\n/* harmony export */   useAssistant: () => (/* binding */ useAssistant),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useCompletion: () => (/* binding */ useCompletion)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/ui-utils */ \"(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var throttleit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! throttleit */ \"(ssr)/./node_modules/throttleit/index.js\");\n// src/use-assistant.ts\n\n\n\nvar getOriginalFetch = () => fetch;\nfunction useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch: fetch2\n}) {\n  const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [currentThreadId, setCurrentThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    void 0\n  );\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"awaiting_message\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const handleInputChange = (event) => {\n    setInput(event.target.value);\n  };\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const append = async (message, requestOptions) => {\n    var _a, _b;\n    setStatus(\"in_progress\");\n    setMessages((messages2) => {\n      var _a2;\n      return [\n        ...messages2,\n        {\n          ...message,\n          id: (_a2 = message.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)()\n        }\n      ];\n    });\n    setInput(\"\");\n    const abortController = new AbortController();\n    try {\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        credentials,\n        signal: abortController.signal,\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: (_a = threadIdParam != null ? threadIdParam : currentThreadId) != null ? _a : null,\n          message: message.content,\n          // optional request data:\n          data: requestOptions == null ? void 0 : requestOptions.data\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_b = await response.text()) != null ? _b : \"Failed to fetch the assistant response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.processAssistantStream)({\n        stream: response.body,\n        onAssistantMessagePart(value) {\n          setMessages((messages2) => [\n            ...messages2,\n            {\n              id: value.id,\n              role: value.role,\n              content: value.content[0].text.value,\n              parts: []\n            }\n          ]);\n        },\n        onTextPart(value) {\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            return [\n              ...messages2.slice(0, messages2.length - 1),\n              {\n                id: lastMessage.id,\n                role: lastMessage.role,\n                content: lastMessage.content + value,\n                parts: lastMessage.parts\n              }\n            ];\n          });\n        },\n        onAssistantControlDataPart(value) {\n          setCurrentThreadId(value.threadId);\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            lastMessage.id = value.messageId;\n            return [...messages2.slice(0, messages2.length - 1), lastMessage];\n          });\n        },\n        onDataMessagePart(value) {\n          setMessages((messages2) => {\n            var _a2;\n            return [\n              ...messages2,\n              {\n                id: (_a2 = value.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                role: \"data\",\n                content: \"\",\n                data: value.data,\n                parts: []\n              }\n            ];\n          });\n        },\n        onErrorPart(value) {\n          setError(new Error(value));\n        }\n      });\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setError(error2);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus(\"awaiting_message\");\n    }\n  };\n  const submitMessage = async (event, requestOptions) => {\n    var _a;\n    (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n    if (input === \"\") {\n      return;\n    }\n    append({ role: \"user\", content: input, parts: [] }, requestOptions);\n  };\n  const setThreadId = (threadId) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop\n  };\n}\n\n// src/use-chat.ts\n\n\n\n\n// src/throttle.ts\n\nfunction throttle(fn, waitMs) {\n  return waitMs != null ? throttleit__WEBPACK_IMPORTED_MODULE_3__(fn, waitMs) : fn;\n}\n\n// src/util/use-stable-value.ts\n\n\nfunction useStableValue(latestValue) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(latestValue);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestValue, value)) {\n      setValue(latestValue);\n    }\n  }, [latestValue, value]);\n  return value;\n}\n\n// src/use-chat.ts\nfunction useChat({\n  api = \"/api/chat\",\n  id,\n  initialMessages,\n  initialInput = \"\",\n  sendExtraMessageFields,\n  onToolCall,\n  experimental_prepareRequestBody,\n  maxSteps = 1,\n  streamProtocol = \"data\",\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId: generateId2 = _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n  fetch: fetch2,\n  keepLastMessageOnError = true,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const [hookId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(generateId2);\n  const chatId = id != null ? id : hookId;\n  const chatKey = typeof api === \"string\" ? [api, chatId] : chatId;\n  const stableInitialMessages = useStableValue(initialMessages != null ? initialMessages : []);\n  const processedInitialMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(stableInitialMessages),\n    [stableInitialMessages]\n  );\n  const { data: messages, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [chatKey, \"messages\"],\n    null,\n    { fallbackData: processedInitialMessages }\n  );\n  const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(messages || []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"streamData\"], null);\n  const streamDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(streamData);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    streamDataRef.current = streamData;\n  }, [streamData]);\n  const { data: status = \"ready\", mutate: mutateStatus } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"status\"], null);\n  const { data: error = void 0, mutate: setError } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"error\"], null);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (chatRequest, requestType = \"generate\") => {\n      var _a, _b;\n      mutateStatus(\"submitted\");\n      setError(void 0);\n      const chatMessages = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(chatRequest.messages);\n      const messageCount = chatMessages.length;\n      const maxStep = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.extractMaxToolInvocationStep)(\n        (_a = chatMessages[chatMessages.length - 1]) == null ? void 0 : _a.toolInvocations\n      );\n      try {\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n        const throttledMutate = throttle(mutate, throttleWaitMs);\n        const throttledMutateStreamData = throttle(\n          mutateStreamData,\n          throttleWaitMs\n        );\n        const previousMessages = messagesRef.current;\n        throttledMutate(chatMessages, false);\n        const constructedMessagesPayload = sendExtraMessageFields ? chatMessages : chatMessages.map(\n          ({\n            role,\n            content,\n            experimental_attachments,\n            data,\n            annotations,\n            toolInvocations,\n            parts\n          }) => ({\n            role,\n            content,\n            ...experimental_attachments !== void 0 && {\n              experimental_attachments\n            },\n            ...data !== void 0 && { data },\n            ...annotations !== void 0 && { annotations },\n            ...toolInvocations !== void 0 && { toolInvocations },\n            ...parts !== void 0 && { parts }\n          })\n        );\n        const existingData = streamDataRef.current;\n        await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callChatApi)({\n          api,\n          body: (_b = experimental_prepareRequestBody == null ? void 0 : experimental_prepareRequestBody({\n            id: chatId,\n            messages: chatMessages,\n            requestData: chatRequest.data,\n            requestBody: chatRequest.body\n          })) != null ? _b : {\n            id: chatId,\n            messages: constructedMessagesPayload,\n            data: chatRequest.data,\n            ...extraMetadataRef.current.body,\n            ...chatRequest.body\n          },\n          streamProtocol,\n          credentials: extraMetadataRef.current.credentials,\n          headers: {\n            ...extraMetadataRef.current.headers,\n            ...chatRequest.headers\n          },\n          abortController: () => abortControllerRef.current,\n          restoreMessagesOnFailure() {\n            if (!keepLastMessageOnError) {\n              throttledMutate(previousMessages, false);\n            }\n          },\n          onResponse,\n          onUpdate({ message, data, replaceLastMessage }) {\n            mutateStatus(\"streaming\");\n            throttledMutate(\n              [\n                ...replaceLastMessage ? chatMessages.slice(0, chatMessages.length - 1) : chatMessages,\n                message\n              ],\n              false\n            );\n            if (data == null ? void 0 : data.length) {\n              throttledMutateStreamData(\n                [...existingData != null ? existingData : [], ...data],\n                false\n              );\n            }\n          },\n          onToolCall,\n          onFinish,\n          generateId: generateId2,\n          fetch: fetch2,\n          lastMessage: chatMessages[chatMessages.length - 1],\n          requestType\n        });\n        abortControllerRef.current = null;\n        mutateStatus(\"ready\");\n      } catch (err) {\n        if (err.name === \"AbortError\") {\n          abortControllerRef.current = null;\n          mutateStatus(\"ready\");\n          return null;\n        }\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n        setError(err);\n        mutateStatus(\"error\");\n      }\n      const messages2 = messagesRef.current;\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.shouldResubmitMessages)({\n        originalMaxToolInvocationStep: maxStep,\n        originalMessageCount: messageCount,\n        maxSteps,\n        messages: messages2\n      })) {\n        await triggerRequest({ messages: messages2 });\n      }\n    },\n    [\n      mutate,\n      mutateStatus,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamDataRef,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxSteps,\n      messagesRef,\n      abortControllerRef,\n      generateId2,\n      fetch2,\n      keepLastMessageOnError,\n      throttleWaitMs,\n      chatId\n    ]\n  );\n  const append = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (message, {\n      data,\n      headers: headers2,\n      body: body2,\n      experimental_attachments = message.experimental_attachments\n    } = {}) => {\n      var _a, _b;\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        ...message,\n        id: (_a = message.id) != null ? _a : generateId2(),\n        createdAt: (_b = message.createdAt) != null ? _b : /* @__PURE__ */ new Date(),\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.getMessageParts)(message)\n      });\n      return triggerRequest({ messages: messages2, headers: headers2, body: body2, data });\n    },\n    [triggerRequest, generateId2]\n  );\n  const reload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async ({ data, headers: headers2, body: body2 } = {}) => {\n      const messages2 = messagesRef.current;\n      if (messages2.length === 0) {\n        return null;\n      }\n      const lastMessage = messages2[messages2.length - 1];\n      return triggerRequest({\n        messages: lastMessage.role === \"assistant\" ? messages2.slice(0, -1) : messages2,\n        headers: headers2,\n        body: body2,\n        data\n      });\n    },\n    [triggerRequest]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const experimental_resume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {\n    const messages2 = messagesRef.current;\n    triggerRequest({ messages: messages2 }, \"resume\");\n  }, [triggerRequest]);\n  const setMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messages2) => {\n      if (typeof messages2 === \"function\") {\n        messages2 = messages2(messagesRef.current);\n      }\n      const messagesWithParts = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(messages2);\n      mutate(messagesWithParts, false);\n      messagesRef.current = messagesWithParts;\n    },\n    [mutate]\n  );\n  const setData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (data) => {\n      if (typeof data === \"function\") {\n        data = data(streamDataRef.current);\n      }\n      mutateStreamData(data, false);\n      streamDataRef.current = data;\n    },\n    [mutateStreamData]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (event, options = {}, metadata) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      if (!input && !options.allowEmptySubmit)\n        return;\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata\n        };\n      }\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        options.experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        id: generateId2(),\n        createdAt: /* @__PURE__ */ new Date(),\n        role: \"user\",\n        content: input,\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: [{ type: \"text\", text: input }]\n      });\n      const chatRequest = {\n        messages: messages2,\n        headers: options.headers,\n        body: options.body,\n        data: options.data\n      };\n      triggerRequest(chatRequest);\n      setInput(\"\");\n    },\n    [input, generateId2, triggerRequest]\n  );\n  const handleInputChange = (e) => {\n    setInput(e.target.value);\n  };\n  const addToolResult = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    ({ toolCallId, result }) => {\n      const currentMessages = messagesRef.current;\n      (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.updateToolCallResult)({\n        messages: currentMessages,\n        toolCallId,\n        toolResult: result\n      });\n      mutate(\n        [\n          ...currentMessages.slice(0, currentMessages.length - 1),\n          { ...currentMessages[currentMessages.length - 1] }\n        ],\n        false\n      );\n      if (status === \"submitted\" || status === \"streaming\") {\n        return;\n      }\n      const lastMessage = currentMessages[currentMessages.length - 1];\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isAssistantMessageWithCompletedToolCalls)(lastMessage)) {\n        triggerRequest({ messages: currentMessages });\n      }\n    },\n    [mutate, status, triggerRequest]\n  );\n  return {\n    messages: messages != null ? messages : [],\n    id: chatId,\n    setMessages,\n    data: streamData,\n    setData,\n    error,\n    append,\n    reload,\n    stop,\n    experimental_resume,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading: status === \"submitted\" || status === \"streaming\",\n    status,\n    addToolResult\n  };\n}\n\n// src/use-completion.ts\n\n\n\nfunction useCompletion({\n  api = \"/api/completion\",\n  id,\n  initialCompletion = \"\",\n  initialInput = \"\",\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  fetch: fetch2,\n  onResponse,\n  onFinish,\n  onError,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id || hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([api, completionId], null, {\n    fallbackData: initialCompletion\n  });\n  const { data: isLoading = false, mutate: mutateLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [completionId, \"loading\"],\n    null\n  );\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([completionId, \"streamData\"], null);\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const completion = data;\n  const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callCompletionApi)({\n      api,\n      prompt,\n      credentials: extraMetadataRef.current.credentials,\n      headers: { ...extraMetadataRef.current.headers, ...options == null ? void 0 : options.headers },\n      body: {\n        ...extraMetadataRef.current.body,\n        ...options == null ? void 0 : options.body\n      },\n      streamProtocol,\n      fetch: fetch2,\n      // throttle streamed ui updates:\n      setCompletion: throttle(\n        (completion2) => mutate(completion2, false),\n        throttleWaitMs\n      ),\n      onData: throttle(\n        (data2) => mutateStreamData([...streamData != null ? streamData : [], ...data2 != null ? data2 : []], false),\n        throttleWaitMs\n      ),\n      setLoading: mutateLoading,\n      setError,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError\n    }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch2,\n      mutateStreamData,\n      throttleWaitMs\n    ]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n  const setCompletion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (completion2) => {\n      mutate(completion2, false);\n    },\n    [mutate]\n  );\n  const complete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      return input ? complete(input) : void 0;\n    },\n    [input, complete]\n  );\n  const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      setInput(e.target.value);\n    },\n    [setInput]\n  );\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData\n  };\n}\n\n// src/use-object.ts\n\n\n\n\nvar getOriginalFetch2 = () => fetch;\nfunction useObject({\n  api,\n  id,\n  schema,\n  // required, in the future we will use it for validation\n  initialValue,\n  fetch: fetch2,\n  onError,\n  onFinish,\n  headers,\n  credentials\n}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id != null ? id : hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue }\n  );\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    var _a;\n    try {\n      (_a = abortControllerRef.current) == null ? void 0 : _a.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const submit = async (input) => {\n    var _a;\n    try {\n      mutate(void 0);\n      setIsLoading(true);\n      setError(void 0);\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch2();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          ...headers\n        },\n        credentials,\n        signal: abortController.signal,\n        body: JSON.stringify(input)\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_a = await response.text()) != null ? _a : \"Failed to fetch the response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      let accumulatedText = \"\";\n      let latestObject = void 0;\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream({\n          write(chunk) {\n            accumulatedText += chunk;\n            const { value } = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.parsePartialJson)(accumulatedText);\n            const currentObject = value;\n            if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestObject, currentObject)) {\n              latestObject = currentObject;\n              mutate(currentObject);\n            }\n          },\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n            if (onFinish != null) {\n              const validationResult = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.safeValidateTypes)({\n                value: latestObject,\n                schema: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.asSchema)(schema)\n              });\n              onFinish(\n                validationResult.success ? { object: validationResult.value, error: void 0 } : { object: void 0, error: validationResult.error }\n              );\n            }\n          }\n        })\n      );\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2)) {\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setIsLoading(false);\n      setError(error2 instanceof Error ? error2 : new Error(String(error2)));\n    }\n  };\n  return {\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop\n  };\n}\nvar experimental_useObject = useObject;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGFpLXNkay9yZWFjdC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTtBQUNzRDtBQUk1QjtBQUM0QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsa0NBQWtDLCtDQUFRO0FBQzFDLDRCQUE0QiwrQ0FBUTtBQUNwQyxnREFBZ0QsK0NBQVE7QUFDeEQ7QUFDQTtBQUNBLDhCQUE4QiwrQ0FBUTtBQUN0Qyw0QkFBNEIsK0NBQVE7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLDZDQUFNO0FBQ25DLGVBQWUsa0RBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELDREQUFVO0FBQzNEO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdEQUFnRDtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksd0VBQXNCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCw0REFBVTtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ04sVUFBVSw4REFBWTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEseUNBQXlDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFXMEI7QUFDc0c7QUFDdkc7O0FBRXpCO0FBQzBDO0FBQzFDO0FBQ0EsMEJBQTBCLHVDQUFnQjtBQUMxQzs7QUFFQTtBQUNtRDtBQUNNO0FBQ3pEO0FBQ0EsNEJBQTRCLCtDQUFTO0FBQ3JDLEVBQUUsZ0RBQVM7QUFDWCxTQUFTLGlFQUFlO0FBQ3hCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHdEQUFjO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOLG1CQUFtQiwrQ0FBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsOENBQU87QUFDMUMsVUFBVSxrRUFBZ0I7QUFDMUI7QUFDQTtBQUNBLFVBQVUseUJBQXlCLEVBQUUsK0NBQU07QUFDM0M7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLHNCQUFzQiw2Q0FBTztBQUM3QixFQUFFLGdEQUFVO0FBQ1o7QUFDQSxHQUFHO0FBQ0gsVUFBVSw2Q0FBNkMsRUFBRSwrQ0FBTTtBQUMvRCx3QkFBd0IsNkNBQU87QUFDL0IsRUFBRSxnREFBVTtBQUNaO0FBQ0EsR0FBRztBQUNILFVBQVUsK0NBQStDLEVBQUUsK0NBQU07QUFDakUsVUFBVSx5Q0FBeUMsRUFBRSwrQ0FBTTtBQUMzRCw2QkFBNkIsNkNBQU87QUFDcEMsMkJBQTJCLDZDQUFPO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFVO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCx5QkFBeUIsa0RBQVk7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsa0VBQWdCO0FBQzNDO0FBQ0Esc0JBQXNCLDhFQUE0QjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2Isb0NBQW9DLE1BQU07QUFDMUMsMkNBQTJDLGFBQWE7QUFDeEQsK0NBQStDLGlCQUFpQjtBQUNoRSxxQ0FBcUM7QUFDckMsV0FBVztBQUNYO0FBQ0E7QUFDQSxjQUFjLDZEQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLHFCQUFxQixtQ0FBbUM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHdFQUFzQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCwrQkFBK0IscUJBQXFCO0FBQ3BEO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLGtEQUFZO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLElBQUk7QUFDVjtBQUNBLDBDQUEwQyw4RUFBNEI7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGlFQUFlO0FBQzlCLE9BQU87QUFDUCw4QkFBOEIsMkRBQTJEO0FBQ3pGLEtBQUs7QUFDTDtBQUNBO0FBQ0EsaUJBQWlCLGtEQUFZO0FBQzdCLGFBQWEsdUNBQXVDLElBQUk7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQSxlQUFlLGtEQUFZO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILDhCQUE4QixrREFBWTtBQUMxQztBQUNBLHFCQUFxQixxQkFBcUI7QUFDMUMsR0FBRztBQUNILHNCQUFzQixrREFBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxrRUFBZ0I7QUFDaEQ7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0Esa0JBQWtCLGtEQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsNEJBQTRCLCtDQUFTO0FBQ3JDLHVCQUF1QixrREFBWTtBQUNuQyw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsOEVBQTRCO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMkJBQTJCO0FBQzdDLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isa0RBQVk7QUFDcEMsT0FBTyxvQkFBb0I7QUFDM0I7QUFDQSxNQUFNLHNFQUFvQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSwwRkFBd0M7QUFDbEQseUJBQXlCLDJCQUEyQjtBQUNwRDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUcwQjtBQUNvRztBQUNwRztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ04saUJBQWlCLDRDQUFLO0FBQ3RCO0FBQ0EsVUFBVSxlQUFlLEVBQUUsK0NBQU87QUFDbEM7QUFDQSxHQUFHO0FBQ0gsVUFBVSxpREFBaUQsRUFBRSwrQ0FBTztBQUNwRTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDZDQUE2QyxFQUFFLCtDQUFPO0FBQ2hFLDRCQUE0QiwrQ0FBUztBQUNyQztBQUNBLGdEQUFnRCwrQ0FBUztBQUN6RCwyQkFBMkIsNkNBQU87QUFDbEM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVU7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHlCQUF5QixrREFBWTtBQUNyQywrQkFBK0IsbUVBQWlCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixvRkFBb0Y7QUFDckc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsd0JBQXdCLGtEQUFZO0FBQ3BDO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLG1CQUFtQixrREFBWTtBQUMvQjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSw0QkFBNEIsK0NBQVM7QUFDckMsdUJBQXVCLGtEQUFZO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSw0QkFBNEIsa0RBQVk7QUFDeEM7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUlnQztBQUtOO0FBQ3FGO0FBQ3JGO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxpQkFBaUIsNENBQU07QUFDdkI7QUFDQSxVQUFVLGVBQWUsRUFBRSwrQ0FBTztBQUNsQztBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsNEJBQTRCLCtDQUFTO0FBQ3JDLG9DQUFvQywrQ0FBUztBQUM3Qyw2QkFBNkIsNkNBQU87QUFDcEMsZUFBZSxrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFFBQVEsRUFBRSxrRUFBZ0I7QUFDOUM7QUFDQSxpQkFBaUIsaUVBQWdCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxtRUFBaUI7QUFDeEQ7QUFDQSx3QkFBd0IsMERBQVE7QUFDaEMsZUFBZTtBQUNmO0FBQ0EsNkNBQTZDLGdEQUFnRCxJQUFJO0FBQ2pHO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLE1BQU07QUFDTixVQUFVLDhEQUFhO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQU1FO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcQGFpLXNka1xccmVhY3RcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXNlLWFzc2lzdGFudC50c1xuaW1wb3J0IHsgaXNBYm9ydEVycm9yIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXItdXRpbHNcIjtcbmltcG9ydCB7XG4gIGdlbmVyYXRlSWQsXG4gIHByb2Nlc3NBc3Npc3RhbnRTdHJlYW1cbn0gZnJvbSBcIkBhaS1zZGsvdWktdXRpbHNcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG52YXIgZ2V0T3JpZ2luYWxGZXRjaCA9ICgpID0+IGZldGNoO1xuZnVuY3Rpb24gdXNlQXNzaXN0YW50KHtcbiAgYXBpLFxuICB0aHJlYWRJZDogdGhyZWFkSWRQYXJhbSxcbiAgY3JlZGVudGlhbHMsXG4gIGhlYWRlcnMsXG4gIGJvZHksXG4gIG9uRXJyb3IsXG4gIGZldGNoOiBmZXRjaDJcbn0pIHtcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFtpbnB1dCwgc2V0SW5wdXRdID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtjdXJyZW50VGhyZWFkSWQsIHNldEN1cnJlbnRUaHJlYWRJZF0gPSB1c2VTdGF0ZShcbiAgICB2b2lkIDBcbiAgKTtcbiAgY29uc3QgW3N0YXR1cywgc2V0U3RhdHVzXSA9IHVzZVN0YXRlKFwiYXdhaXRpbmdfbWVzc2FnZVwiKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZSh2b2lkIDApO1xuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChldmVudCkgPT4ge1xuICAgIHNldElucHV0KGV2ZW50LnRhcmdldC52YWx1ZSk7XG4gIH07XG4gIGNvbnN0IGFib3J0Q29udHJvbGxlclJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3Qgc3RvcCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQpIHtcbiAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LmFib3J0KCk7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICB9LCBbXSk7XG4gIGNvbnN0IGFwcGVuZCA9IGFzeW5jIChtZXNzYWdlLCByZXF1ZXN0T3B0aW9ucykgPT4ge1xuICAgIHZhciBfYSwgX2I7XG4gICAgc2V0U3RhdHVzKFwiaW5fcHJvZ3Jlc3NcIik7XG4gICAgc2V0TWVzc2FnZXMoKG1lc3NhZ2VzMikgPT4ge1xuICAgICAgdmFyIF9hMjtcbiAgICAgIHJldHVybiBbXG4gICAgICAgIC4uLm1lc3NhZ2VzMixcbiAgICAgICAge1xuICAgICAgICAgIC4uLm1lc3NhZ2UsXG4gICAgICAgICAgaWQ6IChfYTIgPSBtZXNzYWdlLmlkKSAhPSBudWxsID8gX2EyIDogZ2VuZXJhdGVJZCgpXG4gICAgICAgIH1cbiAgICAgIF07XG4gICAgfSk7XG4gICAgc2V0SW5wdXQoXCJcIik7XG4gICAgY29uc3QgYWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgIHRyeSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCA9IGFib3J0Q29udHJvbGxlcjtcbiAgICAgIGNvbnN0IGFjdHVhbEZldGNoID0gZmV0Y2gyICE9IG51bGwgPyBmZXRjaDIgOiBnZXRPcmlnaW5hbEZldGNoKCk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFjdHVhbEZldGNoKGFwaSwge1xuICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgICBjcmVkZW50aWFscyxcbiAgICAgICAgc2lnbmFsOiBhYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgICAgICBoZWFkZXJzOiB7IFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLCAuLi5oZWFkZXJzIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAuLi5ib2R5LFxuICAgICAgICAgIC8vIGFsd2F5cyB1c2UgdXNlci1wcm92aWRlZCB0aHJlYWRJZCB3aGVuIGF2YWlsYWJsZTpcbiAgICAgICAgICB0aHJlYWRJZDogKF9hID0gdGhyZWFkSWRQYXJhbSAhPSBudWxsID8gdGhyZWFkSWRQYXJhbSA6IGN1cnJlbnRUaHJlYWRJZCkgIT0gbnVsbCA/IF9hIDogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiBtZXNzYWdlLmNvbnRlbnQsXG4gICAgICAgICAgLy8gb3B0aW9uYWwgcmVxdWVzdCBkYXRhOlxuICAgICAgICAgIGRhdGE6IHJlcXVlc3RPcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiByZXF1ZXN0T3B0aW9ucy5kYXRhXG4gICAgICAgIH0pXG4gICAgICB9KTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIChfYiA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKSkgIT0gbnVsbCA/IF9iIDogXCJGYWlsZWQgdG8gZmV0Y2ggdGhlIGFzc2lzdGFudCByZXNwb25zZS5cIlxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgaWYgKHJlc3BvbnNlLmJvZHkgPT0gbnVsbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGUgcmVzcG9uc2UgYm9keSBpcyBlbXB0eS5cIik7XG4gICAgICB9XG4gICAgICBhd2FpdCBwcm9jZXNzQXNzaXN0YW50U3RyZWFtKHtcbiAgICAgICAgc3RyZWFtOiByZXNwb25zZS5ib2R5LFxuICAgICAgICBvbkFzc2lzdGFudE1lc3NhZ2VQYXJ0KHZhbHVlKSB7XG4gICAgICAgICAgc2V0TWVzc2FnZXMoKG1lc3NhZ2VzMikgPT4gW1xuICAgICAgICAgICAgLi4ubWVzc2FnZXMyLFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBpZDogdmFsdWUuaWQsXG4gICAgICAgICAgICAgIHJvbGU6IHZhbHVlLnJvbGUsXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IHZhbHVlLmNvbnRlbnRbMF0udGV4dC52YWx1ZSxcbiAgICAgICAgICAgICAgcGFydHM6IFtdXG4gICAgICAgICAgICB9XG4gICAgICAgICAgXSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uVGV4dFBhcnQodmFsdWUpIHtcbiAgICAgICAgICBzZXRNZXNzYWdlcygobWVzc2FnZXMyKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBsYXN0TWVzc2FnZSA9IG1lc3NhZ2VzMlttZXNzYWdlczIubGVuZ3RoIC0gMV07XG4gICAgICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgICAuLi5tZXNzYWdlczIuc2xpY2UoMCwgbWVzc2FnZXMyLmxlbmd0aCAtIDEpLFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgaWQ6IGxhc3RNZXNzYWdlLmlkLFxuICAgICAgICAgICAgICAgIHJvbGU6IGxhc3RNZXNzYWdlLnJvbGUsXG4gICAgICAgICAgICAgICAgY29udGVudDogbGFzdE1lc3NhZ2UuY29udGVudCArIHZhbHVlLFxuICAgICAgICAgICAgICAgIHBhcnRzOiBsYXN0TWVzc2FnZS5wYXJ0c1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICBdO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICBvbkFzc2lzdGFudENvbnRyb2xEYXRhUGFydCh2YWx1ZSkge1xuICAgICAgICAgIHNldEN1cnJlbnRUaHJlYWRJZCh2YWx1ZS50aHJlYWRJZCk7XG4gICAgICAgICAgc2V0TWVzc2FnZXMoKG1lc3NhZ2VzMikgPT4ge1xuICAgICAgICAgICAgY29uc3QgbGFzdE1lc3NhZ2UgPSBtZXNzYWdlczJbbWVzc2FnZXMyLmxlbmd0aCAtIDFdO1xuICAgICAgICAgICAgbGFzdE1lc3NhZ2UuaWQgPSB2YWx1ZS5tZXNzYWdlSWQ7XG4gICAgICAgICAgICByZXR1cm4gWy4uLm1lc3NhZ2VzMi5zbGljZSgwLCBtZXNzYWdlczIubGVuZ3RoIC0gMSksIGxhc3RNZXNzYWdlXTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgb25EYXRhTWVzc2FnZVBhcnQodmFsdWUpIHtcbiAgICAgICAgICBzZXRNZXNzYWdlcygobWVzc2FnZXMyKSA9PiB7XG4gICAgICAgICAgICB2YXIgX2EyO1xuICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgLi4ubWVzc2FnZXMyLFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgaWQ6IChfYTIgPSB2YWx1ZS5pZCkgIT0gbnVsbCA/IF9hMiA6IGdlbmVyYXRlSWQoKSxcbiAgICAgICAgICAgICAgICByb2xlOiBcImRhdGFcIixcbiAgICAgICAgICAgICAgICBjb250ZW50OiBcIlwiLFxuICAgICAgICAgICAgICAgIGRhdGE6IHZhbHVlLmRhdGEsXG4gICAgICAgICAgICAgICAgcGFydHM6IFtdXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF07XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uRXJyb3JQYXJ0KHZhbHVlKSB7XG4gICAgICAgICAgc2V0RXJyb3IobmV3IEVycm9yKHZhbHVlKSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yMikge1xuICAgICAgaWYgKGlzQWJvcnRFcnJvcihlcnJvcjIpICYmIGFib3J0Q29udHJvbGxlci5zaWduYWwuYWJvcnRlZCkge1xuICAgICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmIChvbkVycm9yICYmIGVycm9yMiBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIG9uRXJyb3IoZXJyb3IyKTtcbiAgICAgIH1cbiAgICAgIHNldEVycm9yKGVycm9yMik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgIHNldFN0YXR1cyhcImF3YWl0aW5nX21lc3NhZ2VcIik7XG4gICAgfVxuICB9O1xuICBjb25zdCBzdWJtaXRNZXNzYWdlID0gYXN5bmMgKGV2ZW50LCByZXF1ZXN0T3B0aW9ucykgPT4ge1xuICAgIHZhciBfYTtcbiAgICAoX2EgPSBldmVudCA9PSBudWxsID8gdm9pZCAwIDogZXZlbnQucHJldmVudERlZmF1bHQpID09IG51bGwgPyB2b2lkIDAgOiBfYS5jYWxsKGV2ZW50KTtcbiAgICBpZiAoaW5wdXQgPT09IFwiXCIpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgYXBwZW5kKHsgcm9sZTogXCJ1c2VyXCIsIGNvbnRlbnQ6IGlucHV0LCBwYXJ0czogW10gfSwgcmVxdWVzdE9wdGlvbnMpO1xuICB9O1xuICBjb25zdCBzZXRUaHJlYWRJZCA9ICh0aHJlYWRJZCkgPT4ge1xuICAgIHNldEN1cnJlbnRUaHJlYWRJZCh0aHJlYWRJZCk7XG4gICAgc2V0TWVzc2FnZXMoW10pO1xuICB9O1xuICByZXR1cm4ge1xuICAgIGFwcGVuZCxcbiAgICBtZXNzYWdlcyxcbiAgICBzZXRNZXNzYWdlcyxcbiAgICB0aHJlYWRJZDogY3VycmVudFRocmVhZElkLFxuICAgIHNldFRocmVhZElkLFxuICAgIGlucHV0LFxuICAgIHNldElucHV0LFxuICAgIGhhbmRsZUlucHV0Q2hhbmdlLFxuICAgIHN1Ym1pdE1lc3NhZ2UsXG4gICAgc3RhdHVzLFxuICAgIGVycm9yLFxuICAgIHN0b3BcbiAgfTtcbn1cblxuLy8gc3JjL3VzZS1jaGF0LnRzXG5pbXBvcnQge1xuICBjYWxsQ2hhdEFwaSxcbiAgZXh0cmFjdE1heFRvb2xJbnZvY2F0aW9uU3RlcCxcbiAgZmlsbE1lc3NhZ2VQYXJ0cyxcbiAgZ2VuZXJhdGVJZCBhcyBnZW5lcmF0ZUlkRnVuYyxcbiAgZ2V0TWVzc2FnZVBhcnRzLFxuICBpc0Fzc2lzdGFudE1lc3NhZ2VXaXRoQ29tcGxldGVkVG9vbENhbGxzLFxuICBwcmVwYXJlQXR0YWNobWVudHNGb3JSZXF1ZXN0LFxuICBzaG91bGRSZXN1Ym1pdE1lc3NhZ2VzLFxuICB1cGRhdGVUb29sQ2FsbFJlc3VsdFxufSBmcm9tIFwiQGFpLXNkay91aS11dGlsc1wiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2sgYXMgdXNlQ2FsbGJhY2syLCB1c2VFZmZlY3QgYXMgdXNlRWZmZWN0MiwgdXNlTWVtbywgdXNlUmVmIGFzIHVzZVJlZjIsIHVzZVN0YXRlIGFzIHVzZVN0YXRlMyB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHVzZVNXUiBmcm9tIFwic3dyXCI7XG5cbi8vIHNyYy90aHJvdHRsZS50c1xuaW1wb3J0IHRocm90dGxlRnVuY3Rpb24gZnJvbSBcInRocm90dGxlaXRcIjtcbmZ1bmN0aW9uIHRocm90dGxlKGZuLCB3YWl0TXMpIHtcbiAgcmV0dXJuIHdhaXRNcyAhPSBudWxsID8gdGhyb3R0bGVGdW5jdGlvbihmbiwgd2FpdE1zKSA6IGZuO1xufVxuXG4vLyBzcmMvdXRpbC91c2Utc3RhYmxlLXZhbHVlLnRzXG5pbXBvcnQgeyBpc0RlZXBFcXVhbERhdGEgfSBmcm9tIFwiQGFpLXNkay91aS11dGlsc1wiO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSBhcyB1c2VTdGF0ZTIgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZVN0YWJsZVZhbHVlKGxhdGVzdFZhbHVlKSB7XG4gIGNvbnN0IFt2YWx1ZSwgc2V0VmFsdWVdID0gdXNlU3RhdGUyKGxhdGVzdFZhbHVlKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzRGVlcEVxdWFsRGF0YShsYXRlc3RWYWx1ZSwgdmFsdWUpKSB7XG4gICAgICBzZXRWYWx1ZShsYXRlc3RWYWx1ZSk7XG4gICAgfVxuICB9LCBbbGF0ZXN0VmFsdWUsIHZhbHVlXSk7XG4gIHJldHVybiB2YWx1ZTtcbn1cblxuLy8gc3JjL3VzZS1jaGF0LnRzXG5mdW5jdGlvbiB1c2VDaGF0KHtcbiAgYXBpID0gXCIvYXBpL2NoYXRcIixcbiAgaWQsXG4gIGluaXRpYWxNZXNzYWdlcyxcbiAgaW5pdGlhbElucHV0ID0gXCJcIixcbiAgc2VuZEV4dHJhTWVzc2FnZUZpZWxkcyxcbiAgb25Ub29sQ2FsbCxcbiAgZXhwZXJpbWVudGFsX3ByZXBhcmVSZXF1ZXN0Qm9keSxcbiAgbWF4U3RlcHMgPSAxLFxuICBzdHJlYW1Qcm90b2NvbCA9IFwiZGF0YVwiLFxuICBvblJlc3BvbnNlLFxuICBvbkZpbmlzaCxcbiAgb25FcnJvcixcbiAgY3JlZGVudGlhbHMsXG4gIGhlYWRlcnMsXG4gIGJvZHksXG4gIGdlbmVyYXRlSWQ6IGdlbmVyYXRlSWQyID0gZ2VuZXJhdGVJZEZ1bmMsXG4gIGZldGNoOiBmZXRjaDIsXG4gIGtlZXBMYXN0TWVzc2FnZU9uRXJyb3IgPSB0cnVlLFxuICBleHBlcmltZW50YWxfdGhyb3R0bGU6IHRocm90dGxlV2FpdE1zXG59ID0ge30pIHtcbiAgY29uc3QgW2hvb2tJZF0gPSB1c2VTdGF0ZTMoZ2VuZXJhdGVJZDIpO1xuICBjb25zdCBjaGF0SWQgPSBpZCAhPSBudWxsID8gaWQgOiBob29rSWQ7XG4gIGNvbnN0IGNoYXRLZXkgPSB0eXBlb2YgYXBpID09PSBcInN0cmluZ1wiID8gW2FwaSwgY2hhdElkXSA6IGNoYXRJZDtcbiAgY29uc3Qgc3RhYmxlSW5pdGlhbE1lc3NhZ2VzID0gdXNlU3RhYmxlVmFsdWUoaW5pdGlhbE1lc3NhZ2VzICE9IG51bGwgPyBpbml0aWFsTWVzc2FnZXMgOiBbXSk7XG4gIGNvbnN0IHByb2Nlc3NlZEluaXRpYWxNZXNzYWdlcyA9IHVzZU1lbW8oXG4gICAgKCkgPT4gZmlsbE1lc3NhZ2VQYXJ0cyhzdGFibGVJbml0aWFsTWVzc2FnZXMpLFxuICAgIFtzdGFibGVJbml0aWFsTWVzc2FnZXNdXG4gICk7XG4gIGNvbnN0IHsgZGF0YTogbWVzc2FnZXMsIG11dGF0ZSB9ID0gdXNlU1dSKFxuICAgIFtjaGF0S2V5LCBcIm1lc3NhZ2VzXCJdLFxuICAgIG51bGwsXG4gICAgeyBmYWxsYmFja0RhdGE6IHByb2Nlc3NlZEluaXRpYWxNZXNzYWdlcyB9XG4gICk7XG4gIGNvbnN0IG1lc3NhZ2VzUmVmID0gdXNlUmVmMihtZXNzYWdlcyB8fCBbXSk7XG4gIHVzZUVmZmVjdDIoKCkgPT4ge1xuICAgIG1lc3NhZ2VzUmVmLmN1cnJlbnQgPSBtZXNzYWdlcyB8fCBbXTtcbiAgfSwgW21lc3NhZ2VzXSk7XG4gIGNvbnN0IHsgZGF0YTogc3RyZWFtRGF0YSwgbXV0YXRlOiBtdXRhdGVTdHJlYW1EYXRhIH0gPSB1c2VTV1IoW2NoYXRLZXksIFwic3RyZWFtRGF0YVwiXSwgbnVsbCk7XG4gIGNvbnN0IHN0cmVhbURhdGFSZWYgPSB1c2VSZWYyKHN0cmVhbURhdGEpO1xuICB1c2VFZmZlY3QyKCgpID0+IHtcbiAgICBzdHJlYW1EYXRhUmVmLmN1cnJlbnQgPSBzdHJlYW1EYXRhO1xuICB9LCBbc3RyZWFtRGF0YV0pO1xuICBjb25zdCB7IGRhdGE6IHN0YXR1cyA9IFwicmVhZHlcIiwgbXV0YXRlOiBtdXRhdGVTdGF0dXMgfSA9IHVzZVNXUihbY2hhdEtleSwgXCJzdGF0dXNcIl0sIG51bGwpO1xuICBjb25zdCB7IGRhdGE6IGVycm9yID0gdm9pZCAwLCBtdXRhdGU6IHNldEVycm9yIH0gPSB1c2VTV1IoW2NoYXRLZXksIFwiZXJyb3JcIl0sIG51bGwpO1xuICBjb25zdCBhYm9ydENvbnRyb2xsZXJSZWYgPSB1c2VSZWYyKG51bGwpO1xuICBjb25zdCBleHRyYU1ldGFkYXRhUmVmID0gdXNlUmVmMih7XG4gICAgY3JlZGVudGlhbHMsXG4gICAgaGVhZGVycyxcbiAgICBib2R5XG4gIH0pO1xuICB1c2VFZmZlY3QyKCgpID0+IHtcbiAgICBleHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQgPSB7XG4gICAgICBjcmVkZW50aWFscyxcbiAgICAgIGhlYWRlcnMsXG4gICAgICBib2R5XG4gICAgfTtcbiAgfSwgW2NyZWRlbnRpYWxzLCBoZWFkZXJzLCBib2R5XSk7XG4gIGNvbnN0IHRyaWdnZXJSZXF1ZXN0ID0gdXNlQ2FsbGJhY2syKFxuICAgIGFzeW5jIChjaGF0UmVxdWVzdCwgcmVxdWVzdFR5cGUgPSBcImdlbmVyYXRlXCIpID0+IHtcbiAgICAgIHZhciBfYSwgX2I7XG4gICAgICBtdXRhdGVTdGF0dXMoXCJzdWJtaXR0ZWRcIik7XG4gICAgICBzZXRFcnJvcih2b2lkIDApO1xuICAgICAgY29uc3QgY2hhdE1lc3NhZ2VzID0gZmlsbE1lc3NhZ2VQYXJ0cyhjaGF0UmVxdWVzdC5tZXNzYWdlcyk7XG4gICAgICBjb25zdCBtZXNzYWdlQ291bnQgPSBjaGF0TWVzc2FnZXMubGVuZ3RoO1xuICAgICAgY29uc3QgbWF4U3RlcCA9IGV4dHJhY3RNYXhUb29sSW52b2NhdGlvblN0ZXAoXG4gICAgICAgIChfYSA9IGNoYXRNZXNzYWdlc1tjaGF0TWVzc2FnZXMubGVuZ3RoIC0gMV0pID09IG51bGwgPyB2b2lkIDAgOiBfYS50b29sSW52b2NhdGlvbnNcbiAgICAgICk7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBhYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gYWJvcnRDb250cm9sbGVyO1xuICAgICAgICBjb25zdCB0aHJvdHRsZWRNdXRhdGUgPSB0aHJvdHRsZShtdXRhdGUsIHRocm90dGxlV2FpdE1zKTtcbiAgICAgICAgY29uc3QgdGhyb3R0bGVkTXV0YXRlU3RyZWFtRGF0YSA9IHRocm90dGxlKFxuICAgICAgICAgIG11dGF0ZVN0cmVhbURhdGEsXG4gICAgICAgICAgdGhyb3R0bGVXYWl0TXNcbiAgICAgICAgKTtcbiAgICAgICAgY29uc3QgcHJldmlvdXNNZXNzYWdlcyA9IG1lc3NhZ2VzUmVmLmN1cnJlbnQ7XG4gICAgICAgIHRocm90dGxlZE11dGF0ZShjaGF0TWVzc2FnZXMsIGZhbHNlKTtcbiAgICAgICAgY29uc3QgY29uc3RydWN0ZWRNZXNzYWdlc1BheWxvYWQgPSBzZW5kRXh0cmFNZXNzYWdlRmllbGRzID8gY2hhdE1lc3NhZ2VzIDogY2hhdE1lc3NhZ2VzLm1hcChcbiAgICAgICAgICAoe1xuICAgICAgICAgICAgcm9sZSxcbiAgICAgICAgICAgIGNvbnRlbnQsXG4gICAgICAgICAgICBleHBlcmltZW50YWxfYXR0YWNobWVudHMsXG4gICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgYW5ub3RhdGlvbnMsXG4gICAgICAgICAgICB0b29sSW52b2NhdGlvbnMsXG4gICAgICAgICAgICBwYXJ0c1xuICAgICAgICAgIH0pID0+ICh7XG4gICAgICAgICAgICByb2xlLFxuICAgICAgICAgICAgY29udGVudCxcbiAgICAgICAgICAgIC4uLmV4cGVyaW1lbnRhbF9hdHRhY2htZW50cyAhPT0gdm9pZCAwICYmIHtcbiAgICAgICAgICAgICAgZXhwZXJpbWVudGFsX2F0dGFjaG1lbnRzXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgLi4uZGF0YSAhPT0gdm9pZCAwICYmIHsgZGF0YSB9LFxuICAgICAgICAgICAgLi4uYW5ub3RhdGlvbnMgIT09IHZvaWQgMCAmJiB7IGFubm90YXRpb25zIH0sXG4gICAgICAgICAgICAuLi50b29sSW52b2NhdGlvbnMgIT09IHZvaWQgMCAmJiB7IHRvb2xJbnZvY2F0aW9ucyB9LFxuICAgICAgICAgICAgLi4ucGFydHMgIT09IHZvaWQgMCAmJiB7IHBhcnRzIH1cbiAgICAgICAgICB9KVxuICAgICAgICApO1xuICAgICAgICBjb25zdCBleGlzdGluZ0RhdGEgPSBzdHJlYW1EYXRhUmVmLmN1cnJlbnQ7XG4gICAgICAgIGF3YWl0IGNhbGxDaGF0QXBpKHtcbiAgICAgICAgICBhcGksXG4gICAgICAgICAgYm9keTogKF9iID0gZXhwZXJpbWVudGFsX3ByZXBhcmVSZXF1ZXN0Qm9keSA9PSBudWxsID8gdm9pZCAwIDogZXhwZXJpbWVudGFsX3ByZXBhcmVSZXF1ZXN0Qm9keSh7XG4gICAgICAgICAgICBpZDogY2hhdElkLFxuICAgICAgICAgICAgbWVzc2FnZXM6IGNoYXRNZXNzYWdlcyxcbiAgICAgICAgICAgIHJlcXVlc3REYXRhOiBjaGF0UmVxdWVzdC5kYXRhLFxuICAgICAgICAgICAgcmVxdWVzdEJvZHk6IGNoYXRSZXF1ZXN0LmJvZHlcbiAgICAgICAgICB9KSkgIT0gbnVsbCA/IF9iIDoge1xuICAgICAgICAgICAgaWQ6IGNoYXRJZCxcbiAgICAgICAgICAgIG1lc3NhZ2VzOiBjb25zdHJ1Y3RlZE1lc3NhZ2VzUGF5bG9hZCxcbiAgICAgICAgICAgIGRhdGE6IGNoYXRSZXF1ZXN0LmRhdGEsXG4gICAgICAgICAgICAuLi5leHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQuYm9keSxcbiAgICAgICAgICAgIC4uLmNoYXRSZXF1ZXN0LmJvZHlcbiAgICAgICAgICB9LFxuICAgICAgICAgIHN0cmVhbVByb3RvY29sLFxuICAgICAgICAgIGNyZWRlbnRpYWxzOiBleHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQuY3JlZGVudGlhbHMsXG4gICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgLi4uZXh0cmFNZXRhZGF0YVJlZi5jdXJyZW50LmhlYWRlcnMsXG4gICAgICAgICAgICAuLi5jaGF0UmVxdWVzdC5oZWFkZXJzXG4gICAgICAgICAgfSxcbiAgICAgICAgICBhYm9ydENvbnRyb2xsZXI6ICgpID0+IGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LFxuICAgICAgICAgIHJlc3RvcmVNZXNzYWdlc09uRmFpbHVyZSgpIHtcbiAgICAgICAgICAgIGlmICgha2VlcExhc3RNZXNzYWdlT25FcnJvcikge1xuICAgICAgICAgICAgICB0aHJvdHRsZWRNdXRhdGUocHJldmlvdXNNZXNzYWdlcywgZmFsc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgb25SZXNwb25zZSxcbiAgICAgICAgICBvblVwZGF0ZSh7IG1lc3NhZ2UsIGRhdGEsIHJlcGxhY2VMYXN0TWVzc2FnZSB9KSB7XG4gICAgICAgICAgICBtdXRhdGVTdGF0dXMoXCJzdHJlYW1pbmdcIik7XG4gICAgICAgICAgICB0aHJvdHRsZWRNdXRhdGUoXG4gICAgICAgICAgICAgIFtcbiAgICAgICAgICAgICAgICAuLi5yZXBsYWNlTGFzdE1lc3NhZ2UgPyBjaGF0TWVzc2FnZXMuc2xpY2UoMCwgY2hhdE1lc3NhZ2VzLmxlbmd0aCAtIDEpIDogY2hhdE1lc3NhZ2VzLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VcbiAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgZmFsc2VcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICBpZiAoZGF0YSA9PSBudWxsID8gdm9pZCAwIDogZGF0YS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgdGhyb3R0bGVkTXV0YXRlU3RyZWFtRGF0YShcbiAgICAgICAgICAgICAgICBbLi4uZXhpc3RpbmdEYXRhICE9IG51bGwgPyBleGlzdGluZ0RhdGEgOiBbXSwgLi4uZGF0YV0sXG4gICAgICAgICAgICAgICAgZmFsc2VcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LFxuICAgICAgICAgIG9uVG9vbENhbGwsXG4gICAgICAgICAgb25GaW5pc2gsXG4gICAgICAgICAgZ2VuZXJhdGVJZDogZ2VuZXJhdGVJZDIsXG4gICAgICAgICAgZmV0Y2g6IGZldGNoMixcbiAgICAgICAgICBsYXN0TWVzc2FnZTogY2hhdE1lc3NhZ2VzW2NoYXRNZXNzYWdlcy5sZW5ndGggLSAxXSxcbiAgICAgICAgICByZXF1ZXN0VHlwZVxuICAgICAgICB9KTtcbiAgICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICBtdXRhdGVTdGF0dXMoXCJyZWFkeVwiKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICBpZiAoZXJyLm5hbWUgPT09IFwiQWJvcnRFcnJvclwiKSB7XG4gICAgICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICAgIG11dGF0ZVN0YXR1cyhcInJlYWR5XCIpO1xuICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvbkVycm9yICYmIGVyciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgICAgb25FcnJvcihlcnIpO1xuICAgICAgICB9XG4gICAgICAgIHNldEVycm9yKGVycik7XG4gICAgICAgIG11dGF0ZVN0YXR1cyhcImVycm9yXCIpO1xuICAgICAgfVxuICAgICAgY29uc3QgbWVzc2FnZXMyID0gbWVzc2FnZXNSZWYuY3VycmVudDtcbiAgICAgIGlmIChzaG91bGRSZXN1Ym1pdE1lc3NhZ2VzKHtcbiAgICAgICAgb3JpZ2luYWxNYXhUb29sSW52b2NhdGlvblN0ZXA6IG1heFN0ZXAsXG4gICAgICAgIG9yaWdpbmFsTWVzc2FnZUNvdW50OiBtZXNzYWdlQ291bnQsXG4gICAgICAgIG1heFN0ZXBzLFxuICAgICAgICBtZXNzYWdlczogbWVzc2FnZXMyXG4gICAgICB9KSkge1xuICAgICAgICBhd2FpdCB0cmlnZ2VyUmVxdWVzdCh7IG1lc3NhZ2VzOiBtZXNzYWdlczIgfSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBbXG4gICAgICBtdXRhdGUsXG4gICAgICBtdXRhdGVTdGF0dXMsXG4gICAgICBhcGksXG4gICAgICBleHRyYU1ldGFkYXRhUmVmLFxuICAgICAgb25SZXNwb25zZSxcbiAgICAgIG9uRmluaXNoLFxuICAgICAgb25FcnJvcixcbiAgICAgIHNldEVycm9yLFxuICAgICAgbXV0YXRlU3RyZWFtRGF0YSxcbiAgICAgIHN0cmVhbURhdGFSZWYsXG4gICAgICBzdHJlYW1Qcm90b2NvbCxcbiAgICAgIHNlbmRFeHRyYU1lc3NhZ2VGaWVsZHMsXG4gICAgICBleHBlcmltZW50YWxfcHJlcGFyZVJlcXVlc3RCb2R5LFxuICAgICAgb25Ub29sQ2FsbCxcbiAgICAgIG1heFN0ZXBzLFxuICAgICAgbWVzc2FnZXNSZWYsXG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYsXG4gICAgICBnZW5lcmF0ZUlkMixcbiAgICAgIGZldGNoMixcbiAgICAgIGtlZXBMYXN0TWVzc2FnZU9uRXJyb3IsXG4gICAgICB0aHJvdHRsZVdhaXRNcyxcbiAgICAgIGNoYXRJZFxuICAgIF1cbiAgKTtcbiAgY29uc3QgYXBwZW5kID0gdXNlQ2FsbGJhY2syKFxuICAgIGFzeW5jIChtZXNzYWdlLCB7XG4gICAgICBkYXRhLFxuICAgICAgaGVhZGVyczogaGVhZGVyczIsXG4gICAgICBib2R5OiBib2R5MixcbiAgICAgIGV4cGVyaW1lbnRhbF9hdHRhY2htZW50cyA9IG1lc3NhZ2UuZXhwZXJpbWVudGFsX2F0dGFjaG1lbnRzXG4gICAgfSA9IHt9KSA9PiB7XG4gICAgICB2YXIgX2EsIF9iO1xuICAgICAgY29uc3QgYXR0YWNobWVudHNGb3JSZXF1ZXN0ID0gYXdhaXQgcHJlcGFyZUF0dGFjaG1lbnRzRm9yUmVxdWVzdChcbiAgICAgICAgZXhwZXJpbWVudGFsX2F0dGFjaG1lbnRzXG4gICAgICApO1xuICAgICAgY29uc3QgbWVzc2FnZXMyID0gbWVzc2FnZXNSZWYuY3VycmVudC5jb25jYXQoe1xuICAgICAgICAuLi5tZXNzYWdlLFxuICAgICAgICBpZDogKF9hID0gbWVzc2FnZS5pZCkgIT0gbnVsbCA/IF9hIDogZ2VuZXJhdGVJZDIoKSxcbiAgICAgICAgY3JlYXRlZEF0OiAoX2IgPSBtZXNzYWdlLmNyZWF0ZWRBdCkgIT0gbnVsbCA/IF9iIDogLyogQF9fUFVSRV9fICovIG5ldyBEYXRlKCksXG4gICAgICAgIGV4cGVyaW1lbnRhbF9hdHRhY2htZW50czogYXR0YWNobWVudHNGb3JSZXF1ZXN0Lmxlbmd0aCA+IDAgPyBhdHRhY2htZW50c0ZvclJlcXVlc3QgOiB2b2lkIDAsXG4gICAgICAgIHBhcnRzOiBnZXRNZXNzYWdlUGFydHMobWVzc2FnZSlcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHRyaWdnZXJSZXF1ZXN0KHsgbWVzc2FnZXM6IG1lc3NhZ2VzMiwgaGVhZGVyczogaGVhZGVyczIsIGJvZHk6IGJvZHkyLCBkYXRhIH0pO1xuICAgIH0sXG4gICAgW3RyaWdnZXJSZXF1ZXN0LCBnZW5lcmF0ZUlkMl1cbiAgKTtcbiAgY29uc3QgcmVsb2FkID0gdXNlQ2FsbGJhY2syKFxuICAgIGFzeW5jICh7IGRhdGEsIGhlYWRlcnM6IGhlYWRlcnMyLCBib2R5OiBib2R5MiB9ID0ge30pID0+IHtcbiAgICAgIGNvbnN0IG1lc3NhZ2VzMiA9IG1lc3NhZ2VzUmVmLmN1cnJlbnQ7XG4gICAgICBpZiAobWVzc2FnZXMyLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGxhc3RNZXNzYWdlID0gbWVzc2FnZXMyW21lc3NhZ2VzMi5sZW5ndGggLSAxXTtcbiAgICAgIHJldHVybiB0cmlnZ2VyUmVxdWVzdCh7XG4gICAgICAgIG1lc3NhZ2VzOiBsYXN0TWVzc2FnZS5yb2xlID09PSBcImFzc2lzdGFudFwiID8gbWVzc2FnZXMyLnNsaWNlKDAsIC0xKSA6IG1lc3NhZ2VzMixcbiAgICAgICAgaGVhZGVyczogaGVhZGVyczIsXG4gICAgICAgIGJvZHk6IGJvZHkyLFxuICAgICAgICBkYXRhXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFt0cmlnZ2VyUmVxdWVzdF1cbiAgKTtcbiAgY29uc3Qgc3RvcCA9IHVzZUNhbGxiYWNrMigoKSA9PiB7XG4gICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudC5hYm9ydCgpO1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cbiAgfSwgW10pO1xuICBjb25zdCBleHBlcmltZW50YWxfcmVzdW1lID0gdXNlQ2FsbGJhY2syKGFzeW5jICgpID0+IHtcbiAgICBjb25zdCBtZXNzYWdlczIgPSBtZXNzYWdlc1JlZi5jdXJyZW50O1xuICAgIHRyaWdnZXJSZXF1ZXN0KHsgbWVzc2FnZXM6IG1lc3NhZ2VzMiB9LCBcInJlc3VtZVwiKTtcbiAgfSwgW3RyaWdnZXJSZXF1ZXN0XSk7XG4gIGNvbnN0IHNldE1lc3NhZ2VzID0gdXNlQ2FsbGJhY2syKFxuICAgIChtZXNzYWdlczIpID0+IHtcbiAgICAgIGlmICh0eXBlb2YgbWVzc2FnZXMyID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgbWVzc2FnZXMyID0gbWVzc2FnZXMyKG1lc3NhZ2VzUmVmLmN1cnJlbnQpO1xuICAgICAgfVxuICAgICAgY29uc3QgbWVzc2FnZXNXaXRoUGFydHMgPSBmaWxsTWVzc2FnZVBhcnRzKG1lc3NhZ2VzMik7XG4gICAgICBtdXRhdGUobWVzc2FnZXNXaXRoUGFydHMsIGZhbHNlKTtcbiAgICAgIG1lc3NhZ2VzUmVmLmN1cnJlbnQgPSBtZXNzYWdlc1dpdGhQYXJ0cztcbiAgICB9LFxuICAgIFttdXRhdGVdXG4gICk7XG4gIGNvbnN0IHNldERhdGEgPSB1c2VDYWxsYmFjazIoXG4gICAgKGRhdGEpID0+IHtcbiAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIGRhdGEgPSBkYXRhKHN0cmVhbURhdGFSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgICBtdXRhdGVTdHJlYW1EYXRhKGRhdGEsIGZhbHNlKTtcbiAgICAgIHN0cmVhbURhdGFSZWYuY3VycmVudCA9IGRhdGE7XG4gICAgfSxcbiAgICBbbXV0YXRlU3RyZWFtRGF0YV1cbiAgKTtcbiAgY29uc3QgW2lucHV0LCBzZXRJbnB1dF0gPSB1c2VTdGF0ZTMoaW5pdGlhbElucHV0KTtcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gdXNlQ2FsbGJhY2syKFxuICAgIGFzeW5jIChldmVudCwgb3B0aW9ucyA9IHt9LCBtZXRhZGF0YSkgPT4ge1xuICAgICAgdmFyIF9hO1xuICAgICAgKF9hID0gZXZlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IGV2ZW50LnByZXZlbnREZWZhdWx0KSA9PSBudWxsID8gdm9pZCAwIDogX2EuY2FsbChldmVudCk7XG4gICAgICBpZiAoIWlucHV0ICYmICFvcHRpb25zLmFsbG93RW1wdHlTdWJtaXQpXG4gICAgICAgIHJldHVybjtcbiAgICAgIGlmIChtZXRhZGF0YSkge1xuICAgICAgICBleHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQgPSB7XG4gICAgICAgICAgLi4uZXh0cmFNZXRhZGF0YVJlZi5jdXJyZW50LFxuICAgICAgICAgIC4uLm1ldGFkYXRhXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICBjb25zdCBhdHRhY2htZW50c0ZvclJlcXVlc3QgPSBhd2FpdCBwcmVwYXJlQXR0YWNobWVudHNGb3JSZXF1ZXN0KFxuICAgICAgICBvcHRpb25zLmV4cGVyaW1lbnRhbF9hdHRhY2htZW50c1xuICAgICAgKTtcbiAgICAgIGNvbnN0IG1lc3NhZ2VzMiA9IG1lc3NhZ2VzUmVmLmN1cnJlbnQuY29uY2F0KHtcbiAgICAgICAgaWQ6IGdlbmVyYXRlSWQyKCksXG4gICAgICAgIGNyZWF0ZWRBdDogLyogQF9fUFVSRV9fICovIG5ldyBEYXRlKCksXG4gICAgICAgIHJvbGU6IFwidXNlclwiLFxuICAgICAgICBjb250ZW50OiBpbnB1dCxcbiAgICAgICAgZXhwZXJpbWVudGFsX2F0dGFjaG1lbnRzOiBhdHRhY2htZW50c0ZvclJlcXVlc3QubGVuZ3RoID4gMCA/IGF0dGFjaG1lbnRzRm9yUmVxdWVzdCA6IHZvaWQgMCxcbiAgICAgICAgcGFydHM6IFt7IHR5cGU6IFwidGV4dFwiLCB0ZXh0OiBpbnB1dCB9XVxuICAgICAgfSk7XG4gICAgICBjb25zdCBjaGF0UmVxdWVzdCA9IHtcbiAgICAgICAgbWVzc2FnZXM6IG1lc3NhZ2VzMixcbiAgICAgICAgaGVhZGVyczogb3B0aW9ucy5oZWFkZXJzLFxuICAgICAgICBib2R5OiBvcHRpb25zLmJvZHksXG4gICAgICAgIGRhdGE6IG9wdGlvbnMuZGF0YVxuICAgICAgfTtcbiAgICAgIHRyaWdnZXJSZXF1ZXN0KGNoYXRSZXF1ZXN0KTtcbiAgICAgIHNldElucHV0KFwiXCIpO1xuICAgIH0sXG4gICAgW2lucHV0LCBnZW5lcmF0ZUlkMiwgdHJpZ2dlclJlcXVlc3RdXG4gICk7XG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGUpID0+IHtcbiAgICBzZXRJbnB1dChlLnRhcmdldC52YWx1ZSk7XG4gIH07XG4gIGNvbnN0IGFkZFRvb2xSZXN1bHQgPSB1c2VDYWxsYmFjazIoXG4gICAgKHsgdG9vbENhbGxJZCwgcmVzdWx0IH0pID0+IHtcbiAgICAgIGNvbnN0IGN1cnJlbnRNZXNzYWdlcyA9IG1lc3NhZ2VzUmVmLmN1cnJlbnQ7XG4gICAgICB1cGRhdGVUb29sQ2FsbFJlc3VsdCh7XG4gICAgICAgIG1lc3NhZ2VzOiBjdXJyZW50TWVzc2FnZXMsXG4gICAgICAgIHRvb2xDYWxsSWQsXG4gICAgICAgIHRvb2xSZXN1bHQ6IHJlc3VsdFxuICAgICAgfSk7XG4gICAgICBtdXRhdGUoXG4gICAgICAgIFtcbiAgICAgICAgICAuLi5jdXJyZW50TWVzc2FnZXMuc2xpY2UoMCwgY3VycmVudE1lc3NhZ2VzLmxlbmd0aCAtIDEpLFxuICAgICAgICAgIHsgLi4uY3VycmVudE1lc3NhZ2VzW2N1cnJlbnRNZXNzYWdlcy5sZW5ndGggLSAxXSB9XG4gICAgICAgIF0sXG4gICAgICAgIGZhbHNlXG4gICAgICApO1xuICAgICAgaWYgKHN0YXR1cyA9PT0gXCJzdWJtaXR0ZWRcIiB8fCBzdGF0dXMgPT09IFwic3RyZWFtaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgY29uc3QgbGFzdE1lc3NhZ2UgPSBjdXJyZW50TWVzc2FnZXNbY3VycmVudE1lc3NhZ2VzLmxlbmd0aCAtIDFdO1xuICAgICAgaWYgKGlzQXNzaXN0YW50TWVzc2FnZVdpdGhDb21wbGV0ZWRUb29sQ2FsbHMobGFzdE1lc3NhZ2UpKSB7XG4gICAgICAgIHRyaWdnZXJSZXF1ZXN0KHsgbWVzc2FnZXM6IGN1cnJlbnRNZXNzYWdlcyB9KTtcbiAgICAgIH1cbiAgICB9LFxuICAgIFttdXRhdGUsIHN0YXR1cywgdHJpZ2dlclJlcXVlc3RdXG4gICk7XG4gIHJldHVybiB7XG4gICAgbWVzc2FnZXM6IG1lc3NhZ2VzICE9IG51bGwgPyBtZXNzYWdlcyA6IFtdLFxuICAgIGlkOiBjaGF0SWQsXG4gICAgc2V0TWVzc2FnZXMsXG4gICAgZGF0YTogc3RyZWFtRGF0YSxcbiAgICBzZXREYXRhLFxuICAgIGVycm9yLFxuICAgIGFwcGVuZCxcbiAgICByZWxvYWQsXG4gICAgc3RvcCxcbiAgICBleHBlcmltZW50YWxfcmVzdW1lLFxuICAgIGlucHV0LFxuICAgIHNldElucHV0LFxuICAgIGhhbmRsZUlucHV0Q2hhbmdlLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICBpc0xvYWRpbmc6IHN0YXR1cyA9PT0gXCJzdWJtaXR0ZWRcIiB8fCBzdGF0dXMgPT09IFwic3RyZWFtaW5nXCIsXG4gICAgc3RhdHVzLFxuICAgIGFkZFRvb2xSZXN1bHRcbiAgfTtcbn1cblxuLy8gc3JjL3VzZS1jb21wbGV0aW9uLnRzXG5pbXBvcnQge1xuICBjYWxsQ29tcGxldGlvbkFwaVxufSBmcm9tIFwiQGFpLXNkay91aS11dGlsc1wiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2sgYXMgdXNlQ2FsbGJhY2szLCB1c2VFZmZlY3QgYXMgdXNlRWZmZWN0MywgdXNlSWQsIHVzZVJlZiBhcyB1c2VSZWYzLCB1c2VTdGF0ZSBhcyB1c2VTdGF0ZTQgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB1c2VTV1IyIGZyb20gXCJzd3JcIjtcbmZ1bmN0aW9uIHVzZUNvbXBsZXRpb24oe1xuICBhcGkgPSBcIi9hcGkvY29tcGxldGlvblwiLFxuICBpZCxcbiAgaW5pdGlhbENvbXBsZXRpb24gPSBcIlwiLFxuICBpbml0aWFsSW5wdXQgPSBcIlwiLFxuICBjcmVkZW50aWFscyxcbiAgaGVhZGVycyxcbiAgYm9keSxcbiAgc3RyZWFtUHJvdG9jb2wgPSBcImRhdGFcIixcbiAgZmV0Y2g6IGZldGNoMixcbiAgb25SZXNwb25zZSxcbiAgb25GaW5pc2gsXG4gIG9uRXJyb3IsXG4gIGV4cGVyaW1lbnRhbF90aHJvdHRsZTogdGhyb3R0bGVXYWl0TXNcbn0gPSB7fSkge1xuICBjb25zdCBob29rSWQgPSB1c2VJZCgpO1xuICBjb25zdCBjb21wbGV0aW9uSWQgPSBpZCB8fCBob29rSWQ7XG4gIGNvbnN0IHsgZGF0YSwgbXV0YXRlIH0gPSB1c2VTV1IyKFthcGksIGNvbXBsZXRpb25JZF0sIG51bGwsIHtcbiAgICBmYWxsYmFja0RhdGE6IGluaXRpYWxDb21wbGV0aW9uXG4gIH0pO1xuICBjb25zdCB7IGRhdGE6IGlzTG9hZGluZyA9IGZhbHNlLCBtdXRhdGU6IG11dGF0ZUxvYWRpbmcgfSA9IHVzZVNXUjIoXG4gICAgW2NvbXBsZXRpb25JZCwgXCJsb2FkaW5nXCJdLFxuICAgIG51bGxcbiAgKTtcbiAgY29uc3QgeyBkYXRhOiBzdHJlYW1EYXRhLCBtdXRhdGU6IG11dGF0ZVN0cmVhbURhdGEgfSA9IHVzZVNXUjIoW2NvbXBsZXRpb25JZCwgXCJzdHJlYW1EYXRhXCJdLCBudWxsKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTQodm9pZCAwKTtcbiAgY29uc3QgY29tcGxldGlvbiA9IGRhdGE7XG4gIGNvbnN0IFthYm9ydENvbnRyb2xsZXIsIHNldEFib3J0Q29udHJvbGxlcl0gPSB1c2VTdGF0ZTQobnVsbCk7XG4gIGNvbnN0IGV4dHJhTWV0YWRhdGFSZWYgPSB1c2VSZWYzKHtcbiAgICBjcmVkZW50aWFscyxcbiAgICBoZWFkZXJzLFxuICAgIGJvZHlcbiAgfSk7XG4gIHVzZUVmZmVjdDMoKCkgPT4ge1xuICAgIGV4dHJhTWV0YWRhdGFSZWYuY3VycmVudCA9IHtcbiAgICAgIGNyZWRlbnRpYWxzLFxuICAgICAgaGVhZGVycyxcbiAgICAgIGJvZHlcbiAgICB9O1xuICB9LCBbY3JlZGVudGlhbHMsIGhlYWRlcnMsIGJvZHldKTtcbiAgY29uc3QgdHJpZ2dlclJlcXVlc3QgPSB1c2VDYWxsYmFjazMoXG4gICAgYXN5bmMgKHByb21wdCwgb3B0aW9ucykgPT4gY2FsbENvbXBsZXRpb25BcGkoe1xuICAgICAgYXBpLFxuICAgICAgcHJvbXB0LFxuICAgICAgY3JlZGVudGlhbHM6IGV4dHJhTWV0YWRhdGFSZWYuY3VycmVudC5jcmVkZW50aWFscyxcbiAgICAgIGhlYWRlcnM6IHsgLi4uZXh0cmFNZXRhZGF0YVJlZi5jdXJyZW50LmhlYWRlcnMsIC4uLm9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuaGVhZGVycyB9LFxuICAgICAgYm9keToge1xuICAgICAgICAuLi5leHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQuYm9keSxcbiAgICAgICAgLi4ub3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5ib2R5XG4gICAgICB9LFxuICAgICAgc3RyZWFtUHJvdG9jb2wsXG4gICAgICBmZXRjaDogZmV0Y2gyLFxuICAgICAgLy8gdGhyb3R0bGUgc3RyZWFtZWQgdWkgdXBkYXRlczpcbiAgICAgIHNldENvbXBsZXRpb246IHRocm90dGxlKFxuICAgICAgICAoY29tcGxldGlvbjIpID0+IG11dGF0ZShjb21wbGV0aW9uMiwgZmFsc2UpLFxuICAgICAgICB0aHJvdHRsZVdhaXRNc1xuICAgICAgKSxcbiAgICAgIG9uRGF0YTogdGhyb3R0bGUoXG4gICAgICAgIChkYXRhMikgPT4gbXV0YXRlU3RyZWFtRGF0YShbLi4uc3RyZWFtRGF0YSAhPSBudWxsID8gc3RyZWFtRGF0YSA6IFtdLCAuLi5kYXRhMiAhPSBudWxsID8gZGF0YTIgOiBbXV0sIGZhbHNlKSxcbiAgICAgICAgdGhyb3R0bGVXYWl0TXNcbiAgICAgICksXG4gICAgICBzZXRMb2FkaW5nOiBtdXRhdGVMb2FkaW5nLFxuICAgICAgc2V0RXJyb3IsXG4gICAgICBzZXRBYm9ydENvbnRyb2xsZXIsXG4gICAgICBvblJlc3BvbnNlLFxuICAgICAgb25GaW5pc2gsXG4gICAgICBvbkVycm9yXG4gICAgfSksXG4gICAgW1xuICAgICAgbXV0YXRlLFxuICAgICAgbXV0YXRlTG9hZGluZyxcbiAgICAgIGFwaSxcbiAgICAgIGV4dHJhTWV0YWRhdGFSZWYsXG4gICAgICBzZXRBYm9ydENvbnRyb2xsZXIsXG4gICAgICBvblJlc3BvbnNlLFxuICAgICAgb25GaW5pc2gsXG4gICAgICBvbkVycm9yLFxuICAgICAgc2V0RXJyb3IsXG4gICAgICBzdHJlYW1EYXRhLFxuICAgICAgc3RyZWFtUHJvdG9jb2wsXG4gICAgICBmZXRjaDIsXG4gICAgICBtdXRhdGVTdHJlYW1EYXRhLFxuICAgICAgdGhyb3R0bGVXYWl0TXNcbiAgICBdXG4gICk7XG4gIGNvbnN0IHN0b3AgPSB1c2VDYWxsYmFjazMoKCkgPT4ge1xuICAgIGlmIChhYm9ydENvbnRyb2xsZXIpIHtcbiAgICAgIGFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgICAgc2V0QWJvcnRDb250cm9sbGVyKG51bGwpO1xuICAgIH1cbiAgfSwgW2Fib3J0Q29udHJvbGxlcl0pO1xuICBjb25zdCBzZXRDb21wbGV0aW9uID0gdXNlQ2FsbGJhY2szKFxuICAgIChjb21wbGV0aW9uMikgPT4ge1xuICAgICAgbXV0YXRlKGNvbXBsZXRpb24yLCBmYWxzZSk7XG4gICAgfSxcbiAgICBbbXV0YXRlXVxuICApO1xuICBjb25zdCBjb21wbGV0ZSA9IHVzZUNhbGxiYWNrMyhcbiAgICBhc3luYyAocHJvbXB0LCBvcHRpb25zKSA9PiB7XG4gICAgICByZXR1cm4gdHJpZ2dlclJlcXVlc3QocHJvbXB0LCBvcHRpb25zKTtcbiAgICB9LFxuICAgIFt0cmlnZ2VyUmVxdWVzdF1cbiAgKTtcbiAgY29uc3QgW2lucHV0LCBzZXRJbnB1dF0gPSB1c2VTdGF0ZTQoaW5pdGlhbElucHV0KTtcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gdXNlQ2FsbGJhY2szKFxuICAgIChldmVudCkgPT4ge1xuICAgICAgdmFyIF9hO1xuICAgICAgKF9hID0gZXZlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IGV2ZW50LnByZXZlbnREZWZhdWx0KSA9PSBudWxsID8gdm9pZCAwIDogX2EuY2FsbChldmVudCk7XG4gICAgICByZXR1cm4gaW5wdXQgPyBjb21wbGV0ZShpbnB1dCkgOiB2b2lkIDA7XG4gICAgfSxcbiAgICBbaW5wdXQsIGNvbXBsZXRlXVxuICApO1xuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IHVzZUNhbGxiYWNrMyhcbiAgICAoZSkgPT4ge1xuICAgICAgc2V0SW5wdXQoZS50YXJnZXQudmFsdWUpO1xuICAgIH0sXG4gICAgW3NldElucHV0XVxuICApO1xuICByZXR1cm4ge1xuICAgIGNvbXBsZXRpb24sXG4gICAgY29tcGxldGUsXG4gICAgZXJyb3IsXG4gICAgc2V0Q29tcGxldGlvbixcbiAgICBzdG9wLFxuICAgIGlucHV0LFxuICAgIHNldElucHV0LFxuICAgIGhhbmRsZUlucHV0Q2hhbmdlLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICBpc0xvYWRpbmcsXG4gICAgZGF0YTogc3RyZWFtRGF0YVxuICB9O1xufVxuXG4vLyBzcmMvdXNlLW9iamVjdC50c1xuaW1wb3J0IHtcbiAgaXNBYm9ydEVycm9yIGFzIGlzQWJvcnRFcnJvcjIsXG4gIHNhZmVWYWxpZGF0ZVR5cGVzXG59IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyLXV0aWxzXCI7XG5pbXBvcnQge1xuICBhc1NjaGVtYSxcbiAgaXNEZWVwRXF1YWxEYXRhIGFzIGlzRGVlcEVxdWFsRGF0YTIsXG4gIHBhcnNlUGFydGlhbEpzb25cbn0gZnJvbSBcIkBhaS1zZGsvdWktdXRpbHNcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrIGFzIHVzZUNhbGxiYWNrNCwgdXNlSWQgYXMgdXNlSWQyLCB1c2VSZWYgYXMgdXNlUmVmNCwgdXNlU3RhdGUgYXMgdXNlU3RhdGU1IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgdXNlU1dSMyBmcm9tIFwic3dyXCI7XG52YXIgZ2V0T3JpZ2luYWxGZXRjaDIgPSAoKSA9PiBmZXRjaDtcbmZ1bmN0aW9uIHVzZU9iamVjdCh7XG4gIGFwaSxcbiAgaWQsXG4gIHNjaGVtYSxcbiAgLy8gcmVxdWlyZWQsIGluIHRoZSBmdXR1cmUgd2Ugd2lsbCB1c2UgaXQgZm9yIHZhbGlkYXRpb25cbiAgaW5pdGlhbFZhbHVlLFxuICBmZXRjaDogZmV0Y2gyLFxuICBvbkVycm9yLFxuICBvbkZpbmlzaCxcbiAgaGVhZGVycyxcbiAgY3JlZGVudGlhbHNcbn0pIHtcbiAgY29uc3QgaG9va0lkID0gdXNlSWQyKCk7XG4gIGNvbnN0IGNvbXBsZXRpb25JZCA9IGlkICE9IG51bGwgPyBpZCA6IGhvb2tJZDtcbiAgY29uc3QgeyBkYXRhLCBtdXRhdGUgfSA9IHVzZVNXUjMoXG4gICAgW2FwaSwgY29tcGxldGlvbklkXSxcbiAgICBudWxsLFxuICAgIHsgZmFsbGJhY2tEYXRhOiBpbml0aWFsVmFsdWUgfVxuICApO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlNSh2b2lkIDApO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGU1KGZhbHNlKTtcbiAgY29uc3QgYWJvcnRDb250cm9sbGVyUmVmID0gdXNlUmVmNChudWxsKTtcbiAgY29uc3Qgc3RvcCA9IHVzZUNhbGxiYWNrNCgoKSA9PiB7XG4gICAgdmFyIF9hO1xuICAgIHRyeSB7XG4gICAgICAoX2EgPSBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmFib3J0KCk7XG4gICAgfSBjYXRjaCAoaWdub3JlZCkge1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cbiAgfSwgW10pO1xuICBjb25zdCBzdWJtaXQgPSBhc3luYyAoaW5wdXQpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgdHJ5IHtcbiAgICAgIG11dGF0ZSh2b2lkIDApO1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3Iodm9pZCAwKTtcbiAgICAgIGNvbnN0IGFib3J0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gYWJvcnRDb250cm9sbGVyO1xuICAgICAgY29uc3QgYWN0dWFsRmV0Y2ggPSBmZXRjaDIgIT0gbnVsbCA/IGZldGNoMiA6IGdldE9yaWdpbmFsRmV0Y2gyKCk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFjdHVhbEZldGNoKGFwaSwge1xuICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgICAgLi4uaGVhZGVyc1xuICAgICAgICB9LFxuICAgICAgICBjcmVkZW50aWFscyxcbiAgICAgICAgc2lnbmFsOiBhYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShpbnB1dClcbiAgICAgIH0pO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgKF9hID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpKSAhPSBudWxsID8gX2EgOiBcIkZhaWxlZCB0byBmZXRjaCB0aGUgcmVzcG9uc2UuXCJcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIGlmIChyZXNwb25zZS5ib2R5ID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhlIHJlc3BvbnNlIGJvZHkgaXMgZW1wdHkuXCIpO1xuICAgICAgfVxuICAgICAgbGV0IGFjY3VtdWxhdGVkVGV4dCA9IFwiXCI7XG4gICAgICBsZXQgbGF0ZXN0T2JqZWN0ID0gdm9pZCAwO1xuICAgICAgYXdhaXQgcmVzcG9uc2UuYm9keS5waXBlVGhyb3VnaChuZXcgVGV4dERlY29kZXJTdHJlYW0oKSkucGlwZVRvKFxuICAgICAgICBuZXcgV3JpdGFibGVTdHJlYW0oe1xuICAgICAgICAgIHdyaXRlKGNodW5rKSB7XG4gICAgICAgICAgICBhY2N1bXVsYXRlZFRleHQgKz0gY2h1bms7XG4gICAgICAgICAgICBjb25zdCB7IHZhbHVlIH0gPSBwYXJzZVBhcnRpYWxKc29uKGFjY3VtdWxhdGVkVGV4dCk7XG4gICAgICAgICAgICBjb25zdCBjdXJyZW50T2JqZWN0ID0gdmFsdWU7XG4gICAgICAgICAgICBpZiAoIWlzRGVlcEVxdWFsRGF0YTIobGF0ZXN0T2JqZWN0LCBjdXJyZW50T2JqZWN0KSkge1xuICAgICAgICAgICAgICBsYXRlc3RPYmplY3QgPSBjdXJyZW50T2JqZWN0O1xuICAgICAgICAgICAgICBtdXRhdGUoY3VycmVudE9iamVjdCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBjbG9zZSgpIHtcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgICAgICBpZiAob25GaW5pc2ggIT0gbnVsbCkge1xuICAgICAgICAgICAgICBjb25zdCB2YWxpZGF0aW9uUmVzdWx0ID0gc2FmZVZhbGlkYXRlVHlwZXMoe1xuICAgICAgICAgICAgICAgIHZhbHVlOiBsYXRlc3RPYmplY3QsXG4gICAgICAgICAgICAgICAgc2NoZW1hOiBhc1NjaGVtYShzY2hlbWEpXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBvbkZpbmlzaChcbiAgICAgICAgICAgICAgICB2YWxpZGF0aW9uUmVzdWx0LnN1Y2Nlc3MgPyB7IG9iamVjdDogdmFsaWRhdGlvblJlc3VsdC52YWx1ZSwgZXJyb3I6IHZvaWQgMCB9IDogeyBvYmplY3Q6IHZvaWQgMCwgZXJyb3I6IHZhbGlkYXRpb25SZXN1bHQuZXJyb3IgfVxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICk7XG4gICAgfSBjYXRjaCAoZXJyb3IyKSB7XG4gICAgICBpZiAoaXNBYm9ydEVycm9yMihlcnJvcjIpKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmIChvbkVycm9yICYmIGVycm9yMiBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIG9uRXJyb3IoZXJyb3IyKTtcbiAgICAgIH1cbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRFcnJvcihlcnJvcjIgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yMiA6IG5ldyBFcnJvcihTdHJpbmcoZXJyb3IyKSkpO1xuICAgIH1cbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBzdWJtaXQsXG4gICAgb2JqZWN0OiBkYXRhLFxuICAgIGVycm9yLFxuICAgIGlzTG9hZGluZyxcbiAgICBzdG9wXG4gIH07XG59XG52YXIgZXhwZXJpbWVudGFsX3VzZU9iamVjdCA9IHVzZU9iamVjdDtcbmV4cG9ydCB7XG4gIGV4cGVyaW1lbnRhbF91c2VPYmplY3QsXG4gIHVzZUFzc2lzdGFudCxcbiAgdXNlQ2hhdCxcbiAgdXNlQ29tcGxldGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/react/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n      message.parts.push({ type: \"step-start\" });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage,\n  requestType = \"generate\"\n}) {\n  var _a, _b, _c;\n  const request = requestType === \"resume\" ? fetch2(`${api}?chatId=${body.id}`, {\n    method: \"GET\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }) : fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,\n    credentials\n  });\n  const response = await request.catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_c = await response.text()) != null ? _c : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  if (message.role !== \"assistant\") {\n    return false;\n  }\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === \"step-start\" ? index : lastIndex;\n  }, -1);\n  const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part) => part.type === \"tool-invocation\");\n  return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;