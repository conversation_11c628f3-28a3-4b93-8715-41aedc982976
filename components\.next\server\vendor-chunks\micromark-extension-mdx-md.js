"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-mdx-md";
exports.ids = ["vendor-chunks/micromark-extension-mdx-md"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-mdx-md/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark-extension-mdx-md/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mdxMd: () => (/* binding */ mdxMd)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').Extension} Extension\n */\n\n/**\n * Create an extension for `micromark` to disable some CommonMark syntax (code\n * (indented), autolinks, and HTML (flow and text)) for MDX.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to disable\n *   some CommonMark syntax for MDX.\n */\nfunction mdxMd() {\n  return {\n    disable: {null: ['autolink', 'codeIndented', 'htmlFlow', 'htmlText']}\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1tZHgtbWQvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSwwQ0FBMEM7QUFDdkQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLGNBQWM7QUFDZDtBQUNBIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXG1pY3JvbWFyay1leHRlbnNpb24tbWR4LW1kXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuRXh0ZW5zaW9ufSBFeHRlbnNpb25cbiAqL1xuXG4vKipcbiAqIENyZWF0ZSBhbiBleHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRvIGRpc2FibGUgc29tZSBDb21tb25NYXJrIHN5bnRheCAoY29kZVxuICogKGluZGVudGVkKSwgYXV0b2xpbmtzLCBhbmQgSFRNTCAoZmxvdyBhbmQgdGV4dCkpIGZvciBNRFguXG4gKlxuICogQHJldHVybnMge0V4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGV4dGVuc2lvbnNgIHRvIGRpc2FibGVcbiAqICAgc29tZSBDb21tb25NYXJrIHN5bnRheCBmb3IgTURYLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbWR4TWQoKSB7XG4gIHJldHVybiB7XG4gICAgZGlzYWJsZToge251bGw6IFsnYXV0b2xpbmsnLCAnY29kZUluZGVudGVkJywgJ2h0bWxGbG93JywgJ2h0bWxUZXh0J119XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-mdx-md/index.js\n");

/***/ })

};
;