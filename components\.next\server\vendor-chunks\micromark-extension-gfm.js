"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm";
exports.ids = ["vendor-chunks/micromark-extension-gfm"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfm: () => (/* binding */ gfm),\n/* harmony export */   gfmHtml: () => (/* binding */ gfmHtml)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var micromark_extension_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-extension-gfm-autolink-literal */ \"(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js\");\n/* harmony import */ var micromark_extension_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-extension-gfm-autolink-literal */ \"(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js\");\n/* harmony import */ var micromark_extension_gfm_footnote__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-extension-gfm-footnote */ \"(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js\");\n/* harmony import */ var micromark_extension_gfm_footnote__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-extension-gfm-footnote */ \"(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js\");\n/* harmony import */ var micromark_extension_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-extension-gfm-strikethrough */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js\");\n/* harmony import */ var micromark_extension_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-extension-gfm-strikethrough */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js\");\n/* harmony import */ var micromark_extension_gfm_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-extension-gfm-table */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\");\n/* harmony import */ var micromark_extension_gfm_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-extension-gfm-table */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/html.js\");\n/* harmony import */ var micromark_extension_gfm_tagfilter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-extension-gfm-tagfilter */ \"(ssr)/./node_modules/micromark-extension-gfm-tagfilter/index.js\");\n/* harmony import */ var micromark_extension_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-extension-gfm-task-list-item */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js\");\n/* harmony import */ var micromark_extension_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-extension-gfm-task-list-item */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js\");\n/**\n * @typedef {import('micromark-extension-gfm-footnote').HtmlOptions} HtmlOptions\n * @typedef {import('micromark-extension-gfm-strikethrough').Options} Options\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\n\n\n\n\n\n\n\n\n/**\n * Create an extension for `micromark` to enable GFM syntax.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n *\n *   Passed to `micromark-extens-gfm-strikethrough`.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   syntax.\n */\nfunction gfm(options) {\n  return (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([\n    micromark_extension_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_1__.gfmAutolinkLiteral,\n    (0,micromark_extension_gfm_footnote__WEBPACK_IMPORTED_MODULE_2__.gfmFootnote)(),\n    (0,micromark_extension_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_3__.gfmStrikethrough)(options),\n    micromark_extension_gfm_table__WEBPACK_IMPORTED_MODULE_4__.gfmTable,\n    micromark_extension_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_5__.gfmTaskListItem\n  ])\n}\n\n/**\n * Create an extension for `micromark` to support GFM when serializing to HTML.\n *\n * @param {HtmlOptions | null | undefined} [options]\n *   Configuration.\n *\n *   Passed to `micromark-extens-gfm-footnote`.\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM when serializing to HTML.\n */\nfunction gfmHtml(options) {\n  return (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineHtmlExtensions)([\n    micromark_extension_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_6__.gfmAutolinkLiteralHtml,\n    (0,micromark_extension_gfm_footnote__WEBPACK_IMPORTED_MODULE_7__.gfmFootnoteHtml)(options),\n    micromark_extension_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_8__.gfmStrikethroughHtml,\n    micromark_extension_gfm_table__WEBPACK_IMPORTED_MODULE_9__.gfmTableHtml,\n    micromark_extension_gfm_tagfilter__WEBPACK_IMPORTED_MODULE_10__.gfmTagfilterHtml,\n    micromark_extension_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_11__.gfmTaskListItemHtml\n  ])\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethroughHtml: () => (/* binding */ gfmStrikethroughHtml)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\n// To do: next major: expose function instead of object.\n\n/**\n * Extension for `micromark` that can be passed in `htmlExtensions`, to\n * support GFM strikethrough when serializing to HTML.\n *\n * @type {HtmlExtension}\n */\nconst gfmStrikethroughHtml = {\n  enter: {\n    strikethrough() {\n      this.tag('<del>')\n    }\n  },\n  exit: {\n    strikethrough() {\n      this.tag('</del>')\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXN0cmlrZXRocm91Z2gvZGV2L2xpYi9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsOENBQThDO0FBQzNEOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm1cXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tc3RyaWtldGhyb3VnaFxcZGV2XFxsaWJcXGh0bWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLkh0bWxFeHRlbnNpb259IEh0bWxFeHRlbnNpb25cbiAqL1xuXG4vLyBUbyBkbzogbmV4dCBtYWpvcjogZXhwb3NlIGZ1bmN0aW9uIGluc3RlYWQgb2Ygb2JqZWN0LlxuXG4vKipcbiAqIEV4dGVuc2lvbiBmb3IgYG1pY3JvbWFya2AgdGhhdCBjYW4gYmUgcGFzc2VkIGluIGBodG1sRXh0ZW5zaW9uc2AsIHRvXG4gKiBzdXBwb3J0IEdGTSBzdHJpa2V0aHJvdWdoIHdoZW4gc2VyaWFsaXppbmcgdG8gSFRNTC5cbiAqXG4gKiBAdHlwZSB7SHRtbEV4dGVuc2lvbn1cbiAqL1xuZXhwb3J0IGNvbnN0IGdmbVN0cmlrZXRocm91Z2hIdG1sID0ge1xuICBlbnRlcjoge1xuICAgIHN0cmlrZXRocm91Z2goKSB7XG4gICAgICB0aGlzLnRhZygnPGRlbD4nKVxuICAgIH1cbiAgfSxcbiAgZXhpdDoge1xuICAgIHN0cmlrZXRocm91Z2goKSB7XG4gICAgICB0aGlzLnRhZygnPC9kZWw+JylcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethrough: () => (/* binding */ gfmStrikethrough)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-classify-character/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/types.js\");\n/**\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean} [singleTilde=true]\n *   Whether to support strikethrough with a single tilde.\n *\n *   Single tildes work on github.com, but are technically prohibited by the\n *   GFM spec.\n */\n\n\n\n\n\n\n\n\n\n/**\n * Create an extension for `micromark` to enable GFM strikethrough syntax.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable GFM strikethrough syntax.\n */\nfunction gfmStrikethrough(options) {\n  const options_ = options || {}\n  let single = options_.singleTilde\n  const tokenizer = {\n    tokenize: tokenizeStrikethrough,\n    resolveAll: resolveAllStrikethrough\n  }\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    text: {[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.tilde]: tokenizer},\n    insideSpan: {null: [tokenizer]},\n    attentionMarkers: {null: [micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.tilde]}\n  }\n\n  /**\n   * Take events and resolve strikethrough.\n   *\n   * @type {Resolver}\n   */\n  function resolveAllStrikethrough(events, context) {\n    let index = -1\n\n    // Walk through all events.\n    while (++index < events.length) {\n      // Find a token that can close.\n      if (\n        events[index][0] === 'enter' &&\n        events[index][1].type === 'strikethroughSequenceTemporary' &&\n        events[index][1]._close\n      ) {\n        let open = index\n\n        // Now walk back to find an opener.\n        while (open--) {\n          // Find a token that can open the closer.\n          if (\n            events[open][0] === 'exit' &&\n            events[open][1].type === 'strikethroughSequenceTemporary' &&\n            events[open][1]._open &&\n            // If the sizes are the same:\n            events[index][1].end.offset - events[index][1].start.offset ===\n              events[open][1].end.offset - events[open][1].start.offset\n          ) {\n            events[index][1].type = 'strikethroughSequence'\n            events[open][1].type = 'strikethroughSequence'\n\n            /** @type {Token} */\n            const strikethrough = {\n              type: 'strikethrough',\n              start: Object.assign({}, events[open][1].start),\n              end: Object.assign({}, events[index][1].end)\n            }\n\n            /** @type {Token} */\n            const text = {\n              type: 'strikethroughText',\n              start: Object.assign({}, events[open][1].end),\n              end: Object.assign({}, events[index][1].start)\n            }\n\n            // Opening.\n            /** @type {Array<Event>} */\n            const nextEvents = [\n              ['enter', strikethrough, context],\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context],\n              ['enter', text, context]\n            ]\n\n            const insideSpan = context.parser.constructs.insideSpan.null\n\n            if (insideSpan) {\n              // Between.\n              (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n                nextEvents,\n                nextEvents.length,\n                0,\n                (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(insideSpan, events.slice(open + 1, index), context)\n              )\n            }\n\n            // Closing.\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(nextEvents, nextEvents.length, 0, [\n              ['exit', text, context],\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context],\n              ['exit', strikethrough, context]\n            ])\n\n            ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(events, open - 1, index - open + 3, nextEvents)\n\n            index = open + nextEvents.length - 2\n            break\n          }\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      if (events[index][1].type === 'strikethroughSequenceTemporary') {\n        events[index][1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.data\n      }\n    }\n\n    return events\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeStrikethrough(effects, ok, nok) {\n    const previous = this.previous\n    const events = this.events\n    let size = 0\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.tilde, 'expected `~`')\n\n      if (\n        previous === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.tilde &&\n        events[events.length - 1][1].type !== micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.characterEscape\n      ) {\n        return nok(code)\n      }\n\n      effects.enter('strikethroughSequenceTemporary')\n      return more(code)\n    }\n\n    /** @type {State} */\n    function more(code) {\n      const before = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_5__.classifyCharacter)(previous)\n\n      if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.tilde) {\n        // If this is the third marker, exit.\n        if (size > 1) return nok(code)\n        effects.consume(code)\n        size++\n        return more\n      }\n\n      if (size < 2 && !single) return nok(code)\n      const token = effects.exit('strikethroughSequenceTemporary')\n      const after = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_5__.classifyCharacter)(code)\n      token._open =\n        !after || (after === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_6__.constants.attentionSideAfter && Boolean(before))\n      token._close =\n        !before || (before === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_6__.constants.attentionSideAfter && Boolean(after))\n      return ok(code)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nclass EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {void}\n   */\n  add(index, remove, add) {\n    addImpl(this, index, remove, add)\n  }\n\n  // To do: not used here.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {void}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImpl(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {void}\n   */\n  consume(events) {\n    this.map.sort((a, b) => a[0] - b[0])\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(events.slice(this.map[index][0] + this.map[index][1]))\n      // eslint-disable-next-line unicorn/no-array-push-push\n      vecs.push(this.map[index][2])\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push([...events])\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      events.push(...slice)\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {void}\n */\nfunction addImpl(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       console.log('to do: links?', add, rmCurr)\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\n/**\n * @typedef {import('./infer.js').Align} Align\n */\n\n\n\nconst alignment = {\n  none: '',\n  left: ' align=\"left\"',\n  right: ' align=\"right\"',\n  center: ' align=\"center\"'\n}\n\n// To do: next major: expose functions.\n// To do: next major: use `infer` here, when all events are exposed.\n\n/**\n * Extension for `micromark` that can be passed in `htmlExtensions` to support\n * GFM tables when serializing to HTML.\n *\n * @type {HtmlExtension}\n */\nconst gfmTableHtml = {\n  enter: {\n    table(token) {\n      const tableAlign = token._align\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `_align`')\n      this.lineEndingIfNeeded()\n      this.tag('<table>')\n      this.setData('tableAlign', tableAlign)\n    },\n    tableBody() {\n      this.tag('<tbody>')\n    },\n    tableData() {\n      const tableAlign = this.getData('tableAlign')\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n      const align = alignment[tableAlign[tableColumn]]\n\n      if (align === undefined) {\n        // Capture results to ignore them.\n        this.buffer()\n      } else {\n        this.lineEndingIfNeeded()\n        this.tag('<td' + align + '>')\n      }\n    },\n    tableHead() {\n      this.lineEndingIfNeeded()\n      this.tag('<thead>')\n    },\n    tableHeader() {\n      const tableAlign = this.getData('tableAlign')\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n      const align = alignment[tableAlign[tableColumn]]\n      this.lineEndingIfNeeded()\n      this.tag('<th' + align + '>')\n    },\n    tableRow() {\n      this.setData('tableColumn', 0)\n      this.lineEndingIfNeeded()\n      this.tag('<tr>')\n    }\n  },\n  exit: {\n    // Overwrite the default code text data handler to unescape escaped pipes when\n    // they are in tables.\n    codeTextData(token) {\n      let value = this.sliceSerialize(token)\n\n      if (this.getData('tableAlign')) {\n        value = value.replace(/\\\\([\\\\|])/g, replace)\n      }\n\n      this.raw(this.encode(value))\n    },\n    table() {\n      this.setData('tableAlign')\n      // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n      // but we do need to reset it to match a funky newline GH generates for\n      // list items combined with tables.\n      this.setData('slurpAllLineEndings')\n      this.lineEndingIfNeeded()\n      this.tag('</table>')\n    },\n    tableBody() {\n      this.lineEndingIfNeeded()\n      this.tag('</tbody>')\n    },\n    tableData() {\n      const tableAlign = this.getData('tableAlign')\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n      if (tableColumn in tableAlign) {\n        this.tag('</td>')\n        this.setData('tableColumn', tableColumn + 1)\n      } else {\n        // Stop capturing.\n        this.resume()\n      }\n    },\n    tableHead() {\n      this.lineEndingIfNeeded()\n      this.tag('</thead>')\n    },\n    tableHeader() {\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n      this.tag('</th>')\n      this.setData('tableColumn', tableColumn + 1)\n    },\n    tableRow() {\n      const tableAlign = this.getData('tableAlign')\n      let tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n      while (tableColumn < tableAlign.length) {\n        this.lineEndingIfNeeded()\n        this.tag('<td' + alignment[tableAlign[tableColumn]] + '></td>')\n        tableColumn++\n      }\n\n      this.setData('tableColumn', tableColumn)\n      this.lineEndingIfNeeded()\n      this.tag('</tr>')\n    }\n  }\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\n/**\n * @typedef {'left' | 'center' | 'right' | 'none'} Align\n */\n\n\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Array<Event>} events\n * @param {number} index\n * @returns {Array<Align>}\n */\nfunction gfmTableAlign(events, index) {\n  (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\n\n\n\n\n\n\n\n\n\n// To do: next major: expose functions.\n\n/**\n * Extension for `micromark` that can be passed in `extensions` to enable GFM\n * table syntax.\n *\n * @type {Extension}\n */\nconst gfmTable = {\n  flow: {null: {tokenize: tokenizeTable, resolveAll: resolveTable}}\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, headRowBreak, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        headDelimiterBefore,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        headDelimiterValueBefore,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        headDelimiterCellAfter,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n// eslint-disable-next-line complexity\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/// Generate a cell.\n/**\n *\n * @param {EditMap} map\n * @param {TokenizeContext} context\n * @param {Range} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n      ? 'tableDelimiter'\n      : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.chunkText\n      start[1].contentType = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {EditMap} map\n * @param {TokenizeContext} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Array<Event>} events\n * @param {number} index\n * @returns {readonly Point}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemHtml: () => (/* binding */ gfmTaskListItemHtml)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\n// To do: next major: expose function to make extension.\n\n/**\n * Extension for `micromark` that can be passed in `htmlExtensions` to\n * support GFM task list items when serializing to HTML.\n *\n * @type {HtmlExtension}\n */\nconst gfmTaskListItemHtml = {\n  enter: {\n    taskListCheck() {\n      this.tag('<input type=\"checkbox\" disabled=\"\" ')\n    }\n  },\n  exit: {\n    taskListCheck() {\n      this.tag('/>')\n    },\n    taskListCheckValueChecked() {\n      this.tag('checked=\"\" ')\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXRhc2stbGlzdC1pdGVtL2Rldi9saWIvaHRtbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDhDQUE4QztBQUMzRDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm1cXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW1cXGRldlxcbGliXFxodG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5IdG1sRXh0ZW5zaW9ufSBIdG1sRXh0ZW5zaW9uXG4gKi9cblxuLy8gVG8gZG86IG5leHQgbWFqb3I6IGV4cG9zZSBmdW5jdGlvbiB0byBtYWtlIGV4dGVuc2lvbi5cblxuLyoqXG4gKiBFeHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRoYXQgY2FuIGJlIHBhc3NlZCBpbiBgaHRtbEV4dGVuc2lvbnNgIHRvXG4gKiBzdXBwb3J0IEdGTSB0YXNrIGxpc3QgaXRlbXMgd2hlbiBzZXJpYWxpemluZyB0byBIVE1MLlxuICpcbiAqIEB0eXBlIHtIdG1sRXh0ZW5zaW9ufVxuICovXG5leHBvcnQgY29uc3QgZ2ZtVGFza0xpc3RJdGVtSHRtbCA9IHtcbiAgZW50ZXI6IHtcbiAgICB0YXNrTGlzdENoZWNrKCkge1xuICAgICAgdGhpcy50YWcoJzxpbnB1dCB0eXBlPVwiY2hlY2tib3hcIiBkaXNhYmxlZD1cIlwiICcpXG4gICAgfVxuICB9LFxuICBleGl0OiB7XG4gICAgdGFza0xpc3RDaGVjaygpIHtcbiAgICAgIHRoaXMudGFnKCcvPicpXG4gICAgfSxcbiAgICB0YXNrTGlzdENoZWNrVmFsdWVDaGVja2VkKCkge1xuICAgICAgdGhpcy50YWcoJ2NoZWNrZWQ9XCJcIiAnKVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItem: () => (/* binding */ gfmTaskListItem)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/types.js\");\n/**\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\nconst tasklistCheck = {tokenize: tokenizeTasklistCheck}\n\n// To do: next major: expose function to make extension.\n\n/**\n * Extension for `micromark` that can be passed in `extensions`, to\n * enable GFM task list items syntax.\n *\n * @type {Extension}\n */\nconst gfmTaskListItem = {\n  text: {[micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.leftSquareBracket]: tasklistCheck}\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTasklistCheck(effects, ok, nok) {\n  const self = this\n\n  return open\n\n  /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.leftSquareBracket, 'expected `[`')\n\n    if (\n      // Exit if there’s stuff before.\n      self.previous !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      // Exit if not in the first content that is the first child of a list\n      // item.\n      !self._gfmTasklistFirstContentOfListItem\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('taskListCheck')\n    effects.enter('taskListCheckMarker')\n    effects.consume(code)\n    effects.exit('taskListCheckMarker')\n    return inside\n  }\n\n  /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // Currently we match how GH works in files.\n    // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n    // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n      effects.enter('taskListCheckValueUnchecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueUnchecked')\n      return close\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.uppercaseX || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lowercaseX) {\n      effects.enter('taskListCheckValueChecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueChecked')\n      return close\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function close(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.enter('taskListCheckMarker')\n      effects.consume(code)\n      effects.exit('taskListCheckMarker')\n      effects.exit('taskListCheck')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   */\n  function after(code) {\n    // EOL in paragraph means there must be something else after it.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      return ok(code)\n    }\n\n    // Space or tab?\n    // Check what comes after.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return effects.check({tokenize: spaceThenNonSpace}, ok, nok)(code)\n    }\n\n    // EOF, or non-whitespace, both wrong.\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction spaceThenNonSpace(effects, ok, nok) {\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, after, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.whitespace)\n\n  /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // EOF means there was nothing, so bad.\n    // EOL means there’s content after it, so good.\n    // Impossible to have more spaces.\n    // Anything else is good.\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-factory-space/dev/index.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-factory-space/dev/index.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   factorySpace: () => (/* binding */ factorySpace)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js\");\n/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\n\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns\n *   Start state.\n */\nfunction factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-factory-space/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asciiAlpha: () => (/* binding */ asciiAlpha),\n/* harmony export */   asciiAlphanumeric: () => (/* binding */ asciiAlphanumeric),\n/* harmony export */   asciiAtext: () => (/* binding */ asciiAtext),\n/* harmony export */   asciiControl: () => (/* binding */ asciiControl),\n/* harmony export */   asciiDigit: () => (/* binding */ asciiDigit),\n/* harmony export */   asciiHexDigit: () => (/* binding */ asciiHexDigit),\n/* harmony export */   asciiPunctuation: () => (/* binding */ asciiPunctuation),\n/* harmony export */   markdownLineEnding: () => (/* binding */ markdownLineEnding),\n/* harmony export */   markdownLineEndingOrSpace: () => (/* binding */ markdownLineEndingOrSpace),\n/* harmony export */   markdownSpace: () => (/* binding */ markdownSpace),\n/* harmony export */   unicodePunctuation: () => (/* binding */ unicodePunctuation),\n/* harmony export */   unicodeWhitespace: () => (/* binding */ unicodeWhitespace)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var _lib_unicode_punctuation_regex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/unicode-punctuation-regex.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\n\n\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction markdownLineEnding(code) {\n  return code !== null && code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction markdownLineEndingOrSpace(code) {\n  return code !== null && (code < micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.nul || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nfunction markdownSpace(code) {\n  return (\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace ||\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst unicodePunctuation = regexCheck(_lib_unicode_punctuation_regex_js__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuationRegex)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nconst unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n * @returns {(code: Code) => boolean}\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && regex.test(String.fromCharCode(code))\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unicodePunctuationRegex: () => (/* binding */ unicodePunctuationRegex)\n/* harmony export */ });\n// This module is generated by `script/`.\n//\n// CommonMark handles attention (emphasis, strong) markers based on what comes\n// before or after them.\n// One such difference is if those characters are Unicode punctuation.\n// This script is generated from the Unicode data.\n\n/**\n * Regular expression that matches a unicode punctuation character.\n */\nconst unicodePunctuationRegex =\n  /[!-/:-@[-`{-~\\u00A1\\u00A7\\u00AB\\u00B6\\u00B7\\u00BB\\u00BF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061D-\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C77\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1B7D\\u1B7E\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4F\\u2E52-\\u2E5D\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0vbm9kZV9tb2R1bGVzL21pY3JvbWFyay11dGlsLWNoYXJhY3Rlci9kZXYvbGliL3VuaWNvZGUtcHVuY3R1YXRpb24tcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGNBQWMiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLWV4dGVuc2lvbi1nZm1cXG5vZGVfbW9kdWxlc1xcbWljcm9tYXJrLXV0aWwtY2hhcmFjdGVyXFxkZXZcXGxpYlxcdW5pY29kZS1wdW5jdHVhdGlvbi1yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIG1vZHVsZSBpcyBnZW5lcmF0ZWQgYnkgYHNjcmlwdC9gLlxuLy9cbi8vIENvbW1vbk1hcmsgaGFuZGxlcyBhdHRlbnRpb24gKGVtcGhhc2lzLCBzdHJvbmcpIG1hcmtlcnMgYmFzZWQgb24gd2hhdCBjb21lc1xuLy8gYmVmb3JlIG9yIGFmdGVyIHRoZW0uXG4vLyBPbmUgc3VjaCBkaWZmZXJlbmNlIGlzIGlmIHRob3NlIGNoYXJhY3RlcnMgYXJlIFVuaWNvZGUgcHVuY3R1YXRpb24uXG4vLyBUaGlzIHNjcmlwdCBpcyBnZW5lcmF0ZWQgZnJvbSB0aGUgVW5pY29kZSBkYXRhLlxuXG4vKipcbiAqIFJlZ3VsYXIgZXhwcmVzc2lvbiB0aGF0IG1hdGNoZXMgYSB1bmljb2RlIHB1bmN0dWF0aW9uIGNoYXJhY3Rlci5cbiAqL1xuZXhwb3J0IGNvbnN0IHVuaWNvZGVQdW5jdHVhdGlvblJlZ2V4ID1cbiAgL1shLS86LUBbLWB7LX5cXHUwMEExXFx1MDBBN1xcdTAwQUJcXHUwMEI2XFx1MDBCN1xcdTAwQkJcXHUwMEJGXFx1MDM3RVxcdTAzODdcXHUwNTVBLVxcdTA1NUZcXHUwNTg5XFx1MDU4QVxcdTA1QkVcXHUwNUMwXFx1MDVDM1xcdTA1QzZcXHUwNUYzXFx1MDVGNFxcdTA2MDlcXHUwNjBBXFx1MDYwQ1xcdTA2MERcXHUwNjFCXFx1MDYxRC1cXHUwNjFGXFx1MDY2QS1cXHUwNjZEXFx1MDZENFxcdTA3MDAtXFx1MDcwRFxcdTA3RjctXFx1MDdGOVxcdTA4MzAtXFx1MDgzRVxcdTA4NUVcXHUwOTY0XFx1MDk2NVxcdTA5NzBcXHUwOUZEXFx1MEE3NlxcdTBBRjBcXHUwQzc3XFx1MEM4NFxcdTBERjRcXHUwRTRGXFx1MEU1QVxcdTBFNUJcXHUwRjA0LVxcdTBGMTJcXHUwRjE0XFx1MEYzQS1cXHUwRjNEXFx1MEY4NVxcdTBGRDAtXFx1MEZENFxcdTBGRDlcXHUwRkRBXFx1MTA0QS1cXHUxMDRGXFx1MTBGQlxcdTEzNjAtXFx1MTM2OFxcdTE0MDBcXHUxNjZFXFx1MTY5QlxcdTE2OUNcXHUxNkVCLVxcdTE2RURcXHUxNzM1XFx1MTczNlxcdTE3RDQtXFx1MTdENlxcdTE3RDgtXFx1MTdEQVxcdTE4MDAtXFx1MTgwQVxcdTE5NDRcXHUxOTQ1XFx1MUExRVxcdTFBMUZcXHUxQUEwLVxcdTFBQTZcXHUxQUE4LVxcdTFBQURcXHUxQjVBLVxcdTFCNjBcXHUxQjdEXFx1MUI3RVxcdTFCRkMtXFx1MUJGRlxcdTFDM0ItXFx1MUMzRlxcdTFDN0VcXHUxQzdGXFx1MUNDMC1cXHUxQ0M3XFx1MUNEM1xcdTIwMTAtXFx1MjAyN1xcdTIwMzAtXFx1MjA0M1xcdTIwNDUtXFx1MjA1MVxcdTIwNTMtXFx1MjA1RVxcdTIwN0RcXHUyMDdFXFx1MjA4RFxcdTIwOEVcXHUyMzA4LVxcdTIzMEJcXHUyMzI5XFx1MjMyQVxcdTI3NjgtXFx1Mjc3NVxcdTI3QzVcXHUyN0M2XFx1MjdFNi1cXHUyN0VGXFx1Mjk4My1cXHUyOTk4XFx1MjlEOC1cXHUyOURCXFx1MjlGQ1xcdTI5RkRcXHUyQ0Y5LVxcdTJDRkNcXHUyQ0ZFXFx1MkNGRlxcdTJENzBcXHUyRTAwLVxcdTJFMkVcXHUyRTMwLVxcdTJFNEZcXHUyRTUyLVxcdTJFNURcXHUzMDAxLVxcdTMwMDNcXHUzMDA4LVxcdTMwMTFcXHUzMDE0LVxcdTMwMUZcXHUzMDMwXFx1MzAzRFxcdTMwQTBcXHUzMEZCXFx1QTRGRVxcdUE0RkZcXHVBNjBELVxcdUE2MEZcXHVBNjczXFx1QTY3RVxcdUE2RjItXFx1QTZGN1xcdUE4NzQtXFx1QTg3N1xcdUE4Q0VcXHVBOENGXFx1QThGOC1cXHVBOEZBXFx1QThGQ1xcdUE5MkVcXHVBOTJGXFx1QTk1RlxcdUE5QzEtXFx1QTlDRFxcdUE5REVcXHVBOURGXFx1QUE1Qy1cXHVBQTVGXFx1QUFERVxcdUFBREZcXHVBQUYwXFx1QUFGMVxcdUFCRUJcXHVGRDNFXFx1RkQzRlxcdUZFMTAtXFx1RkUxOVxcdUZFMzAtXFx1RkU1MlxcdUZFNTQtXFx1RkU2MVxcdUZFNjNcXHVGRTY4XFx1RkU2QVxcdUZFNkJcXHVGRjAxLVxcdUZGMDNcXHVGRjA1LVxcdUZGMEFcXHVGRjBDLVxcdUZGMEZcXHVGRjFBXFx1RkYxQlxcdUZGMUZcXHVGRjIwXFx1RkYzQi1cXHVGRjNEXFx1RkYzRlxcdUZGNUJcXHVGRjVEXFx1RkY1Ri1cXHVGRjY1XS9cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/lib/unicode-punctuation-regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-chunked/dev/index.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-chunked/dev/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   push: () => (/* binding */ push),\n/* harmony export */   splice: () => (/* binding */ splice)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js\");\n\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {void}\n *   Nothing.\n */\nfunction splice(list, start, remove, items) {\n  const end = list.length\n  let chunkStart = 0\n  /** @type {Array<unknown>} */\n  let parameters\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove)\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(\n        chunkStart,\n        chunkStart + micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize\n      )\n      parameters.unshift(start, 0)\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters)\n\n      chunkStart += micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize\n      start += micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nfunction push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-chunked/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-classify-character/dev/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-classify-character/dev/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   classifyCharacter: () => (/* binding */ classifyCharacter)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\n\n\n\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nfunction classifyCharacter(code) {\n  if (\n    code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) ||\n    (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)\n  ) {\n    return micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.characterGroupWhitespace\n  }\n\n  if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code)) {\n    return micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.characterGroupPunctuation\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-classify-character/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-combine-extensions/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-combine-extensions/index.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combineExtensions: () => (/* binding */ combineExtensions),\n/* harmony export */   combineHtmlExtensions: () => (/* binding */ combineHtmlExtensions)\n/* harmony export */ });\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-chunked/dev/index.js\");\n/**\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').Handles} Handles\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n * @typedef {import('micromark-util-types').NormalizedExtension} NormalizedExtension\n */\n\n\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {Array<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nfunction combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {}\n  let index = -1\n\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index])\n  }\n\n  return all\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {void}\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {})\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook]\n    /** @type {string} */\n    let code\n\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = []\n        const value = right[code]\n        constructs(\n          // @ts-expect-error Looks like a list.\n          left[code],\n          Array.isArray(value) ? value : value ? [value] : []\n        )\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n * @param {Array<unknown>} list\n * @returns {void}\n */\nfunction constructs(existing, list) {\n  let index = -1\n  /** @type {Array<unknown>} */\n  const before = []\n\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_0__.splice)(existing, 0, 0, before)\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {Array<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   A single combined HTML extension.\n */\nfunction combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {}\n  let index = -1\n\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index])\n  }\n\n  return handlers\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {void}\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    const left = maybe || (all[hook] = {})\n    const right = extension[hook]\n    /** @type {keyof Handles} */\n    let type\n\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type]\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-combine-extensions/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-resolve-all/index.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-resolve-all/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveAll: () => (/* binding */ resolveAll)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {Array<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nfunction resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = []\n  let index = -1\n\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll\n\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-resolve-all/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65279,\n  // Unicode Specials block.\n  replacementCharacter: 65533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nconst constants = /** @type {const} */ ({\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeContent: 'content',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlRaw: 1, // Symbol for `<script>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlBasic: 6, // Symbol for `<div`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/types.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/types.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm/node_modules/micromark-util-symbol/types.js\n");

/***/ })

};
;