"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile";
exports.ids = ["vendor-chunks/vfile"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vfile/lib/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFile: () => (/* binding */ VFile)\n/* harmony export */ });\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/is-buffer/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile/node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _minpath_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minpath.js */ \"path\");\n/* harmony import */ var _minproc_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./minproc.js */ \"process\");\n/* harmony import */ var _minurl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minurl.js */ \"(ssr)/./node_modules/vfile/lib/minurl.shared.js\");\n/* harmony import */ var _minurl_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./minurl.js */ \"url\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('./minurl.shared.js').URL} URL\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Value} Value\n */\n\n/**\n * @typedef {Record<string, unknown> & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef {'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex'} BufferEncoding\n *   Encodings supported by the buffer class.\n *\n *   This is a copy of the types from Node, copied to prevent Node globals from\n *   being needed.\n *   Copied from: <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/90a4ec8/types/node/buffer.d.ts#L170>\n *\n * @typedef {Options | URL | Value | VFile} Compatible\n *   Things that can be passed to the constructor.\n *\n * @typedef VFileCoreOptions\n *   Set multiple values.\n * @property {Value | null | undefined} [value]\n *   Set `value`.\n * @property {string | null | undefined} [cwd]\n *   Set `cwd`.\n * @property {Array<string> | null | undefined} [history]\n *   Set `history`.\n * @property {URL | string | null | undefined} [path]\n *   Set `path`.\n * @property {string | null | undefined} [basename]\n *   Set `basename`.\n * @property {string | null | undefined} [stem]\n *   Set `stem`.\n * @property {string | null | undefined} [extname]\n *   Set `extname`.\n * @property {string | null | undefined} [dirname]\n *   Set `dirname`.\n * @property {Data | null | undefined} [data]\n *   Set `data`.\n *\n * @typedef Map\n *   Raw source map.\n *\n *   See:\n *   <https://github.com/mozilla/source-map/blob/58819f0/source-map.d.ts#L15-L23>.\n * @property {number} version\n *   Which version of the source map spec this map is following.\n * @property {Array<string>} sources\n *   An array of URLs to the original source files.\n * @property {Array<string>} names\n *   An array of identifiers which can be referenced by individual mappings.\n * @property {string | undefined} [sourceRoot]\n *   The URL root from which all sources are relative.\n * @property {Array<string> | undefined} [sourcesContent]\n *   An array of contents of the original source files.\n * @property {string} mappings\n *   A string of base64 VLQs which contain the actual mappings.\n * @property {string} file\n *   The generated file this source map is associated with.\n *\n * @typedef {{[key: string]: unknown} & VFileCoreOptions} Options\n *   Configuration.\n *\n *   A bunch of keys that will be shallow copied over to the new file.\n *\n * @typedef {Record<string, unknown>} ReporterSettings\n *   Configuration for reporters.\n */\n\n/**\n * @template {ReporterSettings} Settings\n *   Options type.\n * @callback Reporter\n *   Type for a reporter.\n * @param {Array<VFile>} files\n *   Files to report.\n * @param {Settings} options\n *   Configuration.\n * @returns {string}\n *   Report.\n */\n\n\n\n\n\n\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n *\n * @type {Array<'basename' | 'dirname' | 'extname' | 'history' | 'path' | 'stem'>}\n */\nconst order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname']\n\nclass VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Buffer` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (typeof value === 'string' || buffer(value)) {\n      options = {value}\n    } else if ((0,_minurl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(value)) {\n      options = {path: value}\n    } else {\n      options = value\n    }\n\n    /**\n     * Place to store custom information (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * List of filepaths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    this.cwd = _minproc_js__WEBPACK_IMPORTED_MODULE_2__.cwd()\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const prop = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        prop in options &&\n        options[prop] !== undefined &&\n        options[prop] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[prop] = prop === 'history' ? [...options[prop]] : options[prop]\n      }\n    }\n\n    /** @type {string} */\n    let prop\n\n    // Set non-path related properties.\n    for (prop in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(prop)) {\n        // @ts-expect-error: fine to set other things.\n        this[prop] = options[prop]\n      }\n    }\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {string | URL} path\n   */\n  set path(path) {\n    if ((0,_minurl_js__WEBPACK_IMPORTED_MODULE_1__.isUrl)(path)) {\n      path = (0,_minurl_js__WEBPACK_IMPORTED_MODULE_3__.fileURLToPath)(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.dirname(this.path) : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   */\n  get basename() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.basename(this.path) : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   */\n  get extname() {\n    return typeof this.path === 'string' ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.extname(this.path) : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.charCodeAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? _minpath_js__WEBPACK_IMPORTED_MODULE_4__.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = _minpath_js__WEBPACK_IMPORTED_MODULE_4__.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * @param {BufferEncoding | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Buffer`\n   *   (default: `'utf8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    return (this.value || '').toString(encoding || undefined)\n  }\n\n  /**\n   * Create a warning message associated with the file.\n   *\n   * Its `fatal` is set to `false` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(reason, place, origin) {\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_5__.VFileMessage(reason, place, origin)\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Create an info message associated with the file.\n   *\n   * Its `fatal` is set to `null` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = null\n\n    return message\n  }\n\n  /**\n   * Create a fatal error associated with the file.\n   *\n   * Its `fatal` is set to `true` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * > 👉 **Note**: a fatal error means that a file is no longer processable.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Message.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {void}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(_minpath_js__WEBPACK_IMPORTED_MODULE_4__.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + _minpath_js__WEBPACK_IMPORTED_MODULE_4__.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is a buffer.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Buffer}\n *   Whether `value` is a Node.js buffer.\n */\nfunction buffer(value) {\n  return is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmZpbGUvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsMEJBQTBCO0FBQ3ZDLGFBQWEsdUJBQXVCO0FBQ3BDLGFBQWEsa0NBQWtDO0FBQy9DLGFBQWEsNEJBQTRCO0FBQ3pDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0EsYUFBYSwyQkFBMkIsZ0RBQWdEO0FBQ3hGO0FBQ0EsYUFBYSxrSEFBa0g7QUFDL0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLDBCQUEwQjtBQUN4QztBQUNBLGNBQWMsMkJBQTJCO0FBQ3pDO0FBQ0EsY0FBYyxrQ0FBa0M7QUFDaEQ7QUFDQSxjQUFjLGlDQUFpQztBQUMvQztBQUNBLGNBQWMsMkJBQTJCO0FBQ3pDO0FBQ0EsY0FBYywyQkFBMkI7QUFDekM7QUFDQSxjQUFjLDJCQUEyQjtBQUN6QztBQUNBLGNBQWMsMkJBQTJCO0FBQ3pDO0FBQ0EsY0FBYyx5QkFBeUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFFBQVE7QUFDdEI7QUFDQSxjQUFjLGVBQWU7QUFDN0I7QUFDQSxjQUFjLGVBQWU7QUFDN0I7QUFDQSxjQUFjLG9CQUFvQjtBQUNsQztBQUNBLGNBQWMsMkJBQTJCO0FBQ3pDO0FBQ0EsY0FBYyxRQUFRO0FBQ3RCO0FBQ0EsY0FBYyxRQUFRO0FBQ3RCO0FBQ0E7QUFDQSxjQUFjLHdCQUF3QixvQkFBb0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLHlCQUF5QjtBQUN0QztBQUNBOztBQUVBO0FBQ0EsY0FBYyxrQkFBa0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0EsV0FBVyxjQUFjO0FBQ3pCO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRWtDO0FBQ1E7QUFDVDtBQUNBO0FBQ1c7O0FBRTVDO0FBQ0E7QUFDQSxLQUFLLDBCQUEwQjtBQUMvQjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLGVBQWU7QUFDakQsbUJBQW1CLGNBQWM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLCtCQUErQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxpQkFBaUI7QUFDaEM7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTixpQkFBaUI7QUFDakIsTUFBTSxTQUFTLGlEQUFLO0FBQ3BCLGlCQUFpQjtBQUNqQixNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBLHNEQUFzRDtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxlQUFlLDRDQUFROztBQUV2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsZUFBZSxRQUFRO0FBQ3ZCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGNBQWM7QUFDM0I7QUFDQTtBQUNBLFFBQVEsaURBQUs7QUFDYixhQUFhLHlEQUFTO0FBQ3RCOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLGdEQUFZO0FBQ3ZEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDZDQUFTO0FBQ3pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLGlEQUFhO0FBQ3hEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDZDQUFTO0FBQ3pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLGdEQUFZO0FBQ3ZEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdCQUFnQiw2Q0FBUztBQUN6Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpREFBYTtBQUNyQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDZDQUFTO0FBQ3pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsbUNBQW1DO0FBQ2hEO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLCtCQUErQjtBQUM1QztBQUNBLGFBQWEsdURBQXVEO0FBQ3BFO0FBQ0EsYUFBYSwyQkFBMkI7QUFDeEM7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsdURBQVk7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUM7QUFDQSxhQUFhLHVEQUF1RDtBQUNwRTtBQUNBLGFBQWEsMkJBQTJCO0FBQ3hDO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwrQkFBK0I7QUFDNUM7QUFDQSxhQUFhLHVEQUF1RDtBQUNwRTtBQUNBLGFBQWEsMkJBQTJCO0FBQ3hDO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMkJBQTJCO0FBQ3RDO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw0Q0FBUTtBQUNwQztBQUNBLDREQUE0RCw0Q0FBUTtBQUNwRTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFNBQVMsc0NBQVU7QUFDbkIiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcdmZpbGVcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCd1bmlzdCcpLk5vZGV9IE5vZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0JykuUG9zaXRpb259IFBvc2l0aW9uXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCd1bmlzdCcpLlBvaW50fSBQb2ludFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9taW51cmwuc2hhcmVkLmpzJykuVVJMfSBVUkxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL2luZGV4LmpzJykuRGF0YX0gRGF0YVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vaW5kZXguanMnKS5WYWx1ZX0gVmFsdWVcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIHtSZWNvcmQ8c3RyaW5nLCB1bmtub3duPiAmIHt0eXBlOiBzdHJpbmcsIHBvc2l0aW9uPzogUG9zaXRpb24gfCB1bmRlZmluZWR9fSBOb2RlTGlrZVxuICpcbiAqIEB0eXBlZGVmIHsnYXNjaWknIHwgJ3V0ZjgnIHwgJ3V0Zi04JyB8ICd1dGYxNmxlJyB8ICd1Y3MyJyB8ICd1Y3MtMicgfCAnYmFzZTY0JyB8ICdiYXNlNjR1cmwnIHwgJ2xhdGluMScgfCAnYmluYXJ5JyB8ICdoZXgnfSBCdWZmZXJFbmNvZGluZ1xuICogICBFbmNvZGluZ3Mgc3VwcG9ydGVkIGJ5IHRoZSBidWZmZXIgY2xhc3MuXG4gKlxuICogICBUaGlzIGlzIGEgY29weSBvZiB0aGUgdHlwZXMgZnJvbSBOb2RlLCBjb3BpZWQgdG8gcHJldmVudCBOb2RlIGdsb2JhbHMgZnJvbVxuICogICBiZWluZyBuZWVkZWQuXG4gKiAgIENvcGllZCBmcm9tOiA8aHR0cHM6Ly9naXRodWIuY29tL0RlZmluaXRlbHlUeXBlZC9EZWZpbml0ZWx5VHlwZWQvYmxvYi85MGE0ZWM4L3R5cGVzL25vZGUvYnVmZmVyLmQudHMjTDE3MD5cbiAqXG4gKiBAdHlwZWRlZiB7T3B0aW9ucyB8IFVSTCB8IFZhbHVlIHwgVkZpbGV9IENvbXBhdGlibGVcbiAqICAgVGhpbmdzIHRoYXQgY2FuIGJlIHBhc3NlZCB0byB0aGUgY29uc3RydWN0b3IuXG4gKlxuICogQHR5cGVkZWYgVkZpbGVDb3JlT3B0aW9uc1xuICogICBTZXQgbXVsdGlwbGUgdmFsdWVzLlxuICogQHByb3BlcnR5IHtWYWx1ZSB8IG51bGwgfCB1bmRlZmluZWR9IFt2YWx1ZV1cbiAqICAgU2V0IGB2YWx1ZWAuXG4gKiBAcHJvcGVydHkge3N0cmluZyB8IG51bGwgfCB1bmRlZmluZWR9IFtjd2RdXG4gKiAgIFNldCBgY3dkYC5cbiAqIEBwcm9wZXJ0eSB7QXJyYXk8c3RyaW5nPiB8IG51bGwgfCB1bmRlZmluZWR9IFtoaXN0b3J5XVxuICogICBTZXQgYGhpc3RvcnlgLlxuICogQHByb3BlcnR5IHtVUkwgfCBzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbcGF0aF1cbiAqICAgU2V0IGBwYXRoYC5cbiAqIEBwcm9wZXJ0eSB7c3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2Jhc2VuYW1lXVxuICogICBTZXQgYGJhc2VuYW1lYC5cbiAqIEBwcm9wZXJ0eSB7c3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZH0gW3N0ZW1dXG4gKiAgIFNldCBgc3RlbWAuXG4gKiBAcHJvcGVydHkge3N0cmluZyB8IG51bGwgfCB1bmRlZmluZWR9IFtleHRuYW1lXVxuICogICBTZXQgYGV4dG5hbWVgLlxuICogQHByb3BlcnR5IHtzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbZGlybmFtZV1cbiAqICAgU2V0IGBkaXJuYW1lYC5cbiAqIEBwcm9wZXJ0eSB7RGF0YSB8IG51bGwgfCB1bmRlZmluZWR9IFtkYXRhXVxuICogICBTZXQgYGRhdGFgLlxuICpcbiAqIEB0eXBlZGVmIE1hcFxuICogICBSYXcgc291cmNlIG1hcC5cbiAqXG4gKiAgIFNlZTpcbiAqICAgPGh0dHBzOi8vZ2l0aHViLmNvbS9tb3ppbGxhL3NvdXJjZS1tYXAvYmxvYi81ODgxOWYwL3NvdXJjZS1tYXAuZC50cyNMMTUtTDIzPi5cbiAqIEBwcm9wZXJ0eSB7bnVtYmVyfSB2ZXJzaW9uXG4gKiAgIFdoaWNoIHZlcnNpb24gb2YgdGhlIHNvdXJjZSBtYXAgc3BlYyB0aGlzIG1hcCBpcyBmb2xsb3dpbmcuXG4gKiBAcHJvcGVydHkge0FycmF5PHN0cmluZz59IHNvdXJjZXNcbiAqICAgQW4gYXJyYXkgb2YgVVJMcyB0byB0aGUgb3JpZ2luYWwgc291cmNlIGZpbGVzLlxuICogQHByb3BlcnR5IHtBcnJheTxzdHJpbmc+fSBuYW1lc1xuICogICBBbiBhcnJheSBvZiBpZGVudGlmaWVycyB3aGljaCBjYW4gYmUgcmVmZXJlbmNlZCBieSBpbmRpdmlkdWFsIG1hcHBpbmdzLlxuICogQHByb3BlcnR5IHtzdHJpbmcgfCB1bmRlZmluZWR9IFtzb3VyY2VSb290XVxuICogICBUaGUgVVJMIHJvb3QgZnJvbSB3aGljaCBhbGwgc291cmNlcyBhcmUgcmVsYXRpdmUuXG4gKiBAcHJvcGVydHkge0FycmF5PHN0cmluZz4gfCB1bmRlZmluZWR9IFtzb3VyY2VzQ29udGVudF1cbiAqICAgQW4gYXJyYXkgb2YgY29udGVudHMgb2YgdGhlIG9yaWdpbmFsIHNvdXJjZSBmaWxlcy5cbiAqIEBwcm9wZXJ0eSB7c3RyaW5nfSBtYXBwaW5nc1xuICogICBBIHN0cmluZyBvZiBiYXNlNjQgVkxRcyB3aGljaCBjb250YWluIHRoZSBhY3R1YWwgbWFwcGluZ3MuXG4gKiBAcHJvcGVydHkge3N0cmluZ30gZmlsZVxuICogICBUaGUgZ2VuZXJhdGVkIGZpbGUgdGhpcyBzb3VyY2UgbWFwIGlzIGFzc29jaWF0ZWQgd2l0aC5cbiAqXG4gKiBAdHlwZWRlZiB7e1trZXk6IHN0cmluZ106IHVua25vd259ICYgVkZpbGVDb3JlT3B0aW9uc30gT3B0aW9uc1xuICogICBDb25maWd1cmF0aW9uLlxuICpcbiAqICAgQSBidW5jaCBvZiBrZXlzIHRoYXQgd2lsbCBiZSBzaGFsbG93IGNvcGllZCBvdmVyIHRvIHRoZSBuZXcgZmlsZS5cbiAqXG4gKiBAdHlwZWRlZiB7UmVjb3JkPHN0cmluZywgdW5rbm93bj59IFJlcG9ydGVyU2V0dGluZ3NcbiAqICAgQ29uZmlndXJhdGlvbiBmb3IgcmVwb3J0ZXJzLlxuICovXG5cbi8qKlxuICogQHRlbXBsYXRlIHtSZXBvcnRlclNldHRpbmdzfSBTZXR0aW5nc1xuICogICBPcHRpb25zIHR5cGUuXG4gKiBAY2FsbGJhY2sgUmVwb3J0ZXJcbiAqICAgVHlwZSBmb3IgYSByZXBvcnRlci5cbiAqIEBwYXJhbSB7QXJyYXk8VkZpbGU+fSBmaWxlc1xuICogICBGaWxlcyB0byByZXBvcnQuXG4gKiBAcGFyYW0ge1NldHRpbmdzfSBvcHRpb25zXG4gKiAgIENvbmZpZ3VyYXRpb24uXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBSZXBvcnQuXG4gKi9cblxuaW1wb3J0IGJ1ZmZlckxpa2UgZnJvbSAnaXMtYnVmZmVyJ1xuaW1wb3J0IHtWRmlsZU1lc3NhZ2V9IGZyb20gJ3ZmaWxlLW1lc3NhZ2UnXG5pbXBvcnQge3BhdGh9IGZyb20gJy4vbWlucGF0aC5qcydcbmltcG9ydCB7cHJvY30gZnJvbSAnLi9taW5wcm9jLmpzJ1xuaW1wb3J0IHt1cmxUb1BhdGgsIGlzVXJsfSBmcm9tICcuL21pbnVybC5qcydcblxuLyoqXG4gKiBPcmRlciBvZiBzZXR0aW5nIChsZWFzdCBzcGVjaWZpYyB0byBtb3N0KSwgd2UgbmVlZCB0aGlzIGJlY2F1c2Ugb3RoZXJ3aXNlXG4gKiBge3N0ZW06ICdhJywgcGF0aDogJ34vYi5qcyd9YCB3b3VsZCB0aHJvdywgYXMgYSBwYXRoIGlzIG5lZWRlZCBiZWZvcmUgYVxuICogc3RlbSBjYW4gYmUgc2V0LlxuICpcbiAqIEB0eXBlIHtBcnJheTwnYmFzZW5hbWUnIHwgJ2Rpcm5hbWUnIHwgJ2V4dG5hbWUnIHwgJ2hpc3RvcnknIHwgJ3BhdGgnIHwgJ3N0ZW0nPn1cbiAqL1xuY29uc3Qgb3JkZXIgPSBbJ2hpc3RvcnknLCAncGF0aCcsICdiYXNlbmFtZScsICdzdGVtJywgJ2V4dG5hbWUnLCAnZGlybmFtZSddXG5cbmV4cG9ydCBjbGFzcyBWRmlsZSB7XG4gIC8qKlxuICAgKiBDcmVhdGUgYSBuZXcgdmlydHVhbCBmaWxlLlxuICAgKlxuICAgKiBgb3B0aW9uc2AgaXMgdHJlYXRlZCBhczpcbiAgICpcbiAgICogKiAgIGBzdHJpbmdgIG9yIGBCdWZmZXJgIOKAlCBge3ZhbHVlOiBvcHRpb25zfWBcbiAgICogKiAgIGBVUkxgIOKAlCBge3BhdGg6IG9wdGlvbnN9YFxuICAgKiAqICAgYFZGaWxlYCDigJQgc2hhbGxvdyBjb3BpZXMgaXRzIGRhdGEgb3ZlciB0byB0aGUgbmV3IGZpbGVcbiAgICogKiAgIGBvYmplY3RgIOKAlCBhbGwgZmllbGRzIGFyZSBzaGFsbG93IGNvcGllZCBvdmVyIHRvIHRoZSBuZXcgZmlsZVxuICAgKlxuICAgKiBQYXRoIHJlbGF0ZWQgZmllbGRzIGFyZSBzZXQgaW4gdGhlIGZvbGxvd2luZyBvcmRlciAobGVhc3Qgc3BlY2lmaWMgdG9cbiAgICogbW9zdCBzcGVjaWZpYyk6IGBoaXN0b3J5YCwgYHBhdGhgLCBgYmFzZW5hbWVgLCBgc3RlbWAsIGBleHRuYW1lYCxcbiAgICogYGRpcm5hbWVgLlxuICAgKlxuICAgKiBZb3UgY2Fubm90IHNldCBgZGlybmFtZWAgb3IgYGV4dG5hbWVgIHdpdGhvdXQgc2V0dGluZyBlaXRoZXIgYGhpc3RvcnlgLFxuICAgKiBgcGF0aGAsIGBiYXNlbmFtZWAsIG9yIGBzdGVtYCB0b28uXG4gICAqXG4gICAqIEBwYXJhbSB7Q29tcGF0aWJsZSB8IG51bGwgfCB1bmRlZmluZWR9IFt2YWx1ZV1cbiAgICogICBGaWxlIHZhbHVlLlxuICAgKiBAcmV0dXJuc1xuICAgKiAgIE5ldyBpbnN0YW5jZS5cbiAgICovXG4gIGNvbnN0cnVjdG9yKHZhbHVlKSB7XG4gICAgLyoqIEB0eXBlIHtPcHRpb25zIHwgVkZpbGV9ICovXG4gICAgbGV0IG9wdGlvbnNcblxuICAgIGlmICghdmFsdWUpIHtcbiAgICAgIG9wdGlvbnMgPSB7fVxuICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fCBidWZmZXIodmFsdWUpKSB7XG4gICAgICBvcHRpb25zID0ge3ZhbHVlfVxuICAgIH0gZWxzZSBpZiAoaXNVcmwodmFsdWUpKSB7XG4gICAgICBvcHRpb25zID0ge3BhdGg6IHZhbHVlfVxuICAgIH0gZWxzZSB7XG4gICAgICBvcHRpb25zID0gdmFsdWVcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBQbGFjZSB0byBzdG9yZSBjdXN0b20gaW5mb3JtYXRpb24gKGRlZmF1bHQ6IGB7fWApLlxuICAgICAqXG4gICAgICogSXTigJlzIE9LIHRvIHN0b3JlIGN1c3RvbSBkYXRhIGRpcmVjdGx5IG9uIHRoZSBmaWxlIGJ1dCBtb3ZpbmcgaXQgdG9cbiAgICAgKiBgZGF0YWAgaXMgcmVjb21tZW5kZWQuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7RGF0YX1cbiAgICAgKi9cbiAgICB0aGlzLmRhdGEgPSB7fVxuXG4gICAgLyoqXG4gICAgICogTGlzdCBvZiBtZXNzYWdlcyBhc3NvY2lhdGVkIHdpdGggdGhlIGZpbGUuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7QXJyYXk8VkZpbGVNZXNzYWdlPn1cbiAgICAgKi9cbiAgICB0aGlzLm1lc3NhZ2VzID0gW11cblxuICAgIC8qKlxuICAgICAqIExpc3Qgb2YgZmlsZXBhdGhzIHRoZSBmaWxlIG1vdmVkIGJldHdlZW4uXG4gICAgICpcbiAgICAgKiBUaGUgZmlyc3QgaXMgdGhlIG9yaWdpbmFsIHBhdGggYW5kIHRoZSBsYXN0IGlzIHRoZSBjdXJyZW50IHBhdGguXG4gICAgICpcbiAgICAgKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn1cbiAgICAgKi9cbiAgICB0aGlzLmhpc3RvcnkgPSBbXVxuXG4gICAgLyoqXG4gICAgICogQmFzZSBvZiBgcGF0aGAgKGRlZmF1bHQ6IGBwcm9jZXNzLmN3ZCgpYCBvciBgJy8nYCBpbiBicm93c2VycykuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7c3RyaW5nfVxuICAgICAqL1xuICAgIHRoaXMuY3dkID0gcHJvYy5jd2QoKVxuXG4gICAgLyogZXNsaW50LWRpc2FibGUgbm8tdW51c2VkLWV4cHJlc3Npb25zICovXG4gICAgLyoqXG4gICAgICogUmF3IHZhbHVlLlxuICAgICAqXG4gICAgICogQHR5cGUge1ZhbHVlfVxuICAgICAqL1xuICAgIHRoaXMudmFsdWVcblxuICAgIC8vIFRoZSBiZWxvdyBhcmUgbm9uLXN0YW5kYXJkLCB0aGV5IGFyZSDigJx3ZWxsLWtub3du4oCdLlxuICAgIC8vIEFzIGluLCB1c2VkIGluIHNldmVyYWwgdG9vbHMuXG5cbiAgICAvKipcbiAgICAgKiBXaGV0aGVyIGEgZmlsZSB3YXMgc2F2ZWQgdG8gZGlzay5cbiAgICAgKlxuICAgICAqIFRoaXMgaXMgdXNlZCBieSB2ZmlsZSByZXBvcnRlcnMuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7Ym9vbGVhbn1cbiAgICAgKi9cbiAgICB0aGlzLnN0b3JlZFxuXG4gICAgLyoqXG4gICAgICogQ3VzdG9tLCBub24tc3RyaW5nLCBjb21waWxlZCwgcmVwcmVzZW50YXRpb24uXG4gICAgICpcbiAgICAgKiBUaGlzIGlzIHVzZWQgYnkgdW5pZmllZCB0byBzdG9yZSBub24tc3RyaW5nIHJlc3VsdHMuXG4gICAgICogT25lIGV4YW1wbGUgaXMgd2hlbiB0dXJuaW5nIG1hcmtkb3duIGludG8gUmVhY3Qgbm9kZXMuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7dW5rbm93bn1cbiAgICAgKi9cbiAgICB0aGlzLnJlc3VsdFxuXG4gICAgLyoqXG4gICAgICogU291cmNlIG1hcC5cbiAgICAgKlxuICAgICAqIFRoaXMgdHlwZSBpcyBlcXVpdmFsZW50IHRvIHRoZSBgUmF3U291cmNlTWFwYCB0eXBlIGZyb20gdGhlIGBzb3VyY2UtbWFwYFxuICAgICAqIG1vZHVsZS5cbiAgICAgKlxuICAgICAqIEB0eXBlIHtNYXAgfCBudWxsIHwgdW5kZWZpbmVkfVxuICAgICAqL1xuICAgIHRoaXMubWFwXG4gICAgLyogZXNsaW50LWVuYWJsZSBuby11bnVzZWQtZXhwcmVzc2lvbnMgKi9cblxuICAgIC8vIFNldCBwYXRoIHJlbGF0ZWQgcHJvcGVydGllcyBpbiB0aGUgY29ycmVjdCBvcmRlci5cbiAgICBsZXQgaW5kZXggPSAtMVxuXG4gICAgd2hpbGUgKCsraW5kZXggPCBvcmRlci5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IHByb3AgPSBvcmRlcltpbmRleF1cblxuICAgICAgLy8gTm90ZTogd2Ugc3BlY2lmaWNhbGx5IHVzZSBgaW5gIGluc3RlYWQgb2YgYGhhc093blByb3BlcnR5YCB0byBhY2NlcHRcbiAgICAgIC8vIGB2ZmlsZWBzIHRvby5cbiAgICAgIGlmIChcbiAgICAgICAgcHJvcCBpbiBvcHRpb25zICYmXG4gICAgICAgIG9wdGlvbnNbcHJvcF0gIT09IHVuZGVmaW5lZCAmJlxuICAgICAgICBvcHRpb25zW3Byb3BdICE9PSBudWxsXG4gICAgICApIHtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogVFMgZG9lc27igJl0IHVuZGVyc3RhbmQgYmFzaWMgcmVhbGl0eS5cbiAgICAgICAgdGhpc1twcm9wXSA9IHByb3AgPT09ICdoaXN0b3J5JyA/IFsuLi5vcHRpb25zW3Byb3BdXSA6IG9wdGlvbnNbcHJvcF1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICBsZXQgcHJvcFxuXG4gICAgLy8gU2V0IG5vbi1wYXRoIHJlbGF0ZWQgcHJvcGVydGllcy5cbiAgICBmb3IgKHByb3AgaW4gb3B0aW9ucykge1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogZmluZSB0byBzZXQgb3RoZXIgdGhpbmdzLlxuICAgICAgaWYgKCFvcmRlci5pbmNsdWRlcyhwcm9wKSkge1xuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBmaW5lIHRvIHNldCBvdGhlciB0aGluZ3MuXG4gICAgICAgIHRoaXNbcHJvcF0gPSBvcHRpb25zW3Byb3BdXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCB0aGUgZnVsbCBwYXRoIChleGFtcGxlOiBgJ34vaW5kZXgubWluLmpzJ2ApLlxuICAgKlxuICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgKi9cbiAgZ2V0IHBhdGgoKSB7XG4gICAgcmV0dXJuIHRoaXMuaGlzdG9yeVt0aGlzLmhpc3RvcnkubGVuZ3RoIC0gMV1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZXQgdGhlIGZ1bGwgcGF0aCAoZXhhbXBsZTogYCd+L2luZGV4Lm1pbi5qcydgKS5cbiAgICpcbiAgICogQ2Fubm90IGJlIG51bGxpZmllZC5cbiAgICogWW91IGNhbiBzZXQgYSBmaWxlIFVSTCAoYSBgVVJMYCBvYmplY3Qgd2l0aCBhIGBmaWxlOmAgcHJvdG9jb2wpIHdoaWNoIHdpbGxcbiAgICogYmUgdHVybmVkIGludG8gYSBwYXRoIHdpdGggYHVybC5maWxlVVJMVG9QYXRoYC5cbiAgICpcbiAgICogQHBhcmFtIHtzdHJpbmcgfCBVUkx9IHBhdGhcbiAgICovXG4gIHNldCBwYXRoKHBhdGgpIHtcbiAgICBpZiAoaXNVcmwocGF0aCkpIHtcbiAgICAgIHBhdGggPSB1cmxUb1BhdGgocGF0aClcbiAgICB9XG5cbiAgICBhc3NlcnROb25FbXB0eShwYXRoLCAncGF0aCcpXG5cbiAgICBpZiAodGhpcy5wYXRoICE9PSBwYXRoKSB7XG4gICAgICB0aGlzLmhpc3RvcnkucHVzaChwYXRoKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgdGhlIHBhcmVudCBwYXRoIChleGFtcGxlOiBgJ34nYCkuXG4gICAqL1xuICBnZXQgZGlybmFtZSgpIHtcbiAgICByZXR1cm4gdHlwZW9mIHRoaXMucGF0aCA9PT0gJ3N0cmluZycgPyBwYXRoLmRpcm5hbWUodGhpcy5wYXRoKSA6IHVuZGVmaW5lZFxuICB9XG5cbiAgLyoqXG4gICAqIFNldCB0aGUgcGFyZW50IHBhdGggKGV4YW1wbGU6IGAnfidgKS5cbiAgICpcbiAgICogQ2Fubm90IGJlIHNldCBpZiB0aGVyZeKAmXMgbm8gYHBhdGhgIHlldC5cbiAgICovXG4gIHNldCBkaXJuYW1lKGRpcm5hbWUpIHtcbiAgICBhc3NlcnRQYXRoKHRoaXMuYmFzZW5hbWUsICdkaXJuYW1lJylcbiAgICB0aGlzLnBhdGggPSBwYXRoLmpvaW4oZGlybmFtZSB8fCAnJywgdGhpcy5iYXNlbmFtZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgdGhlIGJhc2VuYW1lIChpbmNsdWRpbmcgZXh0bmFtZSkgKGV4YW1wbGU6IGAnaW5kZXgubWluLmpzJ2ApLlxuICAgKi9cbiAgZ2V0IGJhc2VuYW1lKCkge1xuICAgIHJldHVybiB0eXBlb2YgdGhpcy5wYXRoID09PSAnc3RyaW5nJyA/IHBhdGguYmFzZW5hbWUodGhpcy5wYXRoKSA6IHVuZGVmaW5lZFxuICB9XG5cbiAgLyoqXG4gICAqIFNldCBiYXNlbmFtZSAoaW5jbHVkaW5nIGV4dG5hbWUpIChgJ2luZGV4Lm1pbi5qcydgKS5cbiAgICpcbiAgICogQ2Fubm90IGNvbnRhaW4gcGF0aCBzZXBhcmF0b3JzIChgJy8nYCBvbiB1bml4LCBtYWNPUywgYW5kIGJyb3dzZXJzLCBgJ1xcJ2BcbiAgICogb24gd2luZG93cykuXG4gICAqIENhbm5vdCBiZSBudWxsaWZpZWQgKHVzZSBgZmlsZS5wYXRoID0gZmlsZS5kaXJuYW1lYCBpbnN0ZWFkKS5cbiAgICovXG4gIHNldCBiYXNlbmFtZShiYXNlbmFtZSkge1xuICAgIGFzc2VydE5vbkVtcHR5KGJhc2VuYW1lLCAnYmFzZW5hbWUnKVxuICAgIGFzc2VydFBhcnQoYmFzZW5hbWUsICdiYXNlbmFtZScpXG4gICAgdGhpcy5wYXRoID0gcGF0aC5qb2luKHRoaXMuZGlybmFtZSB8fCAnJywgYmFzZW5hbWUpXG4gIH1cblxuICAvKipcbiAgICogR2V0IHRoZSBleHRuYW1lIChpbmNsdWRpbmcgZG90KSAoZXhhbXBsZTogYCcuanMnYCkuXG4gICAqL1xuICBnZXQgZXh0bmFtZSgpIHtcbiAgICByZXR1cm4gdHlwZW9mIHRoaXMucGF0aCA9PT0gJ3N0cmluZycgPyBwYXRoLmV4dG5hbWUodGhpcy5wYXRoKSA6IHVuZGVmaW5lZFxuICB9XG5cbiAgLyoqXG4gICAqIFNldCB0aGUgZXh0bmFtZSAoaW5jbHVkaW5nIGRvdCkgKGV4YW1wbGU6IGAnLmpzJ2ApLlxuICAgKlxuICAgKiBDYW5ub3QgY29udGFpbiBwYXRoIHNlcGFyYXRvcnMgKGAnLydgIG9uIHVuaXgsIG1hY09TLCBhbmQgYnJvd3NlcnMsIGAnXFwnYFxuICAgKiBvbiB3aW5kb3dzKS5cbiAgICogQ2Fubm90IGJlIHNldCBpZiB0aGVyZeKAmXMgbm8gYHBhdGhgIHlldC5cbiAgICovXG4gIHNldCBleHRuYW1lKGV4dG5hbWUpIHtcbiAgICBhc3NlcnRQYXJ0KGV4dG5hbWUsICdleHRuYW1lJylcbiAgICBhc3NlcnRQYXRoKHRoaXMuZGlybmFtZSwgJ2V4dG5hbWUnKVxuXG4gICAgaWYgKGV4dG5hbWUpIHtcbiAgICAgIGlmIChleHRuYW1lLmNoYXJDb2RlQXQoMCkgIT09IDQ2IC8qIGAuYCAqLykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2BleHRuYW1lYCBtdXN0IHN0YXJ0IHdpdGggYC5gJylcbiAgICAgIH1cblxuICAgICAgaWYgKGV4dG5hbWUuaW5jbHVkZXMoJy4nLCAxKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2BleHRuYW1lYCBjYW5ub3QgY29udGFpbiBtdWx0aXBsZSBkb3RzJylcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLnBhdGggPSBwYXRoLmpvaW4odGhpcy5kaXJuYW1lLCB0aGlzLnN0ZW0gKyAoZXh0bmFtZSB8fCAnJykpXG4gIH1cblxuICAvKipcbiAgICogR2V0IHRoZSBzdGVtIChiYXNlbmFtZSB3L28gZXh0bmFtZSkgKGV4YW1wbGU6IGAnaW5kZXgubWluJ2ApLlxuICAgKi9cbiAgZ2V0IHN0ZW0oKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB0aGlzLnBhdGggPT09ICdzdHJpbmcnXG4gICAgICA/IHBhdGguYmFzZW5hbWUodGhpcy5wYXRoLCB0aGlzLmV4dG5hbWUpXG4gICAgICA6IHVuZGVmaW5lZFxuICB9XG5cbiAgLyoqXG4gICAqIFNldCB0aGUgc3RlbSAoYmFzZW5hbWUgdy9vIGV4dG5hbWUpIChleGFtcGxlOiBgJ2luZGV4Lm1pbidgKS5cbiAgICpcbiAgICogQ2Fubm90IGNvbnRhaW4gcGF0aCBzZXBhcmF0b3JzIChgJy8nYCBvbiB1bml4LCBtYWNPUywgYW5kIGJyb3dzZXJzLCBgJ1xcJ2BcbiAgICogb24gd2luZG93cykuXG4gICAqIENhbm5vdCBiZSBudWxsaWZpZWQgKHVzZSBgZmlsZS5wYXRoID0gZmlsZS5kaXJuYW1lYCBpbnN0ZWFkKS5cbiAgICovXG4gIHNldCBzdGVtKHN0ZW0pIHtcbiAgICBhc3NlcnROb25FbXB0eShzdGVtLCAnc3RlbScpXG4gICAgYXNzZXJ0UGFydChzdGVtLCAnc3RlbScpXG4gICAgdGhpcy5wYXRoID0gcGF0aC5qb2luKHRoaXMuZGlybmFtZSB8fCAnJywgc3RlbSArICh0aGlzLmV4dG5hbWUgfHwgJycpKVxuICB9XG5cbiAgLyoqXG4gICAqIFNlcmlhbGl6ZSB0aGUgZmlsZS5cbiAgICpcbiAgICogQHBhcmFtIHtCdWZmZXJFbmNvZGluZyB8IG51bGwgfCB1bmRlZmluZWR9IFtlbmNvZGluZz0ndXRmOCddXG4gICAqICAgQ2hhcmFjdGVyIGVuY29kaW5nIHRvIHVuZGVyc3RhbmQgYHZhbHVlYCBhcyB3aGVuIGl04oCZcyBhIGBCdWZmZXJgXG4gICAqICAgKGRlZmF1bHQ6IGAndXRmOCdgKS5cbiAgICogQHJldHVybnMge3N0cmluZ31cbiAgICogICBTZXJpYWxpemVkIGZpbGUuXG4gICAqL1xuICB0b1N0cmluZyhlbmNvZGluZykge1xuICAgIHJldHVybiAodGhpcy52YWx1ZSB8fCAnJykudG9TdHJpbmcoZW5jb2RpbmcgfHwgdW5kZWZpbmVkKVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhIHdhcm5pbmcgbWVzc2FnZSBhc3NvY2lhdGVkIHdpdGggdGhlIGZpbGUuXG4gICAqXG4gICAqIEl0cyBgZmF0YWxgIGlzIHNldCB0byBgZmFsc2VgIGFuZCBgZmlsZWAgaXMgc2V0IHRvIHRoZSBjdXJyZW50IGZpbGUgcGF0aC5cbiAgICogSXRzIGFkZGVkIHRvIGBmaWxlLm1lc3NhZ2VzYC5cbiAgICpcbiAgICogQHBhcmFtIHtzdHJpbmcgfCBFcnJvciB8IFZGaWxlTWVzc2FnZX0gcmVhc29uXG4gICAqICAgUmVhc29uIGZvciBtZXNzYWdlLCB1c2VzIHRoZSBzdGFjayBhbmQgbWVzc2FnZSBvZiB0aGUgZXJyb3IgaWYgZ2l2ZW4uXG4gICAqIEBwYXJhbSB7Tm9kZSB8IE5vZGVMaWtlIHwgUG9zaXRpb24gfCBQb2ludCB8IG51bGwgfCB1bmRlZmluZWR9IFtwbGFjZV1cbiAgICogICBQbGFjZSBpbiBmaWxlIHdoZXJlIHRoZSBtZXNzYWdlIG9jY3VycmVkLlxuICAgKiBAcGFyYW0ge3N0cmluZyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcmlnaW5dXG4gICAqICAgUGxhY2UgaW4gY29kZSB3aGVyZSB0aGUgbWVzc2FnZSBvcmlnaW5hdGVzIChleGFtcGxlOlxuICAgKiAgIGAnbXktcGFja2FnZTpteS1ydWxlJ2Agb3IgYCdteS1ydWxlJ2ApLlxuICAgKiBAcmV0dXJucyB7VkZpbGVNZXNzYWdlfVxuICAgKiAgIE1lc3NhZ2UuXG4gICAqL1xuICBtZXNzYWdlKHJlYXNvbiwgcGxhY2UsIG9yaWdpbikge1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBuZXcgVkZpbGVNZXNzYWdlKHJlYXNvbiwgcGxhY2UsIG9yaWdpbilcblxuICAgIGlmICh0aGlzLnBhdGgpIHtcbiAgICAgIG1lc3NhZ2UubmFtZSA9IHRoaXMucGF0aCArICc6JyArIG1lc3NhZ2UubmFtZVxuICAgICAgbWVzc2FnZS5maWxlID0gdGhpcy5wYXRoXG4gICAgfVxuXG4gICAgbWVzc2FnZS5mYXRhbCA9IGZhbHNlXG5cbiAgICB0aGlzLm1lc3NhZ2VzLnB1c2gobWVzc2FnZSlcblxuICAgIHJldHVybiBtZXNzYWdlXG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIGFuIGluZm8gbWVzc2FnZSBhc3NvY2lhdGVkIHdpdGggdGhlIGZpbGUuXG4gICAqXG4gICAqIEl0cyBgZmF0YWxgIGlzIHNldCB0byBgbnVsbGAgYW5kIGBmaWxlYCBpcyBzZXQgdG8gdGhlIGN1cnJlbnQgZmlsZSBwYXRoLlxuICAgKiBJdHMgYWRkZWQgdG8gYGZpbGUubWVzc2FnZXNgLlxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZyB8IEVycm9yIHwgVkZpbGVNZXNzYWdlfSByZWFzb25cbiAgICogICBSZWFzb24gZm9yIG1lc3NhZ2UsIHVzZXMgdGhlIHN0YWNrIGFuZCBtZXNzYWdlIG9mIHRoZSBlcnJvciBpZiBnaXZlbi5cbiAgICogQHBhcmFtIHtOb2RlIHwgTm9kZUxpa2UgfCBQb3NpdGlvbiB8IFBvaW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BsYWNlXVxuICAgKiAgIFBsYWNlIGluIGZpbGUgd2hlcmUgdGhlIG1lc3NhZ2Ugb2NjdXJyZWQuXG4gICAqIEBwYXJhbSB7c3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZH0gW29yaWdpbl1cbiAgICogICBQbGFjZSBpbiBjb2RlIHdoZXJlIHRoZSBtZXNzYWdlIG9yaWdpbmF0ZXMgKGV4YW1wbGU6XG4gICAqICAgYCdteS1wYWNrYWdlOm15LXJ1bGUnYCBvciBgJ215LXJ1bGUnYCkuXG4gICAqIEByZXR1cm5zIHtWRmlsZU1lc3NhZ2V9XG4gICAqICAgTWVzc2FnZS5cbiAgICovXG4gIGluZm8ocmVhc29uLCBwbGFjZSwgb3JpZ2luKSB7XG4gICAgY29uc3QgbWVzc2FnZSA9IHRoaXMubWVzc2FnZShyZWFzb24sIHBsYWNlLCBvcmlnaW4pXG5cbiAgICBtZXNzYWdlLmZhdGFsID0gbnVsbFxuXG4gICAgcmV0dXJuIG1lc3NhZ2VcbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgYSBmYXRhbCBlcnJvciBhc3NvY2lhdGVkIHdpdGggdGhlIGZpbGUuXG4gICAqXG4gICAqIEl0cyBgZmF0YWxgIGlzIHNldCB0byBgdHJ1ZWAgYW5kIGBmaWxlYCBpcyBzZXQgdG8gdGhlIGN1cnJlbnQgZmlsZSBwYXRoLlxuICAgKiBJdHMgYWRkZWQgdG8gYGZpbGUubWVzc2FnZXNgLlxuICAgKlxuICAgKiA+IPCfkYkgKipOb3RlKio6IGEgZmF0YWwgZXJyb3IgbWVhbnMgdGhhdCBhIGZpbGUgaXMgbm8gbG9uZ2VyIHByb2Nlc3NhYmxlLlxuICAgKlxuICAgKiBAcGFyYW0ge3N0cmluZyB8IEVycm9yIHwgVkZpbGVNZXNzYWdlfSByZWFzb25cbiAgICogICBSZWFzb24gZm9yIG1lc3NhZ2UsIHVzZXMgdGhlIHN0YWNrIGFuZCBtZXNzYWdlIG9mIHRoZSBlcnJvciBpZiBnaXZlbi5cbiAgICogQHBhcmFtIHtOb2RlIHwgTm9kZUxpa2UgfCBQb3NpdGlvbiB8IFBvaW50IHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BsYWNlXVxuICAgKiAgIFBsYWNlIGluIGZpbGUgd2hlcmUgdGhlIG1lc3NhZ2Ugb2NjdXJyZWQuXG4gICAqIEBwYXJhbSB7c3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZH0gW29yaWdpbl1cbiAgICogICBQbGFjZSBpbiBjb2RlIHdoZXJlIHRoZSBtZXNzYWdlIG9yaWdpbmF0ZXMgKGV4YW1wbGU6XG4gICAqICAgYCdteS1wYWNrYWdlOm15LXJ1bGUnYCBvciBgJ215LXJ1bGUnYCkuXG4gICAqIEByZXR1cm5zIHtuZXZlcn1cbiAgICogICBNZXNzYWdlLlxuICAgKiBAdGhyb3dzIHtWRmlsZU1lc3NhZ2V9XG4gICAqICAgTWVzc2FnZS5cbiAgICovXG4gIGZhaWwocmVhc29uLCBwbGFjZSwgb3JpZ2luKSB7XG4gICAgY29uc3QgbWVzc2FnZSA9IHRoaXMubWVzc2FnZShyZWFzb24sIHBsYWNlLCBvcmlnaW4pXG5cbiAgICBtZXNzYWdlLmZhdGFsID0gdHJ1ZVxuXG4gICAgdGhyb3cgbWVzc2FnZVxuICB9XG59XG5cbi8qKlxuICogQXNzZXJ0IHRoYXQgYHBhcnRgIGlzIG5vdCBhIHBhdGggKGFzIGluLCBkb2VzIG5vdCBjb250YWluIGBwYXRoLnNlcGApLlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZH0gcGFydFxuICogICBGaWxlIHBhdGggcGFydC5cbiAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gKiAgIFBhcnQgbmFtZS5cbiAqIEByZXR1cm5zIHt2b2lkfVxuICogICBOb3RoaW5nLlxuICovXG5mdW5jdGlvbiBhc3NlcnRQYXJ0KHBhcnQsIG5hbWUpIHtcbiAgaWYgKHBhcnQgJiYgcGFydC5pbmNsdWRlcyhwYXRoLnNlcCkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnYCcgKyBuYW1lICsgJ2AgY2Fubm90IGJlIGEgcGF0aDogZGlkIG5vdCBleHBlY3QgYCcgKyBwYXRoLnNlcCArICdgJ1xuICAgIClcbiAgfVxufVxuXG4vKipcbiAqIEFzc2VydCB0aGF0IGBwYXJ0YCBpcyBub3QgZW1wdHkuXG4gKlxuICogQHBhcmFtIHtzdHJpbmcgfCB1bmRlZmluZWR9IHBhcnRcbiAqICAgVGhpbmcuXG4gKiBAcGFyYW0ge3N0cmluZ30gbmFtZVxuICogICBQYXJ0IG5hbWUuXG4gKiBAcmV0dXJucyB7YXNzZXJ0cyBwYXJ0IGlzIHN0cmluZ31cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gYXNzZXJ0Tm9uRW1wdHkocGFydCwgbmFtZSkge1xuICBpZiAoIXBhcnQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ2AnICsgbmFtZSArICdgIGNhbm5vdCBiZSBlbXB0eScpXG4gIH1cbn1cblxuLyoqXG4gKiBBc3NlcnQgYHBhdGhgIGV4aXN0cy5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZyB8IHVuZGVmaW5lZH0gcGF0aFxuICogICBQYXRoLlxuICogQHBhcmFtIHtzdHJpbmd9IG5hbWVcbiAqICAgRGVwZW5kZW5jeSBuYW1lLlxuICogQHJldHVybnMge2Fzc2VydHMgcGF0aCBpcyBzdHJpbmd9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIGFzc2VydFBhdGgocGF0aCwgbmFtZSkge1xuICBpZiAoIXBhdGgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1NldHRpbmcgYCcgKyBuYW1lICsgJ2AgcmVxdWlyZXMgYHBhdGhgIHRvIGJlIHNldCB0b28nKVxuICB9XG59XG5cbi8qKlxuICogQXNzZXJ0IGB2YWx1ZWAgaXMgYSBidWZmZXIuXG4gKlxuICogQHBhcmFtIHt1bmtub3dufSB2YWx1ZVxuICogICB0aGluZy5cbiAqIEByZXR1cm5zIHt2YWx1ZSBpcyBCdWZmZXJ9XG4gKiAgIFdoZXRoZXIgYHZhbHVlYCBpcyBhIE5vZGUuanMgYnVmZmVyLlxuICovXG5mdW5jdGlvbiBidWZmZXIodmFsdWUpIHtcbiAgcmV0dXJuIGJ1ZmZlckxpa2UodmFsdWUpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/minurl.shared.js":
/*!*************************************************!*\
  !*** ./node_modules/vfile/lib/minurl.shared.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: () => (/* binding */ isUrl)\n/* harmony export */ });\n/**\n * @typedef URL\n * @property {string} hash\n * @property {string} host\n * @property {string} hostname\n * @property {string} href\n * @property {string} origin\n * @property {string} password\n * @property {string} pathname\n * @property {string} port\n * @property {string} protocol\n * @property {string} search\n * @property {any} searchParams\n * @property {string} username\n * @property {() => string} toString\n * @property {() => string} toJSON\n */\n\n/**\n * Check if `fileUrlOrPath` looks like a URL.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js#L1501>\nfunction isUrl(fileUrlOrPath) {\n  return (\n    fileUrlOrPath !== null &&\n    typeof fileUrlOrPath === 'object' &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.href &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.origin\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/minurl.shared.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/node_modules/unist-util-stringify-position/lib/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/vfile/node_modules/unist-util-stringify-position/lib/index.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringifyPosition: () => (/* binding */ stringifyPosition)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Position | PositionLike | Point | PointLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nfunction stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/node_modules/unist-util-stringify-position/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/node_modules/vfile-message/lib/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/vfile/node_modules/vfile-message/lib/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFileMessage: () => (/* binding */ VFileMessage)\n/* harmony export */ });\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/vfile/node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\n\n\n/**\n * Message.\n */\nclass VFileMessage extends Error {\n  /**\n   * Create a message for `reason` at `place` from `origin`.\n   *\n   * When an error is passed in as `reason`, the `stack` is copied.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   *\n   *   > 👉 **Note**: you should use markdown.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // To do: next major: expose `undefined` everywhere instead of `null`.\n  constructor(reason, place, origin) {\n    /** @type {[string | null, string | null]} */\n    const parts = [null, null]\n    /** @type {Position} */\n    let position = {\n      // @ts-expect-error: we always follows the structure of `position`.\n      start: {line: null, column: null},\n      // @ts-expect-error: \"\n      end: {line: null, column: null}\n    }\n\n    super()\n\n    if (typeof place === 'string') {\n      origin = place\n      place = undefined\n    }\n\n    if (typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        parts[1] = origin\n      } else {\n        parts[0] = origin.slice(0, index)\n        parts[1] = origin.slice(index + 1)\n      }\n    }\n\n    if (place) {\n      // Node.\n      if ('type' in place || 'position' in place) {\n        if (place.position) {\n          // To do: next major: deep clone.\n          // @ts-expect-error: looks like a position.\n          position = place.position\n        }\n      }\n      // Position.\n      else if ('start' in place || 'end' in place) {\n        // @ts-expect-error: looks like a position.\n        // To do: next major: deep clone.\n        position = place\n      }\n      // Point.\n      else if ('line' in place || 'column' in place) {\n        // To do: next major: deep clone.\n        position.start = place\n      }\n    }\n\n    // Fields from `Error`.\n    /**\n     * Serialized positional info of error.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_0__.stringifyPosition)(place) || '1:1'\n\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = typeof reason === 'object' ? reason.message : reason\n\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = ''\n\n    if (typeof reason === 'object' && reason.stack) {\n      this.stack = reason.stack\n    }\n\n    /**\n     * Reason for message.\n     *\n     * > 👉 **Note**: you should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * State of problem.\n     *\n     * * `true` — marks associated file as no longer processable (error)\n     * * `false` — necessitates a (potential) change (warning)\n     * * `null | undefined` — for things that might not need changing (info)\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | null}\n     */\n    this.line = position.start.line\n\n    /**\n     * Starting column of error.\n     *\n     * @type {number | null}\n     */\n    this.column = position.start.column\n\n    /**\n     * Full unist position.\n     *\n     * @type {Position | null}\n     */\n    this.position = position\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | null}\n     */\n    this.source = parts[0]\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | null}\n     */\n    this.ruleId = parts[1]\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | null}\n     */\n    this.file\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | null}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | null}\n     */\n    this.expected\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | null}\n     */\n    this.url\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | null}\n     */\n    this.note\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.fatal = null\nVFileMessage.prototype.column = null\nVFileMessage.prototype.line = null\nVFileMessage.prototype.source = null\nVFileMessage.prototype.ruleId = null\nVFileMessage.prototype.position = null\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/node_modules/vfile-message/lib/index.js\n");

/***/ })

};
;