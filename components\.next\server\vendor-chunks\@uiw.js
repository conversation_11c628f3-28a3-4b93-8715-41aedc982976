"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw";
exports.ids = ["vendor-chunks/@uiw"];
exports.modules = {

/***/ "(ssr)/./node_modules/@uiw/codemirror-theme-github/esm/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-theme-github/esm/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSettingsGithubDark: () => (/* binding */ defaultSettingsGithubDark),\n/* harmony export */   defaultSettingsGithubLight: () => (/* binding */ defaultSettingsGithubLight),\n/* harmony export */   githubDark: () => (/* binding */ githubDark),\n/* harmony export */   githubDarkInit: () => (/* binding */ githubDarkInit),\n/* harmony export */   githubDarkStyle: () => (/* binding */ githubDarkStyle),\n/* harmony export */   githubLight: () => (/* binding */ githubLight),\n/* harmony export */   githubLightInit: () => (/* binding */ githubLightInit),\n/* harmony export */   githubLightStyle: () => (/* binding */ githubLightStyle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uiw/codemirror-themes */ \"(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js\");\n\n/**\n * @name github\n */\n\n\nvar defaultSettingsGithubLight = {\n  background: '#fff',\n  foreground: '#24292e',\n  selection: '#BBDFFF',\n  selectionMatch: '#BBDFFF',\n  gutterBackground: '#fff',\n  gutterForeground: '#6e7781'\n};\nvar githubLightStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName],\n  color: '#116329'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bracket],\n  color: '#6a737d'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.className, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName],\n  color: '#6f42c1'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.attributeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.operator],\n  color: '#005cc5'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeOperator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName],\n  color: '#d73a49'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp],\n  color: '#032f62'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote],\n  color: '#22863a'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strong],\n  color: '#24292e',\n  fontWeight: 'bold'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis],\n  color: '#24292e',\n  fontStyle: 'italic'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.deleted],\n  color: '#b31d28',\n  backgroundColor: 'ffeef0'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bool, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#e36209'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.url, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.escape, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link],\n  color: '#032f62'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link,\n  textDecoration: 'underline'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strikethrough,\n  textDecoration: 'line-through'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.invalid,\n  color: '#cb2431'\n}];\nvar githubLightInit = options => {\n  var {\n    theme = 'light',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsGithubLight, settings),\n    styles: [...githubLightStyle, ...styles]\n  });\n};\nvar githubLight = githubLightInit();\nvar defaultSettingsGithubDark = {\n  background: '#0d1117',\n  foreground: '#c9d1d9',\n  caret: '#c9d1d9',\n  selection: '#003d73',\n  selectionMatch: '#003d73',\n  lineHighlight: '#36334280'\n};\nvar githubDarkStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.tagName],\n  color: '#7ee787'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bracket],\n  color: '#8b949e'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.className, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName],\n  color: '#d2a8ff'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.attributeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.operator],\n  color: '#79c0ff'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeOperator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName],\n  color: '#ff7b72'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp],\n  color: '#a5d6ff'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote],\n  color: '#7ee787'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strong],\n  color: '#d2a8ff',\n  fontWeight: 'bold'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis],\n  color: '#d2a8ff',\n  fontStyle: 'italic'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.deleted],\n  color: '#ffdcd7',\n  backgroundColor: 'ffeef0'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bool, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#ffab70'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link,\n  textDecoration: 'underline'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strikethrough,\n  textDecoration: 'line-through'\n}, {\n  tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.invalid,\n  color: '#f97583'\n}];\nvar githubDarkInit = options => {\n  var {\n    theme = 'dark',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsGithubDark, settings),\n    styles: [...githubDarkStyle, ...styles]\n  });\n};\nvar githubDark = githubDarkInit();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-theme-github/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@uiw/codemirror-themes/esm/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTheme: () => (/* binding */ createTheme),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/@codemirror/language/dist/index.js\");\n\n\nvar createTheme = _ref => {\n  var {\n    theme,\n    settings = {},\n    styles = []\n  } = _ref;\n  var themeOptions = {\n    '.cm-gutters': {}\n  };\n  var baseStyle = {};\n  if (settings.background) {\n    baseStyle.backgroundColor = settings.background;\n  }\n  if (settings.backgroundImage) {\n    baseStyle.backgroundImage = settings.backgroundImage;\n  }\n  if (settings.foreground) {\n    baseStyle.color = settings.foreground;\n  }\n  if (settings.fontSize) {\n    baseStyle.fontSize = settings.fontSize;\n  }\n  if (settings.background || settings.foreground) {\n    themeOptions['&'] = baseStyle;\n  }\n  if (settings.fontFamily) {\n    themeOptions['&.cm-editor .cm-scroller'] = {\n      fontFamily: settings.fontFamily\n    };\n  }\n  if (settings.gutterBackground) {\n    themeOptions['.cm-gutters'].backgroundColor = settings.gutterBackground;\n  }\n  if (settings.gutterForeground) {\n    themeOptions['.cm-gutters'].color = settings.gutterForeground;\n  }\n  if (settings.gutterBorder) {\n    themeOptions['.cm-gutters'].borderRightColor = settings.gutterBorder;\n  }\n  if (settings.caret) {\n    themeOptions['.cm-content'] = {\n      caretColor: settings.caret\n    };\n    themeOptions['.cm-cursor, .cm-dropCursor'] = {\n      borderLeftColor: settings.caret\n    };\n  }\n  var activeLineGutterStyle = {};\n  if (settings.gutterActiveForeground) {\n    activeLineGutterStyle.color = settings.gutterActiveForeground;\n  }\n  if (settings.lineHighlight) {\n    themeOptions['.cm-activeLine'] = {\n      backgroundColor: settings.lineHighlight\n    };\n    activeLineGutterStyle.backgroundColor = settings.lineHighlight;\n  }\n  themeOptions['.cm-activeLineGutter'] = activeLineGutterStyle;\n  if (settings.selection) {\n    themeOptions['&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection'] = {\n      background: settings.selection + ' !important'\n    };\n  }\n  if (settings.selectionMatch) {\n    themeOptions['& .cm-selectionMatch'] = {\n      backgroundColor: settings.selectionMatch\n    };\n  }\n  var themeExtension = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme(themeOptions, {\n    dark: theme === 'dark'\n  });\n  var highlightStyle = _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.HighlightStyle.define(styles);\n  var extension = [themeExtension, (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxHighlighting)(highlightStyle)];\n  return extension;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createTheme);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-themes/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicSetup: () => (/* binding */ basicSetup),\n/* harmony export */   minimalSetup: () => (/* binding */ minimalSetup)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/./node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _codemirror_search__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/search */ \"(ssr)/./node_modules/@codemirror/search/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lint__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lint */ \"(ssr)/./node_modules/@codemirror/lint/dist/index.js\");\n\n\n\n\n\n\n\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nvar basicSetup = function basicSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var {\n    crosshairCursor: initCrosshairCursor = false\n  } = options;\n  var keymaps = [];\n  if (options.closeBracketsKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBracketsKeymap);\n  }\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.searchKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.searchKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  if (options.foldKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldKeymap);\n  }\n  if (options.completionKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.completionKeymap);\n  }\n  if (options.lintKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_lint__WEBPACK_IMPORTED_MODULE_4__.lintKeymap);\n  }\n  var extensions = [];\n  if (options.lineNumbers !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers)());\n  if (options.highlightActiveLineGutter !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter)());\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.foldGutter !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldGutter)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.dropCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor)());\n  if (options.allowMultipleSelections !== false) extensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState.allowMultipleSelections.of(true));\n  if (options.indentOnInput !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentOnInput)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  if (options.bracketMatching !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.bracketMatching)());\n  if (options.closeBrackets !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBrackets)());\n  if (options.autocompletion !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.autocompletion)());\n  if (options.rectangularSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection)());\n  if (initCrosshairCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor)());\n  if (options.highlightActiveLine !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine)());\n  if (options.highlightSelectionMatches !== false) extensions.push((0,_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.highlightSelectionMatches)());\n  if (options.tabSize && typeof options.tabSize === 'number') extensions.push(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentUnit.of(' '.repeat(options.tabSize)));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nvar minimalSetup = function minimalSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var keymaps = [];\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  var extensions = [];\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js":
/*!************************************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.color),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _theme_light_js__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption),\n/* harmony export */   getDefaultExtensions: () => (/* binding */ getDefaultExtensions),\n/* harmony export */   oneDark: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkTheme)\n/* harmony export */ });\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/./node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/theme-one-dark */ \"(ssr)/./node_modules/@codemirror/theme-one-dark/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _theme_light_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme/light.js */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/theme/light.js\");\n\n\n\n\n\n\n\n\nvar getDefaultExtensions = function getDefaultExtensions(optios) {\n  if (optios === void 0) {\n    optios = {};\n  }\n  var {\n    indentWithTab: defaultIndentWithTab = true,\n    editable = true,\n    readOnly = false,\n    theme = 'light',\n    placeholder: placeholderStr = '',\n    basicSetup: defaultBasicSetup = true\n  } = optios;\n  var getExtensions = [];\n  if (defaultIndentWithTab) {\n    getExtensions.unshift(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.keymap.of([_codemirror_commands__WEBPACK_IMPORTED_MODULE_4__.indentWithTab]));\n  }\n  if (defaultBasicSetup) {\n    if (typeof defaultBasicSetup === 'boolean') {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)());\n    } else {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)(defaultBasicSetup));\n    }\n  }\n  if (placeholderStr) {\n    getExtensions.unshift((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.placeholder)(placeholderStr));\n  }\n  switch (theme) {\n    case 'light':\n      getExtensions.push(_theme_light_js__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption);\n      break;\n    case 'dark':\n      getExtensions.push(_codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark);\n      break;\n    case 'none':\n      break;\n    default:\n      getExtensions.push(theme);\n      break;\n  }\n  if (editable === false) {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.EditorView.editable.of(false));\n  }\n  if (readOnly) {\n    getExtensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_5__.EditorState.readOnly.of(true));\n  }\n  return [...getExtensions];\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Annotation: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Annotation),\n/* harmony export */   AnnotationType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.AnnotationType),\n/* harmony export */   BidiSpan: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BidiSpan),\n/* harmony export */   BlockInfo: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockInfo),\n/* harmony export */   BlockType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockType),\n/* harmony export */   ChangeDesc: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeDesc),\n/* harmony export */   ChangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeSet),\n/* harmony export */   CharCategory: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.CharCategory),\n/* harmony export */   Compartment: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Compartment),\n/* harmony export */   Decoration: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Decoration),\n/* harmony export */   Direction: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Direction),\n/* harmony export */   EditorSelection: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorSelection),\n/* harmony export */   EditorState: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState),\n/* harmony export */   EditorView: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.EditorView),\n/* harmony export */   Facet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Facet),\n/* harmony export */   GutterMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.GutterMarker),\n/* harmony export */   Line: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Line),\n/* harmony export */   MapMode: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.MapMode),\n/* harmony export */   MatchDecorator: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.MatchDecorator),\n/* harmony export */   Prec: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Prec),\n/* harmony export */   Range: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Range),\n/* harmony export */   RangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSet),\n/* harmony export */   RangeSetBuilder: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSetBuilder),\n/* harmony export */   RangeValue: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeValue),\n/* harmony export */   RectangleMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.RectangleMarker),\n/* harmony export */   SelectionRange: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.SelectionRange),\n/* harmony export */   StateEffect: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffect),\n/* harmony export */   StateEffectType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffectType),\n/* harmony export */   StateField: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateField),\n/* harmony export */   Text: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Text),\n/* harmony export */   Transaction: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Transaction),\n/* harmony export */   ViewPlugin: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewPlugin),\n/* harmony export */   ViewUpdate: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewUpdate),\n/* harmony export */   WidgetType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.WidgetType),\n/* harmony export */   __test: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.__test),\n/* harmony export */   basicSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.basicSetup),\n/* harmony export */   closeHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.closeHoverTooltips),\n/* harmony export */   codePointAt: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointAt),\n/* harmony export */   codePointSize: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointSize),\n/* harmony export */   color: () => (/* reexport safe */ _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__.color),\n/* harmony export */   combineConfig: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.combineConfig),\n/* harmony export */   countColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.countColumn),\n/* harmony export */   crosshairCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__.defaultLightThemeOption),\n/* harmony export */   drawSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection),\n/* harmony export */   dropCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor),\n/* harmony export */   findClusterBreak: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findClusterBreak),\n/* harmony export */   findColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findColumn),\n/* harmony export */   fromCodePoint: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.fromCodePoint),\n/* harmony export */   getDefaultExtensions: () => (/* reexport safe */ _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__.getDefaultExtensions),\n/* harmony export */   getDialog: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getDialog),\n/* harmony export */   getDrawSelectionConfig: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getDrawSelectionConfig),\n/* harmony export */   getPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getPanel),\n/* harmony export */   getStatistics: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_9__.getStatistics),\n/* harmony export */   getTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getTooltip),\n/* harmony export */   gutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutter),\n/* harmony export */   gutterLineClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterLineClass),\n/* harmony export */   gutterWidgetClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterWidgetClass),\n/* harmony export */   gutters: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutters),\n/* harmony export */   hasHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hasHoverTooltips),\n/* harmony export */   highlightActiveLine: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine),\n/* harmony export */   highlightActiveLineGutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter),\n/* harmony export */   highlightSpecialChars: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars),\n/* harmony export */   highlightTrailingWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightTrailingWhitespace),\n/* harmony export */   highlightWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightWhitespace),\n/* harmony export */   hoverTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hoverTooltip),\n/* harmony export */   keymap: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap),\n/* harmony export */   layer: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.layer),\n/* harmony export */   lineNumberMarkers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberMarkers),\n/* harmony export */   lineNumberWidgetMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberWidgetMarker),\n/* harmony export */   lineNumbers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers),\n/* harmony export */   logException: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.logException),\n/* harmony export */   minimalSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.minimalSetup),\n/* harmony export */   oneDark: () => (/* reexport safe */ _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__.oneDarkTheme),\n/* harmony export */   panels: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.panels),\n/* harmony export */   placeholder: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.placeholder),\n/* harmony export */   rectangularSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection),\n/* harmony export */   repositionTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.repositionTooltips),\n/* harmony export */   runScopeHandlers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.runScopeHandlers),\n/* harmony export */   scrollPastEnd: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.scrollPastEnd),\n/* harmony export */   showDialog: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showDialog),\n/* harmony export */   showPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showPanel),\n/* harmony export */   showTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showTooltip),\n/* harmony export */   tooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.tooltips),\n/* harmony export */   useCodeMirror: () => (/* reexport safe */ _useCodeMirror_js__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useCodeMirror_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useCodeMirror.js */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/./node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./getDefaultExtensions.js */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\nvar _excluded = [\"className\", \"value\", \"selection\", \"extensions\", \"onChange\", \"onStatistics\", \"onCreateEditor\", \"onUpdate\", \"autoFocus\", \"theme\", \"height\", \"minHeight\", \"maxHeight\", \"width\", \"minWidth\", \"maxWidth\", \"basicSetup\", \"placeholder\", \"indentWithTab\", \"editable\", \"readOnly\", \"root\", \"initialState\"];\n\n\n\n\n\n\n\n\n\nvar ReactCodeMirror = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => {\n  var {\n      className,\n      value = '',\n      selection,\n      extensions = [],\n      onChange,\n      onStatistics,\n      onCreateEditor,\n      onUpdate,\n      autoFocus,\n      theme = 'light',\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth,\n      basicSetup,\n      placeholder,\n      indentWithTab,\n      editable,\n      readOnly,\n      root,\n      initialState\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__(props, _excluded);\n  var editor = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  var {\n    state,\n    view,\n    container,\n    setContainer\n  } = (0,_useCodeMirror_js__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)({\n    root,\n    value,\n    autoFocus,\n    theme,\n    height,\n    minHeight,\n    maxHeight,\n    width,\n    minWidth,\n    maxWidth,\n    basicSetup,\n    placeholder,\n    indentWithTab,\n    editable,\n    readOnly,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions,\n    initialState\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => ({\n    editor: editor.current,\n    state: state,\n    view: view\n  }), [editor, container, state, view]);\n  var setEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(el => {\n    editor.current = el;\n    setContainer(el);\n  }, [setContainer]);\n\n  // check type of value\n  if (typeof value !== 'string') {\n    throw new Error(\"value must be typeof string but got \" + typeof value);\n  }\n  var defaultClassNames = typeof theme === 'string' ? \"cm-theme-\" + theme : 'cm-theme';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__({\n    ref: setEditorRef,\n    className: \"\" + defaultClassNames + (className ? \" \" + className : '')\n  }, other));\n});\nReactCodeMirror.displayName = 'CodeMirror';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactCodeMirror);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/theme/light.js":
/*!***************************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/theme/light.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLightThemeOption: () => (/* binding */ defaultLightThemeOption)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n\nvar defaultLightThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme({\n  '&': {\n    backgroundColor: '#fff'\n  }\n}, {\n  dark: false\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1jb2RlbWlycm9yL2VzbS90aGVtZS9saWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUN2Qyw4QkFBOEIsd0RBQVU7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxAdWl3XFxyZWFjdC1jb2RlbWlycm9yXFxlc21cXHRoZW1lXFxsaWdodC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFZGl0b3JWaWV3IH0gZnJvbSAnQGNvZGVtaXJyb3Ivdmlldyc7XG5leHBvcnQgdmFyIGRlZmF1bHRMaWdodFRoZW1lT3B0aW9uID0gRWRpdG9yVmlldy50aGVtZSh7XG4gICcmJzoge1xuICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmYnXG4gIH1cbn0sIHtcbiAgZGFyazogZmFsc2Vcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/theme/light.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCodeMirror: () => (/* binding */ useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDefaultExtensions.js */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\n\n\n\nvar External = _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Annotation.define();\nvar emptyExtensions = [];\nfunction useCodeMirror(props) {\n  var {\n    value,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions = emptyExtensions,\n    autoFocus,\n    theme = 'light',\n    height = null,\n    minHeight = null,\n    maxHeight = null,\n    width = null,\n    minWidth = null,\n    maxWidth = null,\n    placeholder: placeholderStr = '',\n    editable = true,\n    readOnly = false,\n    indentWithTab: defaultIndentWithTab = true,\n    basicSetup: defaultBasicSetup = true,\n    root,\n    initialState\n  } = props;\n  var [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var defaultThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.theme({\n    '&': {\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth\n    },\n    '& .cm-scroller': {\n      height: '100% !important'\n    }\n  });\n  var updateListener = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(vu => {\n    if (vu.docChanged && typeof onChange === 'function' &&\n    // Fix echoing of the remote changes:\n    // If transaction is market as remote we don't have to call `onChange` handler again\n    !vu.transactions.some(tr => tr.annotation(External))) {\n      var doc = vu.state.doc;\n      var _value = doc.toString();\n      onChange(_value, vu);\n    }\n    onStatistics && onStatistics((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getStatistics)(vu));\n  });\n  var defaultExtensions = (0,_getDefaultExtensions_js__WEBPACK_IMPORTED_MODULE_1__.getDefaultExtensions)({\n    theme,\n    editable,\n    readOnly,\n    placeholder: placeholderStr,\n    indentWithTab: defaultIndentWithTab,\n    basicSetup: defaultBasicSetup\n  });\n  var getExtensions = [updateListener, defaultThemeOption, ...defaultExtensions];\n  if (onUpdate && typeof onUpdate === 'function') {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(onUpdate));\n  }\n  getExtensions = getExtensions.concat(extensions);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (container && !state) {\n      var config = {\n        doc: value,\n        selection,\n        extensions: getExtensions\n      };\n      var stateCurrent = initialState ? _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.fromJSON(initialState.json, config, initialState.fields) : _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.create(config);\n      setState(stateCurrent);\n      if (!view) {\n        var viewCurrent = new _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView({\n          state: stateCurrent,\n          parent: container,\n          root\n        });\n        setView(viewCurrent);\n        onCreateEditor && onCreateEditor(viewCurrent, stateCurrent);\n      }\n    }\n    return () => {\n      if (view) {\n        setState(undefined);\n        setView(undefined);\n      }\n    };\n  }, [container, state]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (props.container) {\n      setContainer(props.container);\n    }\n  }, [props.container]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => {\n    if (view) {\n      view.destroy();\n      setView(undefined);\n    }\n  }, [view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoFocus && view) {\n      view.focus();\n    }\n  }, [autoFocus, view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (view) {\n      view.dispatch({\n        effects: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.reconfigure.of(getExtensions)\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [theme, extensions, height, minHeight, maxHeight, width, minWidth, maxWidth, placeholderStr, editable, readOnly, defaultIndentWithTab, defaultBasicSetup, onChange, onUpdate]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (value === undefined) {\n      return;\n    }\n    var currentValue = view ? view.state.doc.toString() : '';\n    if (view && value !== currentValue) {\n      view.dispatch({\n        changes: {\n          from: 0,\n          to: currentValue.length,\n          insert: value || ''\n        },\n        annotations: [External.of(true)]\n      });\n    }\n  }, [value, view]);\n  return {\n    state,\n    setState,\n    view,\n    setView,\n    container,\n    setContainer\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1jb2RlbWlycm9yL2VzbS91c2VDb2RlTWlycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUNZO0FBQzNCO0FBQ21CO0FBQ3RCO0FBQzNDLGVBQWUseURBQVU7QUFDekI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0osa0NBQWtDLCtDQUFRO0FBQzFDLHdCQUF3QiwrQ0FBUTtBQUNoQywwQkFBMEIsK0NBQVE7QUFDbEMsMkJBQTJCLHdEQUFVO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCx1QkFBdUIsd0RBQVU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyx3REFBYTtBQUM5QyxHQUFHO0FBQ0gsMEJBQTBCLDhFQUFvQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLHVCQUF1Qix3REFBVTtBQUNqQztBQUNBO0FBQ0EsRUFBRSxzREFBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsMERBQVcsNERBQTRELDBEQUFXO0FBQzFIO0FBQ0E7QUFDQSw4QkFBOEIsd0RBQVU7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQSxpQkFBaUIsMERBQVc7QUFDNUIsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxAdWl3XFxyZWFjdC1jb2RlbWlycm9yXFxlc21cXHVzZUNvZGVNaXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VMYXlvdXRFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQW5ub3RhdGlvbiwgRWRpdG9yU3RhdGUsIFN0YXRlRWZmZWN0IH0gZnJvbSAnQGNvZGVtaXJyb3Ivc3RhdGUnO1xuaW1wb3J0IHsgRWRpdG9yVmlldyB9IGZyb20gJ0Bjb2RlbWlycm9yL3ZpZXcnO1xuaW1wb3J0IHsgZ2V0RGVmYXVsdEV4dGVuc2lvbnMgfSBmcm9tIFwiLi9nZXREZWZhdWx0RXh0ZW5zaW9ucy5qc1wiO1xuaW1wb3J0IHsgZ2V0U3RhdGlzdGljcyB9IGZyb20gXCIuL3V0aWxzLmpzXCI7XG52YXIgRXh0ZXJuYWwgPSBBbm5vdGF0aW9uLmRlZmluZSgpO1xudmFyIGVtcHR5RXh0ZW5zaW9ucyA9IFtdO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUNvZGVNaXJyb3IocHJvcHMpIHtcbiAgdmFyIHtcbiAgICB2YWx1ZSxcbiAgICBzZWxlY3Rpb24sXG4gICAgb25DaGFuZ2UsXG4gICAgb25TdGF0aXN0aWNzLFxuICAgIG9uQ3JlYXRlRWRpdG9yLFxuICAgIG9uVXBkYXRlLFxuICAgIGV4dGVuc2lvbnMgPSBlbXB0eUV4dGVuc2lvbnMsXG4gICAgYXV0b0ZvY3VzLFxuICAgIHRoZW1lID0gJ2xpZ2h0JyxcbiAgICBoZWlnaHQgPSBudWxsLFxuICAgIG1pbkhlaWdodCA9IG51bGwsXG4gICAgbWF4SGVpZ2h0ID0gbnVsbCxcbiAgICB3aWR0aCA9IG51bGwsXG4gICAgbWluV2lkdGggPSBudWxsLFxuICAgIG1heFdpZHRoID0gbnVsbCxcbiAgICBwbGFjZWhvbGRlcjogcGxhY2Vob2xkZXJTdHIgPSAnJyxcbiAgICBlZGl0YWJsZSA9IHRydWUsXG4gICAgcmVhZE9ubHkgPSBmYWxzZSxcbiAgICBpbmRlbnRXaXRoVGFiOiBkZWZhdWx0SW5kZW50V2l0aFRhYiA9IHRydWUsXG4gICAgYmFzaWNTZXR1cDogZGVmYXVsdEJhc2ljU2V0dXAgPSB0cnVlLFxuICAgIHJvb3QsXG4gICAgaW5pdGlhbFN0YXRlXG4gIH0gPSBwcm9wcztcbiAgdmFyIFtjb250YWluZXIsIHNldENvbnRhaW5lcl0gPSB1c2VTdGF0ZSgpO1xuICB2YXIgW3ZpZXcsIHNldFZpZXddID0gdXNlU3RhdGUoKTtcbiAgdmFyIFtzdGF0ZSwgc2V0U3RhdGVdID0gdXNlU3RhdGUoKTtcbiAgdmFyIGRlZmF1bHRUaGVtZU9wdGlvbiA9IEVkaXRvclZpZXcudGhlbWUoe1xuICAgICcmJzoge1xuICAgICAgaGVpZ2h0LFxuICAgICAgbWluSGVpZ2h0LFxuICAgICAgbWF4SGVpZ2h0LFxuICAgICAgd2lkdGgsXG4gICAgICBtaW5XaWR0aCxcbiAgICAgIG1heFdpZHRoXG4gICAgfSxcbiAgICAnJiAuY20tc2Nyb2xsZXInOiB7XG4gICAgICBoZWlnaHQ6ICcxMDAlICFpbXBvcnRhbnQnXG4gICAgfVxuICB9KTtcbiAgdmFyIHVwZGF0ZUxpc3RlbmVyID0gRWRpdG9yVmlldy51cGRhdGVMaXN0ZW5lci5vZih2dSA9PiB7XG4gICAgaWYgKHZ1LmRvY0NoYW5nZWQgJiYgdHlwZW9mIG9uQ2hhbmdlID09PSAnZnVuY3Rpb24nICYmXG4gICAgLy8gRml4IGVjaG9pbmcgb2YgdGhlIHJlbW90ZSBjaGFuZ2VzOlxuICAgIC8vIElmIHRyYW5zYWN0aW9uIGlzIG1hcmtldCBhcyByZW1vdGUgd2UgZG9uJ3QgaGF2ZSB0byBjYWxsIGBvbkNoYW5nZWAgaGFuZGxlciBhZ2FpblxuICAgICF2dS50cmFuc2FjdGlvbnMuc29tZSh0ciA9PiB0ci5hbm5vdGF0aW9uKEV4dGVybmFsKSkpIHtcbiAgICAgIHZhciBkb2MgPSB2dS5zdGF0ZS5kb2M7XG4gICAgICB2YXIgX3ZhbHVlID0gZG9jLnRvU3RyaW5nKCk7XG4gICAgICBvbkNoYW5nZShfdmFsdWUsIHZ1KTtcbiAgICB9XG4gICAgb25TdGF0aXN0aWNzICYmIG9uU3RhdGlzdGljcyhnZXRTdGF0aXN0aWNzKHZ1KSk7XG4gIH0pO1xuICB2YXIgZGVmYXVsdEV4dGVuc2lvbnMgPSBnZXREZWZhdWx0RXh0ZW5zaW9ucyh7XG4gICAgdGhlbWUsXG4gICAgZWRpdGFibGUsXG4gICAgcmVhZE9ubHksXG4gICAgcGxhY2Vob2xkZXI6IHBsYWNlaG9sZGVyU3RyLFxuICAgIGluZGVudFdpdGhUYWI6IGRlZmF1bHRJbmRlbnRXaXRoVGFiLFxuICAgIGJhc2ljU2V0dXA6IGRlZmF1bHRCYXNpY1NldHVwXG4gIH0pO1xuICB2YXIgZ2V0RXh0ZW5zaW9ucyA9IFt1cGRhdGVMaXN0ZW5lciwgZGVmYXVsdFRoZW1lT3B0aW9uLCAuLi5kZWZhdWx0RXh0ZW5zaW9uc107XG4gIGlmIChvblVwZGF0ZSAmJiB0eXBlb2Ygb25VcGRhdGUgPT09ICdmdW5jdGlvbicpIHtcbiAgICBnZXRFeHRlbnNpb25zLnB1c2goRWRpdG9yVmlldy51cGRhdGVMaXN0ZW5lci5vZihvblVwZGF0ZSkpO1xuICB9XG4gIGdldEV4dGVuc2lvbnMgPSBnZXRFeHRlbnNpb25zLmNvbmNhdChleHRlbnNpb25zKTtcbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAoY29udGFpbmVyICYmICFzdGF0ZSkge1xuICAgICAgdmFyIGNvbmZpZyA9IHtcbiAgICAgICAgZG9jOiB2YWx1ZSxcbiAgICAgICAgc2VsZWN0aW9uLFxuICAgICAgICBleHRlbnNpb25zOiBnZXRFeHRlbnNpb25zXG4gICAgICB9O1xuICAgICAgdmFyIHN0YXRlQ3VycmVudCA9IGluaXRpYWxTdGF0ZSA/IEVkaXRvclN0YXRlLmZyb21KU09OKGluaXRpYWxTdGF0ZS5qc29uLCBjb25maWcsIGluaXRpYWxTdGF0ZS5maWVsZHMpIDogRWRpdG9yU3RhdGUuY3JlYXRlKGNvbmZpZyk7XG4gICAgICBzZXRTdGF0ZShzdGF0ZUN1cnJlbnQpO1xuICAgICAgaWYgKCF2aWV3KSB7XG4gICAgICAgIHZhciB2aWV3Q3VycmVudCA9IG5ldyBFZGl0b3JWaWV3KHtcbiAgICAgICAgICBzdGF0ZTogc3RhdGVDdXJyZW50LFxuICAgICAgICAgIHBhcmVudDogY29udGFpbmVyLFxuICAgICAgICAgIHJvb3RcbiAgICAgICAgfSk7XG4gICAgICAgIHNldFZpZXcodmlld0N1cnJlbnQpO1xuICAgICAgICBvbkNyZWF0ZUVkaXRvciAmJiBvbkNyZWF0ZUVkaXRvcih2aWV3Q3VycmVudCwgc3RhdGVDdXJyZW50KTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmICh2aWV3KSB7XG4gICAgICAgIHNldFN0YXRlKHVuZGVmaW5lZCk7XG4gICAgICAgIHNldFZpZXcodW5kZWZpbmVkKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbY29udGFpbmVyLCBzdGF0ZV0pO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChwcm9wcy5jb250YWluZXIpIHtcbiAgICAgIHNldENvbnRhaW5lcihwcm9wcy5jb250YWluZXIpO1xuICAgIH1cbiAgfSwgW3Byb3BzLmNvbnRhaW5lcl0pO1xuICB1c2VFZmZlY3QoKCkgPT4gKCkgPT4ge1xuICAgIGlmICh2aWV3KSB7XG4gICAgICB2aWV3LmRlc3Ryb3koKTtcbiAgICAgIHNldFZpZXcodW5kZWZpbmVkKTtcbiAgICB9XG4gIH0sIFt2aWV3XSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGF1dG9Gb2N1cyAmJiB2aWV3KSB7XG4gICAgICB2aWV3LmZvY3VzKCk7XG4gICAgfVxuICB9LCBbYXV0b0ZvY3VzLCB2aWV3XSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHZpZXcpIHtcbiAgICAgIHZpZXcuZGlzcGF0Y2goe1xuICAgICAgICBlZmZlY3RzOiBTdGF0ZUVmZmVjdC5yZWNvbmZpZ3VyZS5vZihnZXRFeHRlbnNpb25zKVxuICAgICAgfSk7XG4gICAgfVxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcbiAgfSwgW3RoZW1lLCBleHRlbnNpb25zLCBoZWlnaHQsIG1pbkhlaWdodCwgbWF4SGVpZ2h0LCB3aWR0aCwgbWluV2lkdGgsIG1heFdpZHRoLCBwbGFjZWhvbGRlclN0ciwgZWRpdGFibGUsIHJlYWRPbmx5LCBkZWZhdWx0SW5kZW50V2l0aFRhYiwgZGVmYXVsdEJhc2ljU2V0dXAsIG9uQ2hhbmdlLCBvblVwZGF0ZV0pO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHZhciBjdXJyZW50VmFsdWUgPSB2aWV3ID8gdmlldy5zdGF0ZS5kb2MudG9TdHJpbmcoKSA6ICcnO1xuICAgIGlmICh2aWV3ICYmIHZhbHVlICE9PSBjdXJyZW50VmFsdWUpIHtcbiAgICAgIHZpZXcuZGlzcGF0Y2goe1xuICAgICAgICBjaGFuZ2VzOiB7XG4gICAgICAgICAgZnJvbTogMCxcbiAgICAgICAgICB0bzogY3VycmVudFZhbHVlLmxlbmd0aCxcbiAgICAgICAgICBpbnNlcnQ6IHZhbHVlIHx8ICcnXG4gICAgICAgIH0sXG4gICAgICAgIGFubm90YXRpb25zOiBbRXh0ZXJuYWwub2YodHJ1ZSldXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFt2YWx1ZSwgdmlld10pO1xuICByZXR1cm4ge1xuICAgIHN0YXRlLFxuICAgIHNldFN0YXRlLFxuICAgIHZpZXcsXG4gICAgc2V0VmlldyxcbiAgICBjb250YWluZXIsXG4gICAgc2V0Q29udGFpbmVyXG4gIH07XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/@uiw/react-codemirror/esm/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStatistics: () => (/* binding */ getStatistics)\n/* harmony export */ });\nvar getStatistics = view => {\n  return {\n    line: view.state.doc.lineAt(view.state.selection.main.from),\n    lineCount: view.state.doc.lines,\n    lineBreak: view.state.lineBreak,\n    length: view.state.doc.length,\n    readOnly: view.state.readOnly,\n    tabSize: view.state.tabSize,\n    selection: view.state.selection,\n    selectionAsSingle: view.state.selection.asSingle().main,\n    ranges: view.state.selection.ranges,\n    selectionCode: view.state.sliceDoc(view.state.selection.main.from, view.state.selection.main.to),\n    selections: view.state.selection.ranges.map(r => view.state.sliceDoc(r.from, r.to)),\n    selectedText: view.state.selection.ranges.some(r => !r.empty)\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVpdy9yZWFjdC1jb2RlbWlycm9yL2VzbS91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcQHVpd1xccmVhY3QtY29kZW1pcnJvclxcZXNtXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIGdldFN0YXRpc3RpY3MgPSB2aWV3ID0+IHtcbiAgcmV0dXJuIHtcbiAgICBsaW5lOiB2aWV3LnN0YXRlLmRvYy5saW5lQXQodmlldy5zdGF0ZS5zZWxlY3Rpb24ubWFpbi5mcm9tKSxcbiAgICBsaW5lQ291bnQ6IHZpZXcuc3RhdGUuZG9jLmxpbmVzLFxuICAgIGxpbmVCcmVhazogdmlldy5zdGF0ZS5saW5lQnJlYWssXG4gICAgbGVuZ3RoOiB2aWV3LnN0YXRlLmRvYy5sZW5ndGgsXG4gICAgcmVhZE9ubHk6IHZpZXcuc3RhdGUucmVhZE9ubHksXG4gICAgdGFiU2l6ZTogdmlldy5zdGF0ZS50YWJTaXplLFxuICAgIHNlbGVjdGlvbjogdmlldy5zdGF0ZS5zZWxlY3Rpb24sXG4gICAgc2VsZWN0aW9uQXNTaW5nbGU6IHZpZXcuc3RhdGUuc2VsZWN0aW9uLmFzU2luZ2xlKCkubWFpbixcbiAgICByYW5nZXM6IHZpZXcuc3RhdGUuc2VsZWN0aW9uLnJhbmdlcyxcbiAgICBzZWxlY3Rpb25Db2RlOiB2aWV3LnN0YXRlLnNsaWNlRG9jKHZpZXcuc3RhdGUuc2VsZWN0aW9uLm1haW4uZnJvbSwgdmlldy5zdGF0ZS5zZWxlY3Rpb24ubWFpbi50byksXG4gICAgc2VsZWN0aW9uczogdmlldy5zdGF0ZS5zZWxlY3Rpb24ucmFuZ2VzLm1hcChyID0+IHZpZXcuc3RhdGUuc2xpY2VEb2Moci5mcm9tLCByLnRvKSksXG4gICAgc2VsZWN0ZWRUZXh0OiB2aWV3LnN0YXRlLnNlbGVjdGlvbi5yYW5nZXMuc29tZShyID0+ICFyLmVtcHR5KVxuICB9O1xufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uiw/react-codemirror/esm/utils.js\n");

/***/ })

};
;