"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-tagfilter";
exports.ids = ["vendor-chunks/micromark-extension-gfm-tagfilter"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-tagfilter/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-tagfilter/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTagfilterHtml: () => (/* binding */ gfmTagfilterHtml)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').CompileContext} CompileContext\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n * @typedef {import('micromark-util-types').Token} Token\n */\n\n// An opening or closing tag start, followed by a case-insensitive specific tag name,\n// followed by HTML whitespace, a greater than, or a slash.\nconst reFlow =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|title|textarea|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// As HTML (text) parses tags separately (and very strictly), we don’t need to be\n// global.\nconst reText = new RegExp('^' + reFlow.source, 'i')\n\n/**\n * Extension for `micromark` that can be passed in `htmlExtensions`, to\n * support GitHub’s weird and useless tagfilter when serializing to HTML.\n *\n * @type {HtmlExtension}\n */\nconst gfmTagfilterHtml = {\n  exit: {\n    htmlFlowData(token) {\n      exitHtmlData.call(this, token, reFlow)\n    },\n    htmlTextData(token) {\n      exitHtmlData.call(this, token, reText)\n    }\n  }\n}\n\n/**\n * @this {CompileContext}\n * @param {Token} token\n * @param {RegExp} filter\n */\nfunction exitHtmlData(token, filter) {\n  let value = this.sliceSerialize(token)\n\n  if (this.options.allowDangerousHtml) {\n    value = value.replace(filter, '&lt;$1$2')\n  }\n\n  this.raw(this.encode(value))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-tagfilter/index.js\n");

/***/ })

};
;