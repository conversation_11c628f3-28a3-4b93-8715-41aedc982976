"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/compute-scroll-into-view";
exports.ids = ["vendor-chunks/compute-scroll-into-view"];
exports.modules = {

/***/ "(ssr)/./node_modules/compute-scroll-into-view/dist/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/compute-scroll-into-view/dist/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\nlet e=e=>\"object\"==typeof e&&null!=e&&1===e.nodeType,t=(e,t)=>(!t||\"hidden\"!==e)&&(\"visible\"!==e&&\"clip\"!==e),n=(e,n)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let l=getComputedStyle(e,null);return t(l.overflowY,n)||t(l.overflowX,n)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},l=(e,t,n,l,i,o,r,d)=>o<e&&r>t||o>e&&r<t?0:o<=e&&d<=n||r>=t&&d>=n?o-e-l:r>t&&d<n||o<e&&d>n?r-t+i:0,i=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t};var o=(t,o)=>{var r,d,h,f,u,s;if(\"undefined\"==typeof document)return[];let{scrollMode:a,block:c,inline:g,boundary:m,skipOverflowHiddenElements:p}=o,w=\"function\"==typeof m?m:e=>e!==m;if(!e(t))throw new TypeError(\"Invalid target\");let W=document.scrollingElement||document.documentElement,H=[],b=t;for(;e(b)&&w(b);){if(b=i(b),b===W){H.push(b);break}null!=b&&b===document.body&&n(b)&&!n(document.documentElement)||null!=b&&n(b,p)&&H.push(b)}let v=null!=(d=null==(r=window.visualViewport)?void 0:r.width)?d:innerWidth,y=null!=(f=null==(h=window.visualViewport)?void 0:h.height)?f:innerHeight,E=null!=(u=window.scrollX)?u:pageXOffset,M=null!=(s=window.scrollY)?s:pageYOffset,{height:x,width:I,top:C,right:R,bottom:T,left:V}=t.getBoundingClientRect(),k=\"start\"===c||\"nearest\"===c?C:\"end\"===c?T:C+x/2,B=\"center\"===g?V+I/2:\"end\"===g?R:V,D=[];for(let e=0;e<H.length;e++){let t=H[e],{height:n,width:i,top:o,right:r,bottom:d,left:h}=t.getBoundingClientRect();if(\"if-needed\"===a&&C>=0&&V>=0&&T<=y&&R<=v&&C>=o&&T<=d&&V>=h&&R<=r)return D;let f=getComputedStyle(t),u=parseInt(f.borderLeftWidth,10),s=parseInt(f.borderTopWidth,10),m=parseInt(f.borderRightWidth,10),p=parseInt(f.borderBottomWidth,10),w=0,b=0,O=\"offsetWidth\"in t?t.offsetWidth-t.clientWidth-u-m:0,X=\"offsetHeight\"in t?t.offsetHeight-t.clientHeight-s-p:0,Y=\"offsetWidth\"in t?0===t.offsetWidth?0:i/t.offsetWidth:0,L=\"offsetHeight\"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(W===t)w=\"start\"===c?k:\"end\"===c?k-y:\"nearest\"===c?l(M,M+y,y,s,p,M+k,M+k+x,x):k-y/2,b=\"start\"===g?B:\"center\"===g?B-v/2:\"end\"===g?B-v:l(E,E+v,v,u,m,E+B,E+B+I,I),w=Math.max(0,w+M),b=Math.max(0,b+E);else{w=\"start\"===c?k-o-s:\"end\"===c?k-d+p+X:\"nearest\"===c?l(o,d,n,s,p+X,k,k+x,x):k-(o+n/2)+X/2,b=\"start\"===g?B-h-u:\"center\"===g?B-(h+i/2)+O/2:\"end\"===g?B-r+m+O:l(h,r,i,u,m+O,B,B+I,I);let{scrollLeft:e,scrollTop:f}=t;w=Math.max(0,Math.min(f+w/L,t.scrollHeight-n/L+X)),b=Math.max(0,Math.min(e+b/Y,t.scrollWidth-i/Y+O)),k+=f-w,B+=e-b}D.push({el:t,top:w,left:b})}return D};//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compute-scroll-into-view/dist/index.js\n");

/***/ })

};
;