"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-mdx-jsx";
exports.ids = ["vendor-chunks/micromark-extension-mdx-jsx"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/factory-tag.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-extension-mdx-jsx/dev/lib/factory-tag.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   factoryTag: () => (/* binding */ factoryTag)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! estree-util-is-identifier-name */ \"(ssr)/./node_modules/estree-util-is-identifier-name/lib/index.js\");\n/* harmony import */ var micromark_factory_mdx_expression__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-mdx-expression */ \"(ssr)/./node_modules/micromark-factory-mdx-expression/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {AcornOptions, Acorn} from 'micromark-util-events-to-acorn'\n * @import {Code, Effects, State, TokenType, TokenizeContext} from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\nconst trouble = 'https://github.com/micromark/micromark-extension-mdx-jsx'\n\n/**\n * @this {TokenizeContext}\n * @param {Effects} effects\n * @param {State} ok\n * @param {State} nok\n * @param {Acorn | null | undefined} acorn\n * @param {AcornOptions | null | undefined} acornOptions\n * @param {boolean | null | undefined} addResult\n * @param {boolean | undefined} allowLazy\n * @param {TokenType} tagType\n * @param {TokenType} tagMarkerType\n * @param {TokenType} tagClosingMarkerType\n * @param {TokenType} tagSelfClosingMarker\n * @param {TokenType} tagNameType\n * @param {TokenType} tagNamePrimaryType\n * @param {TokenType} tagNameMemberMarkerType\n * @param {TokenType} tagNameMemberType\n * @param {TokenType} tagNamePrefixMarkerType\n * @param {TokenType} tagNameLocalType\n * @param {TokenType} tagExpressionAttributeType\n * @param {TokenType} tagExpressionAttributeMarkerType\n * @param {TokenType} tagExpressionAttributeValueType\n * @param {TokenType} tagAttributeType\n * @param {TokenType} tagAttributeNameType\n * @param {TokenType} tagAttributeNamePrimaryType\n * @param {TokenType} tagAttributeNamePrefixMarkerType\n * @param {TokenType} tagAttributeNameLocalType\n * @param {TokenType} tagAttributeInitializerMarkerType\n * @param {TokenType} tagAttributeValueLiteralType\n * @param {TokenType} tagAttributeValueLiteralMarkerType\n * @param {TokenType} tagAttributeValueLiteralValueType\n * @param {TokenType} tagAttributeValueExpressionType\n * @param {TokenType} tagAttributeValueExpressionMarkerType\n * @param {TokenType} tagAttributeValueExpressionValueType\n */\n// eslint-disable-next-line max-params\nfunction factoryTag(\n  effects,\n  ok,\n  nok,\n  acorn,\n  acornOptions,\n  addResult,\n  allowLazy,\n  tagType,\n  tagMarkerType,\n  tagClosingMarkerType,\n  tagSelfClosingMarker,\n  tagNameType,\n  tagNamePrimaryType,\n  tagNameMemberMarkerType,\n  tagNameMemberType,\n  tagNamePrefixMarkerType,\n  tagNameLocalType,\n  tagExpressionAttributeType,\n  tagExpressionAttributeMarkerType,\n  tagExpressionAttributeValueType,\n  tagAttributeType,\n  tagAttributeNameType,\n  tagAttributeNamePrimaryType,\n  tagAttributeNamePrefixMarkerType,\n  tagAttributeNameLocalType,\n  tagAttributeInitializerMarkerType,\n  tagAttributeValueLiteralType,\n  tagAttributeValueLiteralMarkerType,\n  tagAttributeValueLiteralValueType,\n  tagAttributeValueExpressionType,\n  tagAttributeValueExpressionMarkerType,\n  tagAttributeValueExpressionValueType\n) {\n  const self = this\n  /** @type {State} */\n  let returnState\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n\n  return start\n\n  /**\n   * Start of MDX: JSX.\n   *\n   * ```markdown\n   * > | a <B /> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')\n    effects.enter(tagType)\n    effects.enter(tagMarkerType)\n    effects.consume(code)\n    effects.exit(tagMarkerType)\n    return startAfter\n  }\n\n  /**\n   * After `<`.\n   *\n   * ```markdown\n   * > | a <B /> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function startAfter(code) {\n    // Deviate from JSX, which allows arbitrary whitespace.\n    // See: <https://github.com/micromark/micromark-extension-mdx-jsx/issues/7>.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n      return nok(code)\n    }\n\n    // Any other ES whitespace does not get this treatment.\n    returnState = nameBefore\n    return esWhitespaceStart(code)\n  }\n\n  /**\n   * Before name, self slash, or end of tag for fragments.\n   *\n   * ```markdown\n   * > | a <B> c\n   *        ^\n   * > | a </B> c\n   *        ^\n   * > | a <> b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function nameBefore(code) {\n    // Closing tag.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {\n      effects.enter(tagClosingMarkerType)\n      effects.consume(code)\n      effects.exit(tagClosingMarkerType)\n      returnState = closingTagNameBefore\n      return esWhitespaceStart\n    }\n\n    // Fragment opening tag.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return tagEnd(code)\n    }\n\n    // Start of a name.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code)) {\n      effects.enter(tagNameType)\n      effects.enter(tagNamePrimaryType)\n      effects.consume(code)\n      return primaryName\n    }\n\n    crash(\n      code,\n      'before name',\n      'a character that can start a name, such as a letter, `$`, or `_`' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.exclamationMark\n          ? ' (note: to create a comment in MDX, use `{/* text */}`)'\n          : '')\n    )\n  }\n\n  /**\n   * Before name of closing tag or end of closing fragment tag.\n   *\n   * ```markdown\n   * > | a </> b\n   *         ^\n   * > | a </B> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function closingTagNameBefore(code) {\n    // Fragment closing tag.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return tagEnd(code)\n    }\n\n    // Start of a closing tag name.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code)) {\n      effects.enter(tagNameType)\n      effects.enter(tagNamePrimaryType)\n      effects.consume(code)\n      return primaryName\n    }\n\n    crash(\n      code,\n      'before name',\n      'a character that can start a name, such as a letter, `$`, or `_`' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash\n          ? ' (note: JS comments in JSX tags are not supported in MDX)'\n          : '')\n    )\n  }\n\n  /**\n   * In primary name.\n   *\n   * ```markdown\n   * > | a <Bc> d\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function primaryName(code) {\n    // Continuation of name: remain.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.cont)(code, {jsx: true})) {\n      effects.consume(code)\n      return primaryName\n    }\n\n    // End of name.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)\n    ) {\n      effects.exit(tagNamePrimaryType)\n      returnState = primaryNameAfter\n      return esWhitespaceStart(code)\n    }\n\n    crash(\n      code,\n      'in name',\n      'a name character such as letters, digits, `$`, or `_`; whitespace before attributes; or the end of the tag' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.atSign\n          ? ' (note: to create a link in MDX, use `[text](url)`)'\n          : '')\n    )\n  }\n\n  /**\n   * After primary name.\n   *\n   * ```markdown\n   * > | a <b.c> d\n   *         ^\n   * > | a <b:c> d\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function primaryNameAfter(code) {\n    // Start of a member name.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot) {\n      effects.enter(tagNameMemberMarkerType)\n      effects.consume(code)\n      effects.exit(tagNameMemberMarkerType)\n      returnState = memberNameBefore\n      return esWhitespaceStart\n    }\n\n    // Start of a local name.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      effects.enter(tagNamePrefixMarkerType)\n      effects.consume(code)\n      effects.exit(tagNamePrefixMarkerType)\n      returnState = localNameBefore\n      return esWhitespaceStart\n    }\n\n    // End of name.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code))\n    ) {\n      effects.exit(tagNameType)\n      return attributeBefore(code)\n    }\n\n    crash(\n      code,\n      'after name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * Before member name.\n   *\n   * ```markdown\n   * > | a <b.c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function memberNameBefore(code) {\n    // Start of a member name.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code)) {\n      effects.enter(tagNameMemberType)\n      effects.consume(code)\n      return memberName\n    }\n\n    crash(\n      code,\n      'before member name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * In member name.\n   *\n   * ```markdown\n   * > | a <b.cd> e\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function memberName(code) {\n    // Continuation of name: remain.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.cont)(code, {jsx: true})) {\n      effects.consume(code)\n      return memberName\n    }\n\n    // End of name.\n    // Note: no `:` allowed here.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)\n    ) {\n      effects.exit(tagNameMemberType)\n      returnState = memberNameAfter\n      return esWhitespaceStart(code)\n    }\n\n    crash(\n      code,\n      'in member name',\n      'a name character such as letters, digits, `$`, or `_`; whitespace before attributes; or the end of the tag' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.atSign\n          ? ' (note: to create a link in MDX, use `[text](url)`)'\n          : '')\n    )\n  }\n\n  /**\n   * After member name.\n   *\n   * ```markdown\n   * > | a <b.c> d\n   *           ^\n   * > | a <b.c.d> e\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function memberNameAfter(code) {\n    // Start another member name.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot) {\n      effects.enter(tagNameMemberMarkerType)\n      effects.consume(code)\n      effects.exit(tagNameMemberMarkerType)\n      returnState = memberNameBefore\n      return esWhitespaceStart\n    }\n\n    // End of name.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code))\n    ) {\n      effects.exit(tagNameType)\n      return attributeBefore(code)\n    }\n\n    crash(\n      code,\n      'after member name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * Local member name.\n   *\n   * ```markdown\n   * > | a <b:c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function localNameBefore(code) {\n    // Start of a local name.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code)) {\n      effects.enter(tagNameLocalType)\n      effects.consume(code)\n      return localName\n    }\n\n    crash(\n      code,\n      'before local name',\n      'a character that can start a name, such as a letter, `$`, or `_`' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||\n        (code !== null &&\n          code > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dot &&\n          code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) /* `/` - `9` */\n          ? ' (note: to create a link in MDX, use `[text](url)`)'\n          : '')\n    )\n  }\n\n  /**\n   * In local name.\n   *\n   * ```markdown\n   * > | a <b:cd> e\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function localName(code) {\n    // Continuation of name: remain.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.cont)(code, {jsx: true})) {\n      effects.consume(code)\n      return localName\n    }\n\n    // End of local name (note that we don’t expect another colon, or a member).\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)\n    ) {\n      effects.exit(tagNameLocalType)\n      returnState = localNameAfter\n      return esWhitespaceStart(code)\n    }\n\n    crash(\n      code,\n      'in local name',\n      'a name character such as letters, digits, `$`, or `_`; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * After local name.\n   *\n   * This is like as `primary_name_after`, but we don’t expect colons or\n   * periods.\n   *\n   * ```markdown\n   * > | a <b.c> d\n   *           ^\n   * > | a <b.c.d> e\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function localNameAfter(code) {\n    // End of name.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code))\n    ) {\n      effects.exit(tagNameType)\n      return attributeBefore(code)\n    }\n\n    crash(\n      code,\n      'after local name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * Before attribute.\n   *\n   * ```markdown\n   * > | a <b /> c\n   *          ^\n   * > | a <b > c\n   *          ^\n   * > | a <b {...c}> d\n   *          ^\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeBefore(code) {\n    // Self-closing.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {\n      effects.enter(tagSelfClosingMarker)\n      effects.consume(code)\n      effects.exit(tagSelfClosingMarker)\n      returnState = selfClosing\n      return esWhitespaceStart\n    }\n\n    // End of tag.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return tagEnd(code)\n    }\n\n    // Attribute expression.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace) {\n      return micromark_factory_mdx_expression__WEBPACK_IMPORTED_MODULE_4__.factoryMdxExpression.call(\n        self,\n        effects,\n        attributeExpressionAfter,\n        tagExpressionAttributeType,\n        tagExpressionAttributeMarkerType,\n        tagExpressionAttributeValueType,\n        acorn,\n        acornOptions,\n        addResult,\n        true,\n        false,\n        allowLazy\n      )(code)\n    }\n\n    // Start of an attribute name.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code)) {\n      effects.enter(tagAttributeType)\n      effects.enter(tagAttributeNameType)\n      effects.enter(tagAttributeNamePrimaryType)\n      effects.consume(code)\n      return attributePrimaryName\n    }\n\n    crash(\n      code,\n      'before attribute name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * After attribute expression.\n   *\n   * ```markdown\n   * > | a <b {c} d/> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeExpressionAfter(code) {\n    returnState = attributeBefore\n    return esWhitespaceStart(code)\n  }\n\n  /**\n   * In primary attribute name.\n   *\n   * ```markdown\n   * > | a <b cd/> e\n   *           ^\n   * > | a <b c:d> e\n   *           ^\n   * > | a <b c=d> e\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributePrimaryName(code) {\n    // Continuation of name: remain.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.cont)(code, {jsx: true})) {\n      effects.consume(code)\n      return attributePrimaryName\n    }\n\n    // End of attribute name or tag.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)\n    ) {\n      effects.exit(tagAttributeNamePrimaryType)\n      returnState = attributePrimaryNameAfter\n      return esWhitespaceStart(code)\n    }\n\n    crash(\n      code,\n      'in attribute name',\n      'an attribute name character such as letters, digits, `$`, or `_`; `=` to initialize a value; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * After primary attribute name.\n   *\n   * ```markdown\n   * > | a <b c/> d\n   *           ^\n   * > | a <b c:d> e\n   *           ^\n   * > | a <b c=d> e\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributePrimaryNameAfter(code) {\n    // Start of a local name.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      effects.enter(tagAttributeNamePrefixMarkerType)\n      effects.consume(code)\n      effects.exit(tagAttributeNamePrefixMarkerType)\n      returnState = attributeLocalNameBefore\n      return esWhitespaceStart\n    }\n\n    // Initializer: start of an attribute value.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo) {\n      effects.exit(tagAttributeNameType)\n      effects.enter(tagAttributeInitializerMarkerType)\n      effects.consume(code)\n      effects.exit(tagAttributeInitializerMarkerType)\n      returnState = attributeValueBefore\n      return esWhitespaceStart\n    }\n\n    // End of tag / new attribute.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code) ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code))\n    ) {\n      effects.exit(tagAttributeNameType)\n      effects.exit(tagAttributeType)\n      returnState = attributeBefore\n      return esWhitespaceStart(code)\n    }\n\n    crash(\n      code,\n      'after attribute name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; `=` to initialize a value; or the end of the tag'\n    )\n  }\n\n  /**\n   * Before local attribute name.\n   *\n   * ```markdown\n   * > | a <b c:d/> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeLocalNameBefore(code) {\n    // Start of a local name.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code)) {\n      effects.enter(tagAttributeNameLocalType)\n      effects.consume(code)\n      return attributeLocalName\n    }\n\n    crash(\n      code,\n      'before local attribute name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; `=` to initialize a value; or the end of the tag'\n    )\n  }\n\n  /**\n   * In local attribute name.\n   *\n   * ```markdown\n   * > | a <b c:de/> f\n   *             ^\n   * > | a <b c:d=e/> f\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeLocalName(code) {\n    // Continuation of name: remain.\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.cont)(code, {jsx: true})) {\n      effects.consume(code)\n      return attributeLocalName\n    }\n\n    // End of local name (note that we don’t expect another colon).\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code) ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)\n    ) {\n      effects.exit(tagAttributeNameLocalType)\n      effects.exit(tagAttributeNameType)\n      returnState = attributeLocalNameAfter\n      return esWhitespaceStart(code)\n    }\n\n    crash(\n      code,\n      'in local attribute name',\n      'an attribute name character such as letters, digits, `$`, or `_`; `=` to initialize a value; whitespace before attributes; or the end of the tag'\n    )\n  }\n\n  /**\n   * After local attribute name.\n   *\n   * ```markdown\n   * > | a <b c:d/> f\n   *             ^\n   * > | a <b c:d=e/> f\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeLocalNameAfter(code) {\n    // Start of an attribute value.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo) {\n      effects.enter(tagAttributeInitializerMarkerType)\n      effects.consume(code)\n      effects.exit(tagAttributeInitializerMarkerType)\n      returnState = attributeValueBefore\n      return esWhitespaceStart\n    }\n\n    // End of name.\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace ||\n      (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && code >= 0 && (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_3__.start)(code))\n    ) {\n      effects.exit(tagAttributeType)\n      return attributeBefore(code)\n    }\n\n    crash(\n      code,\n      'after local attribute name',\n      'a character that can start an attribute name, such as a letter, `$`, or `_`; `=` to initialize a value; or the end of the tag'\n    )\n  }\n\n  /**\n   * After `=`, before value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"/> e\n   *            ^\n   * > | a <b c={d}/> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeValueBefore(code) {\n    // Start of double- or single quoted value.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe) {\n      effects.enter(tagAttributeValueLiteralType)\n      effects.enter(tagAttributeValueLiteralMarkerType)\n      effects.consume(code)\n      effects.exit(tagAttributeValueLiteralMarkerType)\n      marker = code\n      return attributeValueQuotedStart\n    }\n\n    // Attribute value expression.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace) {\n      return micromark_factory_mdx_expression__WEBPACK_IMPORTED_MODULE_4__.factoryMdxExpression.call(\n        self,\n        effects,\n        attributeValueExpressionAfter,\n        tagAttributeValueExpressionType,\n        tagAttributeValueExpressionMarkerType,\n        tagAttributeValueExpressionValueType,\n        acorn,\n        acornOptions,\n        addResult,\n        false,\n        false,\n        allowLazy\n      )(code)\n    }\n\n    crash(\n      code,\n      'before attribute value',\n      'a character that can start an attribute value, such as `\"`, `\\'`, or `{`' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan\n          ? ' (note: to use an element or fragment as a prop value in MDX, use `{<element />}`)'\n          : '')\n    )\n  }\n\n  /**\n   * After attribute value expression.\n   *\n   * ```markdown\n   * > | a <b c={d} e/> f\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeValueExpressionAfter(code) {\n    effects.exit(tagAttributeType)\n    returnState = attributeBefore\n    return esWhitespaceStart(code)\n  }\n\n  /**\n   * Before quoted literal attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"/> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeValueQuotedStart(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(marker !== undefined, 'expected `marker` to be defined')\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      crash(\n        code,\n        'in attribute value',\n        'a corresponding closing quote `' + String.fromCodePoint(marker) + '`'\n      )\n    }\n\n    if (code === marker) {\n      effects.enter(tagAttributeValueLiteralMarkerType)\n      effects.consume(code)\n      effects.exit(tagAttributeValueLiteralMarkerType)\n      effects.exit(tagAttributeValueLiteralType)\n      effects.exit(tagAttributeType)\n      marker = undefined\n      returnState = attributeBefore\n      return esWhitespaceStart\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      returnState = attributeValueQuotedStart\n      return esWhitespaceStart(code)\n    }\n\n    effects.enter(tagAttributeValueLiteralValueType)\n    return attributeValueQuoted(code)\n  }\n\n  /**\n   * In quoted literal attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"/> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function attributeValueQuoted(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === marker || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.exit(tagAttributeValueLiteralValueType)\n      return attributeValueQuotedStart(code)\n    }\n\n    effects.consume(code)\n    return attributeValueQuoted\n  }\n\n  /**\n   * After self-closing slash.\n   *\n   * ```markdown\n   * > | a <b/> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function selfClosing(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return tagEnd(code)\n    }\n\n    crash(\n      code,\n      'after self-closing slash',\n      '`>` to end the tag' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.slash\n          ? ' (note: JS comments in JSX tags are not supported in MDX)'\n          : '')\n    )\n  }\n\n  /**\n   * At final `>`.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagEnd(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan, 'expected `>`')\n    effects.enter(tagMarkerType)\n    effects.consume(code)\n    effects.exit(tagMarkerType)\n    effects.exit(tagType)\n    return ok\n  }\n\n  /**\n   * Before optional ECMAScript whitespace.\n   *\n   * ```markdown\n   * > | a <a b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function esWhitespaceStart(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.types.lineEnding)\n      return esWhitespaceEolAfter\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)) {\n      effects.enter('esWhitespace')\n      return esWhitespaceInside(code)\n    }\n\n    return returnState(code)\n  }\n\n  /**\n   * In ECMAScript whitespace.\n   *\n   * ```markdown\n   * > | a <a  b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function esWhitespaceInside(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.exit('esWhitespace')\n      return esWhitespaceStart(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.unicodeWhitespace)(code)) {\n      effects.consume(code)\n      return esWhitespaceInside\n    }\n\n    effects.exit('esWhitespace')\n    return returnState(code)\n  }\n\n  /**\n   * After eol in whitespace.\n   *\n   * ```markdown\n   * > | a <a\\nb> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function esWhitespaceEolAfter(code) {\n    // Lazy continuation in a flow tag is a syntax error.\n    if (!allowLazy && self.parser.lazy[self.now().line]) {\n      const error = new vfile_message__WEBPACK_IMPORTED_MODULE_6__.VFileMessage(\n        'Unexpected lazy line in container, expected line to be prefixed with `>` when in a block quote, whitespace when in a list, etc',\n        self.now(),\n        'micromark-extension-mdx-jsx:unexpected-lazy'\n      )\n      error.url =\n        trouble + '#unexpected-lazy-line-in-container-expected-line-to-be'\n      throw error\n    }\n\n    return esWhitespaceStart(code)\n  }\n\n  /**\n   * Crash at a nonconforming character.\n   *\n   * @param {Code} code\n   * @param {string} at\n   * @param {string} expect\n   */\n  function crash(code, at, expect) {\n    const error = new vfile_message__WEBPACK_IMPORTED_MODULE_6__.VFileMessage(\n      'Unexpected ' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof\n          ? 'end of file'\n          : 'character `' +\n            (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent\n              ? '` ` `'\n              : String.fromCodePoint(code)) +\n            '` (' +\n            serializeCharCode(code) +\n            ')') +\n        ' ' +\n        at +\n        ', expected ' +\n        expect,\n      self.now(),\n      'micromark-extension-mdx-jsx:unexpected-' +\n        (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ? 'eof' : 'character')\n    )\n    error.url =\n      trouble +\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof\n        ? '#unexpected-end-of-file-at-expected-expect'\n        : '#unexpected-character-at-expected-expect')\n    throw error\n  }\n}\n\n/**\n * @param {NonNullable<Code>} code\n * @returns {string}\n */\nfunction serializeCharCode(code) {\n  return (\n    'U+' +\n    code\n      .toString(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseHexadecimal)\n      .toUpperCase()\n      .padStart(4, '0')\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/factory-tag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-flow.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-flow.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jsxFlow: () => (/* binding */ jsxFlow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var _factory_tag_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./factory-tag.js */ \"(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/factory-tag.js\");\n/**\n * @import {Options} from 'micromark-extension-mdx-jsx'\n * @import {Acorn} from 'micromark-util-events-to-acorn'\n * @import {Construct, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/**\n * Parse JSX (flow).\n *\n * @param {Acorn | undefined} acorn\n *   Acorn parser to use (optional).\n * @param {Options} options\n *   Configuration.\n * @returns {Construct}\n *   Construct.\n */\nfunction jsxFlow(acorn, options) {\n  return {concrete: true, name: 'mdxJsxFlowTag', tokenize: tokenizeJsxFlow}\n\n  /**\n   * MDX JSX (flow).\n   *\n   * ```markdown\n   * > | <A />\n   *     ^^^^^\n   * ```\n   *\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeJsxFlow(effects, ok, nok) {\n    const self = this\n\n    return start\n\n    /**\n     * Start of MDX: JSX (flow).\n     *\n     * ```markdown\n     * > | <A />\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // To do: in `markdown-rs`, constructs need to parse the indent themselves.\n      // This should also be introduced in `micromark-js`.\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')\n      return before(code)\n    }\n\n    /**\n     * After optional whitespace, before of MDX JSX (flow).\n     *\n     * ```markdown\n     * > | <A />\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function before(code) {\n      return _factory_tag_js__WEBPACK_IMPORTED_MODULE_2__.factoryTag.call(\n        self,\n        effects,\n        after,\n        nok,\n        acorn,\n        options.acornOptions,\n        options.addResult,\n        false,\n        'mdxJsxFlowTag',\n        'mdxJsxFlowTagMarker',\n        'mdxJsxFlowTagClosingMarker',\n        'mdxJsxFlowTagSelfClosingMarker',\n        'mdxJsxFlowTagName',\n        'mdxJsxFlowTagNamePrimary',\n        'mdxJsxFlowTagNameMemberMarker',\n        'mdxJsxFlowTagNameMember',\n        'mdxJsxFlowTagNamePrefixMarker',\n        'mdxJsxFlowTagNameLocal',\n        'mdxJsxFlowTagExpressionAttribute',\n        'mdxJsxFlowTagExpressionAttributeMarker',\n        'mdxJsxFlowTagExpressionAttributeValue',\n        'mdxJsxFlowTagAttribute',\n        'mdxJsxFlowTagAttributeName',\n        'mdxJsxFlowTagAttributeNamePrimary',\n        'mdxJsxFlowTagAttributeNamePrefixMarker',\n        'mdxJsxFlowTagAttributeNameLocal',\n        'mdxJsxFlowTagAttributeInitializerMarker',\n        'mdxJsxFlowTagAttributeValueLiteral',\n        'mdxJsxFlowTagAttributeValueLiteralMarker',\n        'mdxJsxFlowTagAttributeValueLiteralValue',\n        'mdxJsxFlowTagAttributeValueExpression',\n        'mdxJsxFlowTagAttributeValueExpressionMarker',\n        'mdxJsxFlowTagAttributeValueExpressionValue'\n      )(code)\n    }\n\n    /**\n     * After an MDX JSX (flow) tag.\n     *\n     * ```markdown\n     * > | <A>\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function after(code) {\n      return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)\n        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, end, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.types.whitespace)(code)\n        : end(code)\n    }\n\n    /**\n     * After an MDX JSX (flow) tag, after optional whitespace.\n     *\n     * ```markdown\n     * > | <A> <B>\n     *         ^\n     * ```\n     *\n     * @type {State}\n     */\n    function end(code) {\n      // We want to allow expressions directly after tags.\n      // See <https://github.com/micromark/micromark-extension-mdx-expression/blob/d5d92b9/packages/micromark-extension-mdx-expression/dev/lib/syntax.js#L183>\n      // for more info.\n      const leftBraceValue = self.parser.constructs.flow[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace]\n      /* c8 ignore next 5 -- always a list when normalized. */\n      const constructs = Array.isArray(leftBraceValue)\n        ? leftBraceValue\n        : leftBraceValue\n          ? [leftBraceValue]\n          : []\n      /** @type {Construct | undefined} */\n      let expression\n\n      for (const construct of constructs) {\n        if (construct.name === 'mdxFlowExpression') {\n          expression = construct\n          break\n        }\n      }\n\n      // Another tag.\n      return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan\n        ? // We can’t just say: fine. Lines of blocks have to be parsed until an eol/eof.\n          start(code)\n        : code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace && expression\n          ? effects.attempt(expression, end, nok)(code)\n          : code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)\n            ? ok(code)\n            : nok(code)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-text.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-text.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jsxText: () => (/* binding */ jsxText)\n/* harmony export */ });\n/* harmony import */ var _factory_tag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./factory-tag.js */ \"(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/factory-tag.js\");\n/**\n * @import {Options} from 'micromark-extension-mdx-jsx'\n * @import {Acorn} from 'micromark-util-events-to-acorn'\n * @import {Construct, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n/**\n * Parse JSX (text).\n *\n * @param {Acorn | undefined} acorn\n *   Acorn parser to use (optional).\n * @param {Options} options\n *   Configuration.\n * @returns {Construct}\n *   Construct.\n */\nfunction jsxText(acorn, options) {\n  return {name: 'mdxJsxTextTag', tokenize: tokenizeJsxText}\n\n  /**\n   * MDX JSX (text).\n   *\n   * ```markdown\n   * > | a <b />.\n   *       ^^^^^\n   * ```\n   *\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeJsxText(effects, ok, nok) {\n    return _factory_tag_js__WEBPACK_IMPORTED_MODULE_0__.factoryTag.call(\n      this,\n      effects,\n      ok,\n      nok,\n      acorn,\n      options.acornOptions,\n      options.addResult,\n      true,\n      'mdxJsxTextTag',\n      'mdxJsxTextTagMarker',\n      'mdxJsxTextTagClosingMarker',\n      'mdxJsxTextTagSelfClosingMarker',\n      'mdxJsxTextTagName',\n      'mdxJsxTextTagNamePrimary',\n      'mdxJsxTextTagNameMemberMarker',\n      'mdxJsxTextTagNameMember',\n      'mdxJsxTextTagNamePrefixMarker',\n      'mdxJsxTextTagNameLocal',\n      'mdxJsxTextTagExpressionAttribute',\n      'mdxJsxTextTagExpressionAttributeMarker',\n      'mdxJsxTextTagExpressionAttributeValue',\n      'mdxJsxTextTagAttribute',\n      'mdxJsxTextTagAttributeName',\n      'mdxJsxTextTagAttributeNamePrimary',\n      'mdxJsxTextTagAttributeNamePrefixMarker',\n      'mdxJsxTextTagAttributeNameLocal',\n      'mdxJsxTextTagAttributeInitializerMarker',\n      'mdxJsxTextTagAttributeValueLiteral',\n      'mdxJsxTextTagAttributeValueLiteralMarker',\n      'mdxJsxTextTagAttributeValueLiteralValue',\n      'mdxJsxTextTagAttributeValueExpression',\n      'mdxJsxTextTagAttributeValueExpressionMarker',\n      'mdxJsxTextTagAttributeValueExpressionValue'\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/syntax.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-mdx-jsx/dev/lib/syntax.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mdxJsx: () => (/* binding */ mdxJsx)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _jsx_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./jsx-text.js */ \"(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-text.js\");\n/* harmony import */ var _jsx_flow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./jsx-flow.js */ \"(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/jsx-flow.js\");\n/**\n * @import {Options} from 'micromark-extension-mdx-jsx'\n * @import {AcornOptions} from 'micromark-util-events-to-acorn'\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/**\n * Create an extension for `micromark` to enable MDX JSX syntax.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable MDX\n *   JSX syntax.\n */\nfunction mdxJsx(options) {\n  const settings = options || {}\n  const acorn = settings.acorn\n  /** @type {AcornOptions | undefined} */\n  let acornOptions\n\n  if (acorn) {\n    if (!acorn.parse || !acorn.parseExpressionAt) {\n      throw new Error(\n        'Expected a proper `acorn` instance passed in as `options.acorn`'\n      )\n    }\n\n    acornOptions = Object.assign(\n      {ecmaVersion: 2024, sourceType: 'module'},\n      settings.acornOptions,\n      {locations: true}\n    )\n  } else if (settings.acornOptions || settings.addResult) {\n    throw new Error('Expected an `acorn` instance passed in as `options.acorn`')\n  }\n\n  return {\n    flow: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: (0,_jsx_flow_js__WEBPACK_IMPORTED_MODULE_1__.jsxFlow)(acorn || undefined, {\n        acornOptions,\n        addResult: settings.addResult || undefined\n      })\n    },\n    text: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: (0,_jsx_text_js__WEBPACK_IMPORTED_MODULE_2__.jsxText)(acorn || undefined, {\n        acornOptions,\n        addResult: settings.addResult || undefined\n      })\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-mdx-jsx/dev/lib/syntax.js\n");

/***/ })

};
;