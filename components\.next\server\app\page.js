/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjcmVhdGUtbGxsYW1hJTVDJTVDcHlsbGFtYWluZGV4JTVDJTVDY29tcG9uZW50cyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNyZWF0ZS1sbGxhbWFcXFxccHlsbGFtYWluZGV4XFxcXGNvbXBvbmVudHNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjcmVhdGUtbGxsYW1hJTVDJTVDcHlsbGFtYWluZGV4JTVDJTVDY29tcG9uZW50cyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBK0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGNyZWF0ZS1sbGxhbWFcXFxccHlsbGFtYWluZGV4XFxcXGNvbXBvbmVudHNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cmarkdown.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ccreate-lllama%5C%5Cpyllamaindex%5C%5Ccomponents%5C%5Cnode_modules%5C%5C%40llamaindex%5C%5Cchat-ui%5C%5Cdist%5C%5Cstyles%5C%5Cpdf.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/CitationTooltip.tsx":
/*!********************************************!*\
  !*** ./app/components/CitationTooltip.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CitationTooltip: () => (/* binding */ CitationTooltip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./app/components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ CitationTooltip auto */ \n\n\nfunction CitationTooltip({ citationId, children }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [citationData, setCitationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const loadCitationData = async ()=>{\n        if (citationData || isLoading) return;\n        setIsLoading(true);\n        try {\n            const response = await fetch(`/api/citation/${citationId}`);\n            if (!response.ok) throw new Error(\"Failed to load citation\");\n            const data = await response.json();\n            setCitationData({\n                id: data.id || citationId,\n                title: data.metadata?.file_name || `Document ${citationId.substring(0, 8)}...`,\n                content: data.text || data.content || \"内容不可用\",\n                metadata: data.metadata\n            });\n        } catch (error) {\n            console.error(\"Error loading citation:\", error);\n            setCitationData({\n                id: citationId,\n                title: \"Error\",\n                content: \"Failed to load citation content\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        const rect = triggerRef.current?.getBoundingClientRect();\n        if (rect) {\n            setPosition({\n                x: rect.left + rect.width / 2,\n                y: rect.top - 10\n            });\n        }\n        setIsVisible(true);\n        loadCitationData();\n    };\n    const handleMouseLeave = ()=>{\n        setIsVisible(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CitationTooltip.useEffect\": ()=>{\n            if (isVisible && tooltipRef.current) {\n                const tooltip = tooltipRef.current;\n                const rect = tooltip.getBoundingClientRect();\n                const viewportWidth = window.innerWidth;\n                const viewportHeight = window.innerHeight;\n                let adjustedX = position.x - rect.width / 2;\n                let adjustedY = position.y - rect.height;\n                // 确保tooltip不超出视口\n                if (adjustedX < 10) adjustedX = 10;\n                if (adjustedX + rect.width > viewportWidth - 10) {\n                    adjustedX = viewportWidth - rect.width - 10;\n                }\n                if (adjustedY < 10) adjustedY = position.y + 30;\n                tooltip.style.left = `${adjustedX}px`;\n                tooltip.style.top = `${adjustedY}px`;\n            }\n        }\n    }[\"CitationTooltip.useEffect\"], [\n        isVisible,\n        position,\n        citationData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                ref: triggerRef,\n                onMouseEnter: handleMouseEnter,\n                onMouseLeave: handleMouseLeave,\n                className: \"citation-number\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: tooltipRef,\n                className: \"citation-tooltip fixed z-50\",\n                style: {\n                    left: position.x,\n                    top: position.y\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                            size: \"sm\",\n                            className: \"text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 13\n                }, this) : citationData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-gray-900 mb-2\",\n                            children: citationData.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-700 text-sm leading-relaxed\",\n                            children: citationData.content.length > 300 ? `${citationData.content.substring(0, 300)}...` : citationData.content\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Error loading citation\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/CitationTooltip.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/CustomChatMessage.tsx":
/*!**********************************************!*\
  !*** ./app/components/CustomChatMessage.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomChatMessage: () => (/* binding */ CustomChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @llamaindex/chat-ui */ \"(ssr)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var _CitationTooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CitationTooltip */ \"(ssr)/./app/components/CitationTooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomChatMessage auto */ \n\n\nfunction CustomChatMessage({ message, isLoading }) {\n    // 处理引用标记的函数\n    const processContent = (content)=>{\n        // 查找所有引用标记\n        const citationRegex = /\\[citation:(.*?)\\]/g;\n        const citations = [];\n        let match;\n        while((match = citationRegex.exec(content)) !== null){\n            citations.push(match[1]);\n        }\n        // 替换引用标记为带tooltip的序号\n        let processedContent = content;\n        citations.forEach((citationId, index)=>{\n            const citationNumber = index + 1;\n            const citationPattern = new RegExp(`\\\\[citation:${citationId.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\]`, 'g');\n            processedContent = processedContent.replace(citationPattern, `<citation-placeholder data-citation-id=\"${citationId}\" data-citation-number=\"${citationNumber}\"></citation-placeholder>`);\n        });\n        return processedContent;\n    };\n    // 渲染处理后的内容\n    const renderContent = (content)=>{\n        const processedContent = processContent(content);\n        // 分割内容并处理引用占位符\n        const parts = processedContent.split(/(<citation-placeholder[^>]*><\\/citation-placeholder>)/g);\n        return parts.map((part, index)=>{\n            const citationMatch = part.match(/<citation-placeholder data-citation-id=\"([^\"]*)\" data-citation-number=\"([^\"]*)\"><\\/citation-placeholder>/);\n            if (citationMatch) {\n                const [, citationId, citationNumber] = citationMatch;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CitationTooltip__WEBPACK_IMPORTED_MODULE_1__.CitationTooltip, {\n                    citationId: citationId,\n                    children: [\n                        \"[\",\n                        citationNumber,\n                        \"]\"\n                    ]\n                }, `citation-${index}`, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this);\n            }\n            return part;\n        });\n    };\n    // 如果是助手消息且包含引用，使用自定义渲染\n    if (message.role === 'assistant' && message.content.includes('[citation:')) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} mb-4`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `max-w-[80%] rounded-lg px-4 py-2 ${message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-sm max-w-none\",\n                        children: renderContent(message.content)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: \"Thinking...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // 对于其他消息，使用默认的ChatMessage组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n        message: message,\n        isLoading: isLoading\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n        lineNumber: 86,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/CustomChatMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./app/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center p-6 bg-red-50 border border-red-200 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold text-red-800 mb-2\",\n                children: \"Something went wrong\"\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-600 text-sm mb-4 text-center\",\n                children: error?.message || 'An unexpected error occurred'\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: resetError,\n                className: \"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors\",\n                children: \"Try again\"\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9FcnJvckJvdW5kYXJ5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFeUI7QUFZbEIsTUFBTUMsc0JBQXNCRCx3REFBZTtJQUNoREcsWUFBWUMsS0FBeUIsQ0FBRTtRQUNyQyxLQUFLLENBQUNBLGFBWVJDLGFBQWE7WUFDWCxJQUFJLENBQUNDLFFBQVEsQ0FBQztnQkFBRUMsVUFBVTtnQkFBT0MsT0FBT0M7WUFBVTtRQUNwRDtRQWJFLElBQUksQ0FBQ0MsS0FBSyxHQUFHO1lBQUVILFVBQVU7UUFBTTtJQUNqQztJQUVBLE9BQU9JLHlCQUF5QkgsS0FBWSxFQUFzQjtRQUNoRSxPQUFPO1lBQUVELFVBQVU7WUFBTUM7UUFBTTtJQUNqQztJQUVBSSxrQkFBa0JKLEtBQVksRUFBRUssU0FBMEIsRUFBRTtRQUMxREMsUUFBUU4sS0FBSyxDQUFDLGtDQUFrQ0EsT0FBT0s7SUFDekQ7SUFNQUUsU0FBUztRQUNQLElBQUksSUFBSSxDQUFDTCxLQUFLLENBQUNILFFBQVEsRUFBRTtZQUN2QixNQUFNUyxvQkFBb0IsSUFBSSxDQUFDWixLQUFLLENBQUNhLFFBQVEsSUFBSUM7WUFDakQscUJBQU8sOERBQUNGO2dCQUFrQlIsT0FBTyxJQUFJLENBQUNFLEtBQUssQ0FBQ0YsS0FBSztnQkFBRUgsWUFBWSxJQUFJLENBQUNBLFVBQVU7Ozs7OztRQUNoRjtRQUVBLE9BQU8sSUFBSSxDQUFDRCxLQUFLLENBQUNlLFFBQVE7SUFDNUI7QUFDRjtBQUVBLFNBQVNELHFCQUFxQixFQUFFVixLQUFLLEVBQUVILFVBQVUsRUFBNkM7SUFDNUYscUJBQ0UsOERBQUNlO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBMEM7Ozs7OzswQkFDeEQsOERBQUNFO2dCQUFFRixXQUFVOzBCQUNWYixPQUFPZ0IsV0FBVzs7Ozs7OzBCQUVyQiw4REFBQ0M7Z0JBQ0NDLFNBQVNyQjtnQkFDVGdCLFdBQVU7MEJBQ1g7Ozs7Ozs7Ozs7OztBQUtQIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxhcHBcXGNvbXBvbmVudHNcXEVycm9yQm91bmRhcnkudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBFcnJvckJvdW5kYXJ5U3RhdGUge1xuICBoYXNFcnJvcjogYm9vbGVhblxuICBlcnJvcj86IEVycm9yXG59XG5cbmludGVyZmFjZSBFcnJvckJvdW5kYXJ5UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG4gIGZhbGxiYWNrPzogUmVhY3QuQ29tcG9uZW50VHlwZTx7IGVycm9yPzogRXJyb3I7IHJlc2V0RXJyb3I6ICgpID0+IHZvaWQgfT5cbn1cblxuZXhwb3J0IGNsYXNzIEVycm9yQm91bmRhcnkgZXh0ZW5kcyBSZWFjdC5Db21wb25lbnQ8RXJyb3JCb3VuZGFyeVByb3BzLCBFcnJvckJvdW5kYXJ5U3RhdGU+IHtcbiAgY29uc3RydWN0b3IocHJvcHM6IEVycm9yQm91bmRhcnlQcm9wcykge1xuICAgIHN1cGVyKHByb3BzKVxuICAgIHRoaXMuc3RhdGUgPSB7IGhhc0Vycm9yOiBmYWxzZSB9XG4gIH1cblxuICBzdGF0aWMgZ2V0RGVyaXZlZFN0YXRlRnJvbUVycm9yKGVycm9yOiBFcnJvcik6IEVycm9yQm91bmRhcnlTdGF0ZSB7XG4gICAgcmV0dXJuIHsgaGFzRXJyb3I6IHRydWUsIGVycm9yIH1cbiAgfVxuXG4gIGNvbXBvbmVudERpZENhdGNoKGVycm9yOiBFcnJvciwgZXJyb3JJbmZvOiBSZWFjdC5FcnJvckluZm8pIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvckJvdW5kYXJ5IGNhdWdodCBhbiBlcnJvcjonLCBlcnJvciwgZXJyb3JJbmZvKVxuICB9XG5cbiAgcmVzZXRFcnJvciA9ICgpID0+IHtcbiAgICB0aGlzLnNldFN0YXRlKHsgaGFzRXJyb3I6IGZhbHNlLCBlcnJvcjogdW5kZWZpbmVkIH0pXG4gIH1cblxuICByZW5kZXIoKSB7XG4gICAgaWYgKHRoaXMuc3RhdGUuaGFzRXJyb3IpIHtcbiAgICAgIGNvbnN0IEZhbGxiYWNrQ29tcG9uZW50ID0gdGhpcy5wcm9wcy5mYWxsYmFjayB8fCBEZWZhdWx0RXJyb3JGYWxsYmFja1xuICAgICAgcmV0dXJuIDxGYWxsYmFja0NvbXBvbmVudCBlcnJvcj17dGhpcy5zdGF0ZS5lcnJvcn0gcmVzZXRFcnJvcj17dGhpcy5yZXNldEVycm9yfSAvPlxuICAgIH1cblxuICAgIHJldHVybiB0aGlzLnByb3BzLmNoaWxkcmVuXG4gIH1cbn1cblxuZnVuY3Rpb24gRGVmYXVsdEVycm9yRmFsbGJhY2soeyBlcnJvciwgcmVzZXRFcnJvciB9OiB7IGVycm9yPzogRXJyb3I7IHJlc2V0RXJyb3I6ICgpID0+IHZvaWQgfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC02IGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXJlZC04MDAgbWItMlwiPlNvbWV0aGluZyB3ZW50IHdyb25nPC9oMj5cbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCB0ZXh0LXNtIG1iLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAge2Vycm9yPy5tZXNzYWdlIHx8ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJ31cbiAgICAgIDwvcD5cbiAgICAgIDxidXR0b25cbiAgICAgICAgb25DbGljaz17cmVzZXRFcnJvcn1cbiAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLXJlZC02MDAgdGV4dC13aGl0ZSByb3VuZGVkIGhvdmVyOmJnLXJlZC03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgPlxuICAgICAgICBUcnkgYWdhaW5cbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJFcnJvckJvdW5kYXJ5IiwiQ29tcG9uZW50IiwiY29uc3RydWN0b3IiLCJwcm9wcyIsInJlc2V0RXJyb3IiLCJzZXRTdGF0ZSIsImhhc0Vycm9yIiwiZXJyb3IiLCJ1bmRlZmluZWQiLCJzdGF0ZSIsImdldERlcml2ZWRTdGF0ZUZyb21FcnJvciIsImNvbXBvbmVudERpZENhdGNoIiwiZXJyb3JJbmZvIiwiY29uc29sZSIsInJlbmRlciIsIkZhbGxiYWNrQ29tcG9uZW50IiwiZmFsbGJhY2siLCJEZWZhdWx0RXJyb3JGYWxsYmFjayIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwibWVzc2FnZSIsImJ1dHRvbiIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/LoadingSpinner.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingSpinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner auto */ \nfunction LoadingSpinner({ size = 'md', className = '' }) {\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} border-2 border-current border-t-transparent rounded-full animate-spin ${className}`\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQU9PLFNBQVNBLGVBQWUsRUFBRUMsT0FBTyxJQUFJLEVBQUVDLFlBQVksRUFBRSxFQUF1QjtJQUNqRixNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsR0FBR0MsV0FBVyxDQUFDRixLQUFLLENBQUMsd0VBQXdFLEVBQUVDLFdBQVc7Ozs7OztBQUU5SCIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcYXBwXFxjb21wb25lbnRzXFxMb2FkaW5nU3Bpbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmludGVyZmFjZSBMb2FkaW5nU3Bpbm5lclByb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZCcsIGNsYXNzTmFtZSA9ICcnIH06IExvYWRpbmdTcGlubmVyUHJvcHMpIHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICd3LTQgaC00JyxcbiAgICBtZDogJ3ctNiBoLTYnLCBcbiAgICBsZzogJ3ctOCBoLTgnXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzaXplQ2xhc3Nlc1tzaXplXX0gYm9yZGVyLTIgYm9yZGVyLWN1cnJlbnQgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiAke2NsYXNzTmFtZX1gfSAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiTG9hZGluZ1NwaW5uZXIiLCJzaXplIiwiY2xhc3NOYW1lIiwic2l6ZUNsYXNzZXMiLCJzbSIsIm1kIiwibGciLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @llamaindex/chat-ui */ \"(ssr)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ai/react */ \"(ssr)/./node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var _components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/CustomChatMessage */ \"(ssr)/./app/components/CustomChatMessage.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ErrorBoundary */ \"(ssr)/./app/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatPage() {\n    const handler = (0,ai_react__WEBPACK_IMPORTED_MODULE_3__.useChat)({\n        api: \"/api/chat\",\n        onError: {\n            \"ChatPage.useChat[handler]\": (error)=>{\n                console.error(\"Chat error:\", error);\n            }\n        }[\"ChatPage.useChat[handler]\"],\n        // 自定义请求体格式以匹配FastAPI后端\n        body: {\n            id: `chat-${Date.now()}`\n        },\n        initialMessages: [\n            {\n                id: \"1\",\n                role: \"assistant\",\n                content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n            }\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__.ChatSection, {\n                handler: handler,\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__.ChatMessages, {\n                        messageRenderer: (message, isLoading)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_1__.CustomChatMessage, {\n                                message: message,\n                                isLoading: isLoading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {}, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bf3811a20115\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiZjM4MTFhMjAxMTVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _llamaindex_chat_ui_styles_markdown_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @llamaindex/chat-ui/styles/markdown.css */ \"(rsc)/./node_modules/@llamaindex/chat-ui/dist/styles/markdown.css\");\n/* harmony import */ var _llamaindex_chat_ui_styles_pdf_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @llamaindex/chat-ui/styles/pdf.css */ \"(rsc)/./node_modules/@llamaindex/chat-ui/dist/styles/pdf.css\");\n\n\n\n\n\nconst metadata = {\n    title: 'PyLlamaIndex Chat',\n    description: 'AI-powered chat interface with document citations'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b border-border bg-card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-foreground\",\n                                children: \"PyLlamaIndex Chat\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto h-[calc(100vh-73px)]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\create-lllama\\pyllamaindex\\components\\app\\page.tsx",
"default",
));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@llamaindex","vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/highlight.js","vendor-chunks/@mdxeditor","vendor-chunks/react-markdown","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/zod-to-json-schema","vendor-chunks/@radix-ui","vendor-chunks/@lexical","vendor-chunks/lucide-react","vendor-chunks/micromark-core-commonmark","vendor-chunks/hastscript","vendor-chunks/micromark-extension-gfm","vendor-chunks/hast-util-from-parse5","vendor-chunks/property-information","vendor-chunks/prismjs","vendor-chunks/parse5","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/@codemirror","vendor-chunks/zod","vendor-chunks/mdast-util-gfm","vendor-chunks/micromark","vendor-chunks/micromark-extension-math","vendor-chunks/stringify-entities","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/@uiw","vendor-chunks/@lezer","vendor-chunks/@babel","vendor-chunks/react-remove-scroll","vendor-chunks/prop-types","vendor-chunks/swr","vendor-chunks/remark","vendor-chunks/mdast-util-math","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/mdast-util-definitions","vendor-chunks/entities","vendor-chunks/@floating-ui","vendor-chunks/vfile","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-mdx-jsx","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/hast-util-from-html","vendor-chunks/@ai-sdk","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/debug","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/use-callback-ref","vendor-chunks/uvu","vendor-chunks/unist-util-visit-parents","vendor-chunks/micromark-util-subtokenize","vendor-chunks/estree-util-visit","vendor-chunks/dequal","vendor-chunks/use-sync-external-store","vendor-chunks/use-sidecar","vendor-chunks/style-to-object","vendor-chunks/react-is","vendor-chunks/zwitch","vendor-chunks/web-namespaces","vendor-chunks/w3c-keyname","vendor-chunks/vfile-message","vendor-chunks/vfile-location","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-remove-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-position-from-estree","vendor-chunks/unist-util-is","vendor-chunks/unist-util-generated","vendor-chunks/unist-util-find-after","vendor-chunks/unified","vendor-chunks/tslib","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/tailwind-merge","vendor-chunks/style-mod","vendor-chunks/space-separated-tokens","vendor-chunks/remark-stringify","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-math","vendor-chunks/remark-gfm","vendor-chunks/rehype-katex","vendor-chunks/react-hook-form","vendor-chunks/parse-entities","vendor-chunks/nanoid","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-events-to-acorn","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-mdx-expression","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-mdx-md","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-mdx-jsx","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/lexical","vendor-chunks/kleur","vendor-chunks/katex","vendor-chunks/is-plain-obj","vendor-chunks/is-hexadecimal","vendor-chunks/is-decimal","vendor-chunks/is-alphanumerical","vendor-chunks/is-alphabetical","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-text","vendor-chunks/hast-util-parse-selector","vendor-chunks/hast-util-is-element","vendor-chunks/hast-util-from-html-isomorphic","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/diff","vendor-chunks/devlop","vendor-chunks/decode-named-character-reference","vendor-chunks/crelt","vendor-chunks/compute-scroll-into-view","vendor-chunks/comma-separated-tokens","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/character-reference-invalid","vendor-chunks/character-entities","vendor-chunks/character-entities-legacy","vendor-chunks/character-entities-html4","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/ai","vendor-chunks/@marijn","vendor-chunks/classnames","vendor-chunks/throttleit","vendor-chunks/supports-color","vendor-chunks/secure-json-parse","vendor-chunks/object-assign","vendor-chunks/ms","vendor-chunks/is-buffer","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/get-nonce","vendor-chunks/extend","vendor-chunks/downshift","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccreate-lllama%5Cpyllamaindex%5Ccomponents&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();