"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-factory-mdx-expression";
exports.ids = ["vendor-chunks/micromark-factory-mdx-expression"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-factory-mdx-expression/dev/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-factory-mdx-expression/dev/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   factoryMdxExpression: () => (/* binding */ factoryMdxExpression)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_events_to_acorn__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-events-to-acorn */ \"(ssr)/./node_modules/micromark-util-events-to-acorn/dev/lib/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var unist_util_position_from_estree__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-position-from-estree */ \"(ssr)/./node_modules/unist-util-position-from-estree/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {Program} from 'estree'\n * @import {Acorn, AcornOptions} from 'micromark-util-events-to-acorn'\n * @import {Effects, Point, State, TokenType, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * @typedef MdxSignalOk\n *   Good result.\n * @property {'ok'} type\n *   Type.\n * @property {Program | undefined} estree\n *   Value.\n *\n * @typedef MdxSignalNok\n *   Bad result.\n * @property {'nok'} type\n *   Type.\n * @property {VFileMessage} message\n *   Value.\n *\n * @typedef {MdxSignalNok | MdxSignalOk} MdxSignal\n */\n\n\n\n\n\n\n\n\n\n// Tab-size to eat has to be the same as what we serialize as.\n// While in some places in markdown that’s 4, in JS it’s more common as 2.\n// Which is what’s also in `mdast-util-mdx-jsx`:\n// <https://github.com/syntax-tree/mdast-util-mdx-jsx/blob/40b951b/lib/index.js#L52>\nconst indentSize = 2\n\nconst trouble =\n  'https://github.com/micromark/micromark-extension-mdx-expression/tree/main/packages/micromark-extension-mdx-expression'\n\nconst unexpectedEndOfFileHash =\n  '#unexpected-end-of-file-in-expression-expected-a-corresponding-closing-brace-for-'\nconst unexpectedLazyHash =\n  '#unexpected-lazy-line-in-expression-in-container-expected-line-to-be-prefixed'\nconst nonSpreadHash =\n  '#unexpected-type-in-code-expected-an-object-spread-spread'\nconst spreadExtraHash =\n  '#unexpected-extra-content-in-spread-only-a-single-spread-is-supported'\nconst acornHash = '#could-not-parse-expression-with-acorn'\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful\n * @param {TokenType} type\n *   Token type for whole (`{}`).\n * @param {TokenType} markerType\n *   Token type for the markers (`{`, `}`).\n * @param {TokenType} chunkType\n *   Token type for the value (`1`).\n * @param {Acorn | null | undefined} [acorn]\n *   Object with `acorn.parse` and `acorn.parseExpressionAt`.\n * @param {AcornOptions | null | undefined} [acornOptions]\n *   Configuration for acorn.\n * @param {boolean | null | undefined} [addResult=false]\n *   Add `estree` to token (default: `false`).\n * @param {boolean | null | undefined} [spread=false]\n *   Support a spread (`{...a}`) only (default: `false`).\n * @param {boolean | null | undefined} [allowEmpty=false]\n *   Support an empty expression (default: `false`).\n * @param {boolean | null | undefined} [allowLazy=false]\n *   Support lazy continuation of an expression (default: `false`).\n * @returns {State}\n */\n// eslint-disable-next-line max-params\nfunction factoryMdxExpression(\n  effects,\n  ok,\n  type,\n  markerType,\n  chunkType,\n  acorn,\n  acornOptions,\n  addResult,\n  spread,\n  allowEmpty,\n  allowLazy\n) {\n  const self = this\n  const eventStart = this.events.length + 3 // Add main and marker token\n  let size = 0\n  /** @type {Point} */\n  let pointStart\n  /** @type {Error} */\n  let lastCrash\n\n  return start\n\n  /**\n   * Start of an MDX expression.\n   *\n   * ```markdown\n   * > | a {Math.PI} c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace, 'expected `{`')\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    pointStart = self.now()\n    return before\n  }\n\n  /**\n   * Before data.\n   *\n   * ```markdown\n   * > | a {Math.PI} c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      if (lastCrash) throw lastCrash\n\n      const error = new vfile_message__WEBPACK_IMPORTED_MODULE_2__.VFileMessage(\n        'Unexpected end of file in expression, expected a corresponding closing brace for `{`',\n        {\n          place: self.now(),\n          ruleId: 'unexpected-eof',\n          source: 'micromark-extension-mdx-expression'\n        }\n      )\n      error.url = trouble + unexpectedEndOfFileHash\n      throw error\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)\n      return eolAfter\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightCurlyBrace && size === 0) {\n      /** @type {MdxSignal} */\n      const next = acorn\n        ? mdxExpressionParse.call(\n            self,\n            acorn,\n            acornOptions,\n            chunkType,\n            eventStart,\n            pointStart,\n            allowEmpty || false,\n            spread || false\n          )\n        : {type: 'ok', estree: undefined}\n\n      if (next.type === 'ok') {\n        effects.enter(markerType)\n        effects.consume(code)\n        effects.exit(markerType)\n        const token = effects.exit(type)\n\n        if (addResult && next.estree) {\n          Object.assign(token, {estree: next.estree})\n        }\n\n        return ok\n      }\n\n      lastCrash = next.message\n      effects.enter(chunkType)\n      effects.consume(code)\n      return inside\n    }\n\n    effects.enter(chunkType)\n    return inside(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | a {Math.PI} c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightCurlyBrace && size === 0) ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)\n    ) {\n      effects.exit(chunkType)\n      return before(code)\n    }\n\n    // Don’t count if gnostic.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.leftCurlyBrace && !acorn) {\n      size += 1\n    } else if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.rightCurlyBrace) {\n      size -= 1\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /**\n   * After eol.\n   *\n   * ```markdown\n   *   | a {b +\n   * > | c} d\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function eolAfter(code) {\n    const now = self.now()\n\n    // Lazy continuation in a flow expression (or flow tag) is a syntax error.\n    if (\n      now.line !== pointStart.line &&\n      !allowLazy &&\n      self.parser.lazy[now.line]\n    ) {\n      const error = new vfile_message__WEBPACK_IMPORTED_MODULE_2__.VFileMessage(\n        'Unexpected lazy line in expression in container, expected line to be prefixed with `>` when in a block quote, whitespace when in a list, etc',\n        {\n          place: self.now(),\n          ruleId: 'unexpected-lazy',\n          source: 'micromark-extension-mdx-expression'\n        }\n      )\n      error.url = trouble + unexpectedLazyHash\n      throw error\n    }\n\n    // Note: `markdown-rs` uses `4`, but we use `2`.\n    //\n    // Idea: investigate if we’d need to use more complex stripping.\n    // Take this example:\n    //\n    // ```markdown\n    // >  aaa <b c={`\n    // >      d\n    // >  `} /> eee\n    // ```\n    //\n    // Currently, the “paragraph” starts at `> | aaa`, so for the next line\n    // here we split it into `>␠|␠␠|␠␠␠d` (prefix, this indent here,\n    // expression data).\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n        effects,\n        before,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix,\n        indentSize + 1\n      )(code)\n    }\n\n    return before(code)\n  }\n}\n\n/**\n * Mix of `markdown-rs`’s `parse_expression` and `MdxExpressionParse`\n * functionality, to wrap our `eventsToAcorn`.\n *\n * In the future, the plan is to realise the rust way, which allows arbitrary\n * parsers.\n *\n * @this {TokenizeContext}\n * @param {Acorn} acorn\n * @param {AcornOptions | null | undefined} acornOptions\n * @param {TokenType} chunkType\n * @param {number} eventStart\n * @param {Point} pointStart\n * @param {boolean} allowEmpty\n * @param {boolean} spread\n * @returns {MdxSignal}\n */\n// eslint-disable-next-line max-params\nfunction mdxExpressionParse(\n  acorn,\n  acornOptions,\n  chunkType,\n  eventStart,\n  pointStart,\n  allowEmpty,\n  spread\n) {\n  // Gnostic mode: parse w/ acorn.\n  const result = (0,micromark_util_events_to_acorn__WEBPACK_IMPORTED_MODULE_6__.eventsToAcorn)(this.events.slice(eventStart), {\n    acorn,\n    tokenTypes: [chunkType],\n    acornOptions,\n    start: pointStart,\n    expression: true,\n    allowEmpty,\n    prefix: spread ? '({' : '',\n    suffix: spread ? '})' : ''\n  })\n  const estree = result.estree\n\n  // Get the spread value.\n  if (spread && estree) {\n    // Should always be the case as we wrap in `d={}`\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(estree.type === 'Program', 'expected program')\n    const head = estree.body[0]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(head, 'expected body')\n\n    if (\n      head.type !== 'ExpressionStatement' ||\n      head.expression.type !== 'ObjectExpression'\n    ) {\n      const place = (0,unist_util_position_from_estree__WEBPACK_IMPORTED_MODULE_7__.positionFromEstree)(head)\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(place, 'expected position')\n      const error = new vfile_message__WEBPACK_IMPORTED_MODULE_2__.VFileMessage(\n        'Unexpected `' +\n          head.type +\n          '` in code: expected an object spread (`{...spread}`)',\n        {\n          place: place.start,\n          ruleId: 'non-spread',\n          source: 'micromark-extension-mdx-expression'\n        }\n      )\n      error.url = trouble + nonSpreadHash\n      throw error\n    }\n\n    if (head.expression.properties[1]) {\n      const place = (0,unist_util_position_from_estree__WEBPACK_IMPORTED_MODULE_7__.positionFromEstree)(head.expression.properties[1])\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(place, 'expected position')\n      const error = new vfile_message__WEBPACK_IMPORTED_MODULE_2__.VFileMessage(\n        'Unexpected extra content in spread: only a single spread is supported',\n        {\n          place: place.start,\n          ruleId: 'spread-extra',\n          source: 'micromark-extension-mdx-expression'\n        }\n      )\n      error.url = trouble + spreadExtraHash\n      throw error\n    }\n\n    if (\n      head.expression.properties[0] &&\n      head.expression.properties[0].type !== 'SpreadElement'\n    ) {\n      const place = (0,unist_util_position_from_estree__WEBPACK_IMPORTED_MODULE_7__.positionFromEstree)(head.expression.properties[0])\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(place, 'expected position')\n      const error = new vfile_message__WEBPACK_IMPORTED_MODULE_2__.VFileMessage(\n        'Unexpected `' +\n          head.expression.properties[0].type +\n          '` in code: only spread elements are supported',\n        {\n          place: place.start,\n          ruleId: 'non-spread',\n          source: 'micromark-extension-mdx-expression'\n        }\n      )\n      error.url = trouble + nonSpreadHash\n      throw error\n    }\n  }\n\n  if (result.error) {\n    const error = new vfile_message__WEBPACK_IMPORTED_MODULE_2__.VFileMessage('Could not parse expression with acorn', {\n      cause: result.error,\n      place: {\n        line: result.error.loc.line,\n        column: result.error.loc.column + 1,\n        offset: result.error.pos\n      },\n      ruleId: 'acorn',\n      source: 'micromark-extension-mdx-expression'\n    })\n    error.url = trouble + acornHash\n\n    return {type: 'nok', message: error}\n  }\n\n  return {type: 'ok', estree}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-factory-mdx-expression/dev/index.js\n");

/***/ })

};
;