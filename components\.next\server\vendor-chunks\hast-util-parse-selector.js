"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-parse-selector";
exports.ids = ["vendor-chunks/hast-util-parse-selector"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-parse-selector/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-parse-selector/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseSelector: () => (/* binding */ parseSelector)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n */\n\n/**\n * @template {string} SimpleSelector\n *   Selector type.\n * @template {string} DefaultTagName\n *   Default tag name.\n * @typedef {(\n *   SimpleSelector extends ''\n *     ? DefaultTagName\n *     : SimpleSelector extends `${infer TagName}.${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends `${infer TagName}#${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends string\n *     ? SimpleSelector\n *     : DefaultTagName\n * )} ExtractTagName\n *   Extract tag name from a simple selector.\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name (default: `'div'`).\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector (optional).\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nfunction parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: tag name is parsed.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\n");

/***/ })

};
;