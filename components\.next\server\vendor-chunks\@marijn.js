"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@marijn";
exports.ids = ["vendor-chunks/@marijn"];
exports.modules = {

/***/ "(ssr)/./node_modules/@marijn/find-cluster-break/src/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@marijn/find-cluster-break/src/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findClusterBreak: () => (/* binding */ findClusterBreak),\n/* harmony export */   isExtendingChar: () => (/* binding */ isExtendingChar)\n/* harmony export */ });\n// These are filled with ranges (rangeFrom[i] up to but not including\n// rangeTo[i]) of code points that count as extending characters.\nlet rangeFrom = [], rangeTo = []\n\n;(() => {\n  // Compressed representation of the Grapheme_Cluster_Break=Extend\n  // information from\n  // http://www.unicode.org/Public/16.0.0/ucd/auxiliary/GraphemeBreakProperty.txt.\n  // Each pair of elements represents a range, as an offet from the\n  // previous range and a length. Numbers are in base-36, with the empty\n  // string being a shorthand for 1.\n  let numbers = \"lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o\".split(\",\").map(s => s ? parseInt(s, 36) : 1)\n  for (let i = 0, n = 0; i < numbers.length; i++)\n    (i % 2 ? rangeTo : rangeFrom).push(n = n + numbers[i])\n})()\n\nfunction isExtendingChar(code) {\n  if (code < 768) return false\n  for (let from = 0, to = rangeFrom.length;;) {\n    let mid = (from + to) >> 1\n    if (code < rangeFrom[mid]) to = mid\n    else if (code >= rangeTo[mid]) from = mid + 1\n    else return true\n    if (from == to) return false\n  }\n}\n\nfunction isRegionalIndicator(code) {\n  return code >= 0x1F1E6 && code <= 0x1F1FF\n}\n\nfunction check(code) {\n  for (let i = 0; i < rangeFrom.length; i++) {\n    if (rangeTo[i] > code) return rangeFrom[i] <= code\n  }\n  return false\n}\n\nconst ZWJ = 0x200d\n\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return (forward ? nextClusterBreak : prevClusterBreak)(str, pos, includeExtending)\n}\n\nfunction nextClusterBreak(str, pos, includeExtending) {\n  if (pos == str.length) return pos\n  // If pos is in the middle of a surrogate pair, move to its start\n  if (pos && surrogateLow(str.charCodeAt(pos)) && surrogateHigh(str.charCodeAt(pos - 1))) pos--\n  let prev = codePointAt(str, pos)\n  pos += codePointSize(prev)\n  while (pos < str.length) {\n    let next = codePointAt(str, pos)\n    if (prev == ZWJ || next == ZWJ || includeExtending && isExtendingChar(next)) {\n      pos += codePointSize(next)\n      prev = next\n    } else if (isRegionalIndicator(next)) {\n      let countBefore = 0, i = pos - 2\n      while (i >= 0 && isRegionalIndicator(codePointAt(str, i))) { countBefore++; i -= 2 }\n      if (countBefore % 2 == 0) break\n      else pos += 2\n    } else {\n      break\n    }\n  }\n  return pos\n}\n\nfunction prevClusterBreak(str, pos, includeExtending) {\n  while (pos > 0) {\n    let found = nextClusterBreak(str, pos - 2, includeExtending)\n    if (found < pos) return found\n    pos--\n  }\n  return 0\n}\n\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos)\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0\n  let code1 = str.charCodeAt(pos + 1)\n  if (!surrogateLow(code1)) return code0\n  return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000\n}\n\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000 }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00 }\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2 }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@marijn/find-cluster-break/src/index.js\n");

/***/ })

};
;