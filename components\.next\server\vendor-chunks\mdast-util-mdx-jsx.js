"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-mdx-jsx";
exports.ids = ["vendor-chunks/mdast-util-mdx-jsx"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-mdx-jsx/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-mdx-jsx/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mdxJsxFromMarkdown: () => (/* binding */ mdxJsxFromMarkdown),\n/* harmony export */   mdxJsxToMarkdown: () => (/* binding */ mdxJsxToMarkdown)\n/* harmony export */ });\n/* harmony import */ var ccount__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ccount */ \"(ssr)/./node_modules/ccount/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var parse_entities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! parse-entities */ \"(ssr)/./node_modules/parse-entities/lib/index.js\");\n/* harmony import */ var stringify_entities__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! stringify-entities */ \"(ssr)/./node_modules/stringify-entities/lib/index.js\");\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {CompileContext, Extension as FromMarkdownExtension, Handle as FromMarkdownHandle, OnEnterError, OnExitError, Token} from 'mdast-util-from-markdown'\n * @import {Handle as ToMarkdownHandle, Options as ToMarkdownExtension, State, Tracker} from 'mdast-util-to-markdown'\n * @import {Point} from 'unist'\n * @import {MdxJsxAttribute, MdxJsxAttributeValueExpression, MdxJsxExpressionAttribute, MdxJsxFlowElement, MdxJsxTextElement} from '../index.js'\n */\n\n/**\n * @typedef Tag\n *   Single tag.\n * @property {string | undefined} name\n *   Name of tag, or `undefined` for fragment.\n *\n *   > 👉 **Note**: `null` is used in the AST for fragments, as it serializes in\n *   > JSON.\n * @property {Array<MdxJsxAttribute | MdxJsxExpressionAttribute>} attributes\n *   Attributes.\n * @property {boolean} close\n *   Whether the tag is closing (`</x>`).\n * @property {boolean} selfClosing\n *   Whether the tag is self-closing (`<x/>`).\n * @property {Token['start']} start\n *   Start point.\n * @property {Token['start']} end\n *   End point.\n *\n * @typedef ToMarkdownOptions\n *   Configuration.\n * @property {'\"' | \"'\" | null | undefined} [quote='\"']\n *   Preferred quote to use around attribute values (default: `'\"'`).\n * @property {boolean | null | undefined} [quoteSmart=false]\n *   Use the other quote if that results in less bytes (default: `false`).\n * @property {boolean | null | undefined} [tightSelfClosing=false]\n *   Do not use an extra space when closing self-closing elements: `<img/>`\n *   instead of `<img />` (default: `false`).\n * @property {number | null | undefined} [printWidth=Infinity]\n *   Try and wrap syntax at this width (default: `Infinity`).\n *\n *   When set to a finite number (say, `80`), the formatter will print\n *   attributes on separate lines when a tag doesn’t fit on one line.\n *   The normal behavior is to print attributes with spaces between them\n *   instead of line endings.\n */\n\n\n\n\n\n\n\n\nconst indent = '  '\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable MDX JSX.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable MDX JSX.\n *\n *   When using the syntax extension with `addResult`, nodes will have a\n *   `data.estree` field set to an ESTree `Program` node.\n */\nfunction mdxJsxFromMarkdown() {\n  return {\n    canContainEols: ['mdxJsxTextElement'],\n    enter: {\n      mdxJsxFlowTag: enterMdxJsxTag,\n      mdxJsxFlowTagClosingMarker: enterMdxJsxTagClosingMarker,\n      mdxJsxFlowTagAttribute: enterMdxJsxTagAttribute,\n      mdxJsxFlowTagExpressionAttribute: enterMdxJsxTagExpressionAttribute,\n      mdxJsxFlowTagAttributeValueLiteral: buffer,\n      mdxJsxFlowTagAttributeValueExpression: buffer,\n      mdxJsxFlowTagSelfClosingMarker: enterMdxJsxTagSelfClosingMarker,\n\n      mdxJsxTextTag: enterMdxJsxTag,\n      mdxJsxTextTagClosingMarker: enterMdxJsxTagClosingMarker,\n      mdxJsxTextTagAttribute: enterMdxJsxTagAttribute,\n      mdxJsxTextTagExpressionAttribute: enterMdxJsxTagExpressionAttribute,\n      mdxJsxTextTagAttributeValueLiteral: buffer,\n      mdxJsxTextTagAttributeValueExpression: buffer,\n      mdxJsxTextTagSelfClosingMarker: enterMdxJsxTagSelfClosingMarker\n    },\n    exit: {\n      mdxJsxFlowTagClosingMarker: exitMdxJsxTagClosingMarker,\n      mdxJsxFlowTagNamePrimary: exitMdxJsxTagNamePrimary,\n      mdxJsxFlowTagNameMember: exitMdxJsxTagNameMember,\n      mdxJsxFlowTagNameLocal: exitMdxJsxTagNameLocal,\n      mdxJsxFlowTagExpressionAttribute: exitMdxJsxTagExpressionAttribute,\n      mdxJsxFlowTagExpressionAttributeValue: data,\n      mdxJsxFlowTagAttributeNamePrimary: exitMdxJsxTagAttributeNamePrimary,\n      mdxJsxFlowTagAttributeNameLocal: exitMdxJsxTagAttributeNameLocal,\n      mdxJsxFlowTagAttributeValueLiteral: exitMdxJsxTagAttributeValueLiteral,\n      mdxJsxFlowTagAttributeValueLiteralValue: data,\n      mdxJsxFlowTagAttributeValueExpression:\n        exitMdxJsxTagAttributeValueExpression,\n      mdxJsxFlowTagAttributeValueExpressionValue: data,\n      mdxJsxFlowTagSelfClosingMarker: exitMdxJsxTagSelfClosingMarker,\n      mdxJsxFlowTag: exitMdxJsxTag,\n\n      mdxJsxTextTagClosingMarker: exitMdxJsxTagClosingMarker,\n      mdxJsxTextTagNamePrimary: exitMdxJsxTagNamePrimary,\n      mdxJsxTextTagNameMember: exitMdxJsxTagNameMember,\n      mdxJsxTextTagNameLocal: exitMdxJsxTagNameLocal,\n      mdxJsxTextTagExpressionAttribute: exitMdxJsxTagExpressionAttribute,\n      mdxJsxTextTagExpressionAttributeValue: data,\n      mdxJsxTextTagAttributeNamePrimary: exitMdxJsxTagAttributeNamePrimary,\n      mdxJsxTextTagAttributeNameLocal: exitMdxJsxTagAttributeNameLocal,\n      mdxJsxTextTagAttributeValueLiteral: exitMdxJsxTagAttributeValueLiteral,\n      mdxJsxTextTagAttributeValueLiteralValue: data,\n      mdxJsxTextTagAttributeValueExpression:\n        exitMdxJsxTagAttributeValueExpression,\n      mdxJsxTextTagAttributeValueExpressionValue: data,\n      mdxJsxTextTagSelfClosingMarker: exitMdxJsxTagSelfClosingMarker,\n      mdxJsxTextTag: exitMdxJsxTag\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function buffer() {\n    this.buffer()\n  }\n\n  /**\n   * Copy a point-like value.\n   *\n   * @param {Point} d\n   *   Point-like value.\n   * @returns {Point}\n   *   unist point.\n   */\n  function point(d) {\n    return {line: d.line, column: d.column, offset: d.offset}\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function data(token) {\n    this.config.enter.data.call(this, token)\n    this.config.exit.data.call(this, token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMdxJsxTag(token) {\n    /** @type {Tag} */\n    const tag = {\n      name: undefined,\n      attributes: [],\n      close: false,\n      selfClosing: false,\n      start: token.start,\n      end: token.end\n    }\n    if (!this.data.mdxJsxTagStack) this.data.mdxJsxTagStack = []\n    this.data.mdxJsxTag = tag\n    this.buffer()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMdxJsxTagClosingMarker(token) {\n    const stack = this.data.mdxJsxTagStack\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(stack, 'expected `mdxJsxTagStack`')\n\n    if (stack.length === 0) {\n      throw new vfile_message__WEBPACK_IMPORTED_MODULE_1__.VFileMessage(\n        'Unexpected closing slash `/` in tag, expected an open tag first',\n        {start: token.start, end: token.end},\n        'mdast-util-mdx-jsx:unexpected-closing-slash'\n      )\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMdxJsxTagAnyAttribute(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n\n    if (tag.close) {\n      throw new vfile_message__WEBPACK_IMPORTED_MODULE_1__.VFileMessage(\n        'Unexpected attribute in closing tag, expected the end of the tag',\n        {start: token.start, end: token.end},\n        'mdast-util-mdx-jsx:unexpected-attribute'\n      )\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMdxJsxTagSelfClosingMarker(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n\n    if (tag.close) {\n      throw new vfile_message__WEBPACK_IMPORTED_MODULE_1__.VFileMessage(\n        'Unexpected self-closing slash `/` in closing tag, expected the end of the tag',\n        {start: token.start, end: token.end},\n        'mdast-util-mdx-jsx:unexpected-self-closing-slash'\n      )\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagClosingMarker() {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    tag.close = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagNamePrimary(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    tag.name = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagNameMember(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    tag.name += '.' + this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagNameLocal(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    tag.name += ':' + this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMdxJsxTagAttribute(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    enterMdxJsxTagAnyAttribute.call(this, token)\n    tag.attributes.push({\n      type: 'mdxJsxAttribute',\n      name: '',\n      value: null,\n      position: {\n        start: point(token.start),\n        // @ts-expect-error: `end` will be patched later.\n        end: undefined\n      }\n    })\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMdxJsxTagExpressionAttribute(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    enterMdxJsxTagAnyAttribute.call(this, token)\n    tag.attributes.push({\n      type: 'mdxJsxExpressionAttribute',\n      value: '',\n      position: {\n        start: point(token.start),\n        // @ts-expect-error: `end` will be patched later.\n        end: undefined\n      }\n    })\n    this.buffer()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagExpressionAttribute(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const tail = tag.attributes[tag.attributes.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.type === 'mdxJsxExpressionAttribute')\n    const estree = token.estree\n\n    tail.value = this.resume()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.position !== undefined)\n    tail.position.end = point(token.end)\n\n    if (estree) {\n      tail.data = {estree}\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagAttributeNamePrimary(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const node = tag.attributes[tag.attributes.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'mdxJsxAttribute')\n    node.name = this.sliceSerialize(token)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.position !== undefined)\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagAttributeNameLocal(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const node = tag.attributes[tag.attributes.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'mdxJsxAttribute')\n    node.name += ':' + this.sliceSerialize(token)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.position !== undefined)\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagAttributeValueLiteral(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const node = tag.attributes[tag.attributes.length - 1]\n    node.value = (0,parse_entities__WEBPACK_IMPORTED_MODULE_2__.parseEntities)(this.resume(), {nonTerminated: false})\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.position !== undefined)\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagAttributeValueExpression(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const tail = tag.attributes[tag.attributes.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.type === 'mdxJsxAttribute')\n    /** @type {MdxJsxAttributeValueExpression} */\n    const node = {type: 'mdxJsxAttributeValueExpression', value: this.resume()}\n    const estree = token.estree\n\n    if (estree) {\n      node.data = {estree}\n    }\n\n    tail.value = node\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.position !== undefined)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTagSelfClosingMarker() {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n\n    tag.selfClosing = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMdxJsxTag(token) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const stack = this.data.mdxJsxTagStack\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(stack, 'expected `mdxJsxTagStack`')\n    const tail = stack[stack.length - 1]\n\n    if (tag.close && tail.name !== tag.name) {\n      throw new vfile_message__WEBPACK_IMPORTED_MODULE_1__.VFileMessage(\n        'Unexpected closing tag `' +\n          serializeAbbreviatedTag(tag) +\n          '`, expected corresponding closing tag for `' +\n          serializeAbbreviatedTag(tail) +\n          '` (' +\n          (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_3__.stringifyPosition)(tail) +\n          ')',\n        {start: token.start, end: token.end},\n        'mdast-util-mdx-jsx:end-tag-mismatch'\n      )\n    }\n\n    // End of a tag, so drop the buffer.\n    this.resume()\n\n    if (tag.close) {\n      stack.pop()\n    } else {\n      this.enter(\n        {\n          type:\n            token.type === 'mdxJsxTextTag'\n              ? 'mdxJsxTextElement'\n              : 'mdxJsxFlowElement',\n          name: tag.name || null,\n          attributes: tag.attributes,\n          children: []\n        },\n        token,\n        onErrorRightIsTag\n      )\n    }\n\n    if (tag.selfClosing || tag.close) {\n      this.exit(token, onErrorLeftIsTag)\n    } else {\n      stack.push(tag)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {OnEnterError}\n   */\n  function onErrorRightIsTag(closing, open) {\n    const stack = this.data.mdxJsxTagStack\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(stack, 'expected `mdxJsxTagStack`')\n    const tag = stack[stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n    const place = closing ? ' before the end of `' + closing.type + '`' : ''\n    const position = closing\n      ? {start: closing.start, end: closing.end}\n      : undefined\n\n    throw new vfile_message__WEBPACK_IMPORTED_MODULE_1__.VFileMessage(\n      'Expected a closing tag for `' +\n        serializeAbbreviatedTag(tag) +\n        '` (' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_3__.stringifyPosition)({start: open.start, end: open.end}) +\n        ')' +\n        place,\n      position,\n      'mdast-util-mdx-jsx:end-tag-mismatch'\n    )\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {OnExitError}\n   */\n  function onErrorLeftIsTag(a, b) {\n    const tag = this.data.mdxJsxTag\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tag, 'expected `mdxJsxTag`')\n\n    throw new vfile_message__WEBPACK_IMPORTED_MODULE_1__.VFileMessage(\n      'Expected the closing tag `' +\n        serializeAbbreviatedTag(tag) +\n        '` either after the end of `' +\n        b.type +\n        '` (' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_3__.stringifyPosition)(b.end) +\n        ') or another opening tag after the start of `' +\n        b.type +\n        '` (' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_3__.stringifyPosition)(b.start) +\n        ')',\n      {start: a.start, end: a.end},\n      'mdast-util-mdx-jsx:end-tag-mismatch'\n    )\n  }\n\n  /**\n   * Serialize a tag, excluding attributes.\n   * `self-closing` is not supported, because we don’t need it yet.\n   *\n   * @param {Tag} tag\n   * @returns {string}\n   */\n  function serializeAbbreviatedTag(tag) {\n    return '<' + (tag.close ? '/' : '') + (tag.name || '') + '>'\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable MDX JSX.\n *\n * This extension configures `mdast-util-to-markdown` with\n * `options.fences: true` and `options.resourceLink: true` too, do not\n * overwrite them!\n *\n * @param {ToMarkdownOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable MDX JSX.\n */\nfunction mdxJsxToMarkdown(options) {\n  const options_ = options || {}\n  const quote = options_.quote || '\"'\n  const quoteSmart = options_.quoteSmart || false\n  const tightSelfClosing = options_.tightSelfClosing || false\n  const printWidth = options_.printWidth || Number.POSITIVE_INFINITY\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error(\n      'Cannot serialize attribute values with `' +\n        quote +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  mdxElement.peek = peekElement\n\n  return {\n    handlers: {\n      mdxJsxFlowElement: mdxElement,\n      mdxJsxTextElement: mdxElement\n    },\n    unsafe: [\n      {character: '<', inConstruct: ['phrasing']},\n      {atBreak: true, character: '<'}\n    ],\n    // Always generate fenced code (never indented code).\n    fences: true,\n    // Always generate links with resources (never autolinks).\n    resourceLink: true\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {MdxJsxFlowElement | MdxJsxTextElement} node\n   */\n  // eslint-disable-next-line complexity\n  function mdxElement(node, _, state, info) {\n    const flow = node.type === 'mdxJsxFlowElement'\n    const selfClosing = node.name\n      ? !node.children || node.children.length === 0\n      : false\n    const depth = inferDepth(state)\n    const currentIndent = createIndent(depth)\n    const trackerOneLine = state.createTracker(info)\n    const trackerMultiLine = state.createTracker(info)\n    /** @type {Array<string>} */\n    const serializedAttributes = []\n    const prefix = (flow ? currentIndent : '') + '<' + (node.name || '')\n    const exit = state.enter(node.type)\n\n    trackerOneLine.move(prefix)\n    trackerMultiLine.move(prefix)\n\n    // None.\n    if (node.attributes && node.attributes.length > 0) {\n      if (!node.name) {\n        throw new Error('Cannot serialize fragment w/ attributes')\n      }\n\n      let index = -1\n      while (++index < node.attributes.length) {\n        const attribute = node.attributes[index]\n        /** @type {string} */\n        let result\n\n        if (attribute.type === 'mdxJsxExpressionAttribute') {\n          result = '{' + (attribute.value || '') + '}'\n        } else {\n          if (!attribute.name) {\n            throw new Error('Cannot serialize attribute w/o name')\n          }\n\n          const value = attribute.value\n          const left = attribute.name\n          /** @type {string} */\n          let right = ''\n\n          if (value === null || value === undefined) {\n            // Empty.\n          } else if (typeof value === 'object') {\n            right = '{' + (value.value || '') + '}'\n          } else {\n            // If the alternative is less common than `quote`, switch.\n            const appliedQuote =\n              quoteSmart && (0,ccount__WEBPACK_IMPORTED_MODULE_4__.ccount)(value, quote) > (0,ccount__WEBPACK_IMPORTED_MODULE_4__.ccount)(value, alternative)\n                ? alternative\n                : quote\n            right =\n              appliedQuote +\n              (0,stringify_entities__WEBPACK_IMPORTED_MODULE_5__.stringifyEntitiesLight)(value, {subset: [appliedQuote]}) +\n              appliedQuote\n          }\n\n          result = left + (right ? '=' : '') + right\n        }\n\n        serializedAttributes.push(result)\n      }\n    }\n\n    let attributesOnTheirOwnLine = false\n    const attributesOnOneLine = serializedAttributes.join(' ')\n\n    if (\n      // Block:\n      flow &&\n      // Including a line ending (expressions).\n      (/\\r?\\n|\\r/.test(attributesOnOneLine) ||\n        // Current position (including `<tag`).\n        trackerOneLine.current().now.column +\n          // -1 because columns, +1 for ` ` before attributes.\n          // Attributes joined by spaces.\n          attributesOnOneLine.length +\n          // ` />`.\n          (selfClosing ? (tightSelfClosing ? 2 : 3) : 1) >\n          printWidth)\n    ) {\n      attributesOnTheirOwnLine = true\n    }\n\n    let tracker = trackerOneLine\n    let value = prefix\n\n    if (attributesOnTheirOwnLine) {\n      tracker = trackerMultiLine\n\n      let index = -1\n\n      while (++index < serializedAttributes.length) {\n        // Only indent first line of of attributes, we can’t indent attribute\n        // values.\n        serializedAttributes[index] =\n          currentIndent + indent + serializedAttributes[index]\n      }\n\n      value += tracker.move(\n        '\\n' + serializedAttributes.join('\\n') + '\\n' + currentIndent\n      )\n    } else if (attributesOnOneLine) {\n      value += tracker.move(' ' + attributesOnOneLine)\n    }\n\n    if (selfClosing) {\n      value += tracker.move(\n        (tightSelfClosing || attributesOnTheirOwnLine ? '' : ' ') + '/'\n      )\n    }\n\n    value += tracker.move('>')\n\n    if (node.children && node.children.length > 0) {\n      if (node.type === 'mdxJsxTextElement') {\n        value += tracker.move(\n          state.containerPhrasing(node, {\n            ...tracker.current(),\n            before: '>',\n            after: '<'\n          })\n        )\n      } else {\n        tracker.shift(2)\n        value += tracker.move('\\n')\n        value += tracker.move(containerFlow(node, state, tracker.current()))\n        value += tracker.move('\\n')\n      }\n    }\n\n    if (!selfClosing) {\n      value += tracker.move(\n        (flow ? currentIndent : '') + '</' + (node.name || '') + '>'\n      )\n    }\n\n    exit()\n    return value\n  }\n}\n\n// Modified copy of:\n// <https://github.com/syntax-tree/mdast-util-to-markdown/blob/a381cbc/lib/util/container-flow.js>.\n//\n// To do: add `indent` support to `mdast-util-to-markdown`.\n// As indents are only used for JSX, it’s fine for now, but perhaps better\n// there.\n/**\n * @param {MdxJsxFlowElement} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {ReturnType<Tracker['current']>} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined by (blank) lines.\n */\nfunction containerFlow(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children\n  const tracker = state.createTracker(info)\n  const currentIndent = createIndent(inferDepth(state))\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n\n  indexStack.push(-1)\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    indexStack[indexStack.length - 1] = index\n\n    const childInfo = {before: '\\n', after: '\\n', ...tracker.current()}\n\n    const result = state.handle(child, parent, state, childInfo)\n\n    const serializedChild =\n      child.type === 'mdxJsxFlowElement'\n        ? result\n        : state.indentLines(result, function (line, _, blank) {\n            return (blank ? '' : currentIndent) + line\n          })\n\n    results.push(tracker.move(serializedChild))\n\n    if (child.type !== 'list') {\n      state.bulletLastUsed = undefined\n    }\n\n    if (index < children.length - 1) {\n      results.push(tracker.move('\\n\\n'))\n    }\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n\n/**\n * @param {State} state\n * @returns {number}\n */\nfunction inferDepth(state) {\n  let depth = 0\n  let index = state.stack.length\n\n  while (--index > -1) {\n    const name = state.stack[index]\n\n    if (name === 'blockquote' || name === 'listItem') break\n    if (name === 'mdxJsxFlowElement') depth++\n  }\n\n  return depth\n}\n\n/**\n * @param {number} depth\n * @returns {string}\n */\nfunction createIndent(depth) {\n  return indent.repeat(depth)\n}\n\n/**\n * @type {ToMarkdownHandle}\n */\nfunction peekElement() {\n  return '<'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1tZHgtanN4L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxvSEFBb0g7QUFDaEksWUFBWSw0RUFBNEU7QUFDeEYsWUFBWSxPQUFPO0FBQ25CLFlBQVksa0hBQWtIO0FBQzlIOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsb0JBQW9CO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxvREFBb0Q7QUFDbEU7QUFDQSxjQUFjLFNBQVM7QUFDdkI7QUFDQSxjQUFjLFNBQVM7QUFDdkI7QUFDQSxjQUFjLGdCQUFnQjtBQUM5QjtBQUNBLGNBQWMsZ0JBQWdCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw4QkFBOEI7QUFDNUM7QUFDQSxjQUFjLDRCQUE0QjtBQUMxQztBQUNBLGNBQWMsNEJBQTRCO0FBQzFDO0FBQ0E7QUFDQSxjQUFjLDJCQUEyQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFNkI7QUFDTTtBQUNTO0FBQ2E7QUFDTTtBQUNyQjs7QUFFMUM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsT0FBTztBQUNwQjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQSxlQUFlLEtBQUs7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNOztBQUVWO0FBQ0EsZ0JBQWdCLHVEQUFZO0FBQzVCO0FBQ0EsU0FBUyxtQ0FBbUM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07O0FBRVY7QUFDQSxnQkFBZ0IsdURBQVk7QUFDNUI7QUFDQSxTQUFTLG1DQUFtQztBQUM1QztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTs7QUFFVjtBQUNBLGdCQUFnQix1REFBWTtBQUM1QjtBQUNBLFNBQVMsbUNBQW1DO0FBQzVDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07QUFDVjtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07QUFDVjtBQUNBLElBQUksMkNBQU07QUFDVjs7QUFFQTtBQUNBLElBQUksMkNBQU07QUFDVjs7QUFFQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07QUFDVjtBQUNBLGlCQUFpQiw2REFBYSxpQkFBaUIscUJBQXFCO0FBQ3BFLElBQUksMkNBQU07QUFDVjtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQSxJQUFJLDJDQUFNO0FBQ1YsZUFBZSxnQ0FBZ0M7QUFDL0Msa0JBQWtCO0FBQ2xCOztBQUVBO0FBQ0EsbUJBQW1CO0FBQ25COztBQUVBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07O0FBRVY7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWOztBQUVBO0FBQ0EsZ0JBQWdCLHVEQUFZO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGdGQUFpQjtBQUMzQjtBQUNBLFNBQVMsbUNBQW1DO0FBQzVDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQSxJQUFJLDJDQUFNO0FBQ1Y7QUFDQTtBQUNBLFNBQVM7QUFDVDs7QUFFQSxjQUFjLHVEQUFZO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLFFBQVEsZ0ZBQWlCLEVBQUUsaUNBQWlDO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTs7QUFFVixjQUFjLHVEQUFZO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdGQUFpQjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdGQUFpQjtBQUN6QjtBQUNBLE9BQU8sMkJBQTJCO0FBQ2xDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsS0FBSztBQUNsQixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsc0NBQXNDO0FBQ2pEO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLE9BQU8sMENBQTBDO0FBQ2pELE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osYUFBYSx1Q0FBdUM7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZUFBZTtBQUM5QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixRQUFRO0FBQzNCOztBQUVBO0FBQ0EscUJBQXFCLGdDQUFnQztBQUNyRCxVQUFVO0FBQ1Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxxQkFBcUIsUUFBUTtBQUM3Qjs7QUFFQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLHNCQUFzQiw0QkFBNEI7QUFDbEQsWUFBWTtBQUNaO0FBQ0E7QUFDQSw0QkFBNEIsOENBQU0saUJBQWlCLDhDQUFNO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYywwRUFBc0IsU0FBUyx1QkFBdUI7QUFDcEU7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsbUJBQW1CO0FBQzlCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxnQ0FBZ0M7QUFDM0M7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGVBQWU7QUFDNUI7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBLHVCQUF1Qjs7QUFFdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7O0FBRVg7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXG1kYXN0LXV0aWwtbWR4LWpzeFxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0NvbXBpbGVDb250ZXh0LCBFeHRlbnNpb24gYXMgRnJvbU1hcmtkb3duRXh0ZW5zaW9uLCBIYW5kbGUgYXMgRnJvbU1hcmtkb3duSGFuZGxlLCBPbkVudGVyRXJyb3IsIE9uRXhpdEVycm9yLCBUb2tlbn0gZnJvbSAnbWRhc3QtdXRpbC1mcm9tLW1hcmtkb3duJ1xuICogQGltcG9ydCB7SGFuZGxlIGFzIFRvTWFya2Rvd25IYW5kbGUsIE9wdGlvbnMgYXMgVG9NYXJrZG93bkV4dGVuc2lvbiwgU3RhdGUsIFRyYWNrZXJ9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQb2ludH0gZnJvbSAndW5pc3QnXG4gKiBAaW1wb3J0IHtNZHhKc3hBdHRyaWJ1dGUsIE1keEpzeEF0dHJpYnV0ZVZhbHVlRXhwcmVzc2lvbiwgTWR4SnN4RXhwcmVzc2lvbkF0dHJpYnV0ZSwgTWR4SnN4Rmxvd0VsZW1lbnQsIE1keEpzeFRleHRFbGVtZW50fSBmcm9tICcuLi9pbmRleC5qcydcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIFRhZ1xuICogICBTaW5nbGUgdGFnLlxuICogQHByb3BlcnR5IHtzdHJpbmcgfCB1bmRlZmluZWR9IG5hbWVcbiAqICAgTmFtZSBvZiB0YWcsIG9yIGB1bmRlZmluZWRgIGZvciBmcmFnbWVudC5cbiAqXG4gKiAgID4g8J+RiSAqKk5vdGUqKjogYG51bGxgIGlzIHVzZWQgaW4gdGhlIEFTVCBmb3IgZnJhZ21lbnRzLCBhcyBpdCBzZXJpYWxpemVzIGluXG4gKiAgID4gSlNPTi5cbiAqIEBwcm9wZXJ0eSB7QXJyYXk8TWR4SnN4QXR0cmlidXRlIHwgTWR4SnN4RXhwcmVzc2lvbkF0dHJpYnV0ZT59IGF0dHJpYnV0ZXNcbiAqICAgQXR0cmlidXRlcy5cbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbn0gY2xvc2VcbiAqICAgV2hldGhlciB0aGUgdGFnIGlzIGNsb3NpbmcgKGA8L3g+YCkuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IHNlbGZDbG9zaW5nXG4gKiAgIFdoZXRoZXIgdGhlIHRhZyBpcyBzZWxmLWNsb3NpbmcgKGA8eC8+YCkuXG4gKiBAcHJvcGVydHkge1Rva2VuWydzdGFydCddfSBzdGFydFxuICogICBTdGFydCBwb2ludC5cbiAqIEBwcm9wZXJ0eSB7VG9rZW5bJ3N0YXJ0J119IGVuZFxuICogICBFbmQgcG9pbnQuXG4gKlxuICogQHR5cGVkZWYgVG9NYXJrZG93bk9wdGlvbnNcbiAqICAgQ29uZmlndXJhdGlvbi5cbiAqIEBwcm9wZXJ0eSB7J1wiJyB8IFwiJ1wiIHwgbnVsbCB8IHVuZGVmaW5lZH0gW3F1b3RlPSdcIiddXG4gKiAgIFByZWZlcnJlZCBxdW90ZSB0byB1c2UgYXJvdW5kIGF0dHJpYnV0ZSB2YWx1ZXMgKGRlZmF1bHQ6IGAnXCInYCkuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW4gfCBudWxsIHwgdW5kZWZpbmVkfSBbcXVvdGVTbWFydD1mYWxzZV1cbiAqICAgVXNlIHRoZSBvdGhlciBxdW90ZSBpZiB0aGF0IHJlc3VsdHMgaW4gbGVzcyBieXRlcyAoZGVmYXVsdDogYGZhbHNlYCkuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW4gfCBudWxsIHwgdW5kZWZpbmVkfSBbdGlnaHRTZWxmQ2xvc2luZz1mYWxzZV1cbiAqICAgRG8gbm90IHVzZSBhbiBleHRyYSBzcGFjZSB3aGVuIGNsb3Npbmcgc2VsZi1jbG9zaW5nIGVsZW1lbnRzOiBgPGltZy8+YFxuICogICBpbnN0ZWFkIG9mIGA8aW1nIC8+YCAoZGVmYXVsdDogYGZhbHNlYCkuXG4gKiBAcHJvcGVydHkge251bWJlciB8IG51bGwgfCB1bmRlZmluZWR9IFtwcmludFdpZHRoPUluZmluaXR5XVxuICogICBUcnkgYW5kIHdyYXAgc3ludGF4IGF0IHRoaXMgd2lkdGggKGRlZmF1bHQ6IGBJbmZpbml0eWApLlxuICpcbiAqICAgV2hlbiBzZXQgdG8gYSBmaW5pdGUgbnVtYmVyIChzYXksIGA4MGApLCB0aGUgZm9ybWF0dGVyIHdpbGwgcHJpbnRcbiAqICAgYXR0cmlidXRlcyBvbiBzZXBhcmF0ZSBsaW5lcyB3aGVuIGEgdGFnIGRvZXNu4oCZdCBmaXQgb24gb25lIGxpbmUuXG4gKiAgIFRoZSBub3JtYWwgYmVoYXZpb3IgaXMgdG8gcHJpbnQgYXR0cmlidXRlcyB3aXRoIHNwYWNlcyBiZXR3ZWVuIHRoZW1cbiAqICAgaW5zdGVhZCBvZiBsaW5lIGVuZGluZ3MuXG4gKi9cblxuaW1wb3J0IHtjY291bnR9IGZyb20gJ2Njb3VudCdcbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICdkZXZsb3AnXG5pbXBvcnQge3BhcnNlRW50aXRpZXN9IGZyb20gJ3BhcnNlLWVudGl0aWVzJ1xuaW1wb3J0IHtzdHJpbmdpZnlFbnRpdGllc0xpZ2h0fSBmcm9tICdzdHJpbmdpZnktZW50aXRpZXMnXG5pbXBvcnQge3N0cmluZ2lmeVBvc2l0aW9ufSBmcm9tICd1bmlzdC11dGlsLXN0cmluZ2lmeS1wb3NpdGlvbidcbmltcG9ydCB7VkZpbGVNZXNzYWdlfSBmcm9tICd2ZmlsZS1tZXNzYWdlJ1xuXG5jb25zdCBpbmRlbnQgPSAnICAnXG5cbi8qKlxuICogQ3JlYXRlIGFuIGV4dGVuc2lvbiBmb3IgYG1kYXN0LXV0aWwtZnJvbS1tYXJrZG93bmAgdG8gZW5hYmxlIE1EWCBKU1guXG4gKlxuICogQHJldHVybnMge0Zyb21NYXJrZG93bkV4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWRhc3QtdXRpbC1mcm9tLW1hcmtkb3duYCB0byBlbmFibGUgTURYIEpTWC5cbiAqXG4gKiAgIFdoZW4gdXNpbmcgdGhlIHN5bnRheCBleHRlbnNpb24gd2l0aCBgYWRkUmVzdWx0YCwgbm9kZXMgd2lsbCBoYXZlIGFcbiAqICAgYGRhdGEuZXN0cmVlYCBmaWVsZCBzZXQgdG8gYW4gRVNUcmVlIGBQcm9ncmFtYCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbWR4SnN4RnJvbU1hcmtkb3duKCkge1xuICByZXR1cm4ge1xuICAgIGNhbkNvbnRhaW5Fb2xzOiBbJ21keEpzeFRleHRFbGVtZW50J10sXG4gICAgZW50ZXI6IHtcbiAgICAgIG1keEpzeEZsb3dUYWc6IGVudGVyTWR4SnN4VGFnLFxuICAgICAgbWR4SnN4Rmxvd1RhZ0Nsb3NpbmdNYXJrZXI6IGVudGVyTWR4SnN4VGFnQ2xvc2luZ01hcmtlcixcbiAgICAgIG1keEpzeEZsb3dUYWdBdHRyaWJ1dGU6IGVudGVyTWR4SnN4VGFnQXR0cmlidXRlLFxuICAgICAgbWR4SnN4Rmxvd1RhZ0V4cHJlc3Npb25BdHRyaWJ1dGU6IGVudGVyTWR4SnN4VGFnRXhwcmVzc2lvbkF0dHJpYnV0ZSxcbiAgICAgIG1keEpzeEZsb3dUYWdBdHRyaWJ1dGVWYWx1ZUxpdGVyYWw6IGJ1ZmZlcixcbiAgICAgIG1keEpzeEZsb3dUYWdBdHRyaWJ1dGVWYWx1ZUV4cHJlc3Npb246IGJ1ZmZlcixcbiAgICAgIG1keEpzeEZsb3dUYWdTZWxmQ2xvc2luZ01hcmtlcjogZW50ZXJNZHhKc3hUYWdTZWxmQ2xvc2luZ01hcmtlcixcblxuICAgICAgbWR4SnN4VGV4dFRhZzogZW50ZXJNZHhKc3hUYWcsXG4gICAgICBtZHhKc3hUZXh0VGFnQ2xvc2luZ01hcmtlcjogZW50ZXJNZHhKc3hUYWdDbG9zaW5nTWFya2VyLFxuICAgICAgbWR4SnN4VGV4dFRhZ0F0dHJpYnV0ZTogZW50ZXJNZHhKc3hUYWdBdHRyaWJ1dGUsXG4gICAgICBtZHhKc3hUZXh0VGFnRXhwcmVzc2lvbkF0dHJpYnV0ZTogZW50ZXJNZHhKc3hUYWdFeHByZXNzaW9uQXR0cmlidXRlLFxuICAgICAgbWR4SnN4VGV4dFRhZ0F0dHJpYnV0ZVZhbHVlTGl0ZXJhbDogYnVmZmVyLFxuICAgICAgbWR4SnN4VGV4dFRhZ0F0dHJpYnV0ZVZhbHVlRXhwcmVzc2lvbjogYnVmZmVyLFxuICAgICAgbWR4SnN4VGV4dFRhZ1NlbGZDbG9zaW5nTWFya2VyOiBlbnRlck1keEpzeFRhZ1NlbGZDbG9zaW5nTWFya2VyXG4gICAgfSxcbiAgICBleGl0OiB7XG4gICAgICBtZHhKc3hGbG93VGFnQ2xvc2luZ01hcmtlcjogZXhpdE1keEpzeFRhZ0Nsb3NpbmdNYXJrZXIsXG4gICAgICBtZHhKc3hGbG93VGFnTmFtZVByaW1hcnk6IGV4aXRNZHhKc3hUYWdOYW1lUHJpbWFyeSxcbiAgICAgIG1keEpzeEZsb3dUYWdOYW1lTWVtYmVyOiBleGl0TWR4SnN4VGFnTmFtZU1lbWJlcixcbiAgICAgIG1keEpzeEZsb3dUYWdOYW1lTG9jYWw6IGV4aXRNZHhKc3hUYWdOYW1lTG9jYWwsXG4gICAgICBtZHhKc3hGbG93VGFnRXhwcmVzc2lvbkF0dHJpYnV0ZTogZXhpdE1keEpzeFRhZ0V4cHJlc3Npb25BdHRyaWJ1dGUsXG4gICAgICBtZHhKc3hGbG93VGFnRXhwcmVzc2lvbkF0dHJpYnV0ZVZhbHVlOiBkYXRhLFxuICAgICAgbWR4SnN4Rmxvd1RhZ0F0dHJpYnV0ZU5hbWVQcmltYXJ5OiBleGl0TWR4SnN4VGFnQXR0cmlidXRlTmFtZVByaW1hcnksXG4gICAgICBtZHhKc3hGbG93VGFnQXR0cmlidXRlTmFtZUxvY2FsOiBleGl0TWR4SnN4VGFnQXR0cmlidXRlTmFtZUxvY2FsLFxuICAgICAgbWR4SnN4Rmxvd1RhZ0F0dHJpYnV0ZVZhbHVlTGl0ZXJhbDogZXhpdE1keEpzeFRhZ0F0dHJpYnV0ZVZhbHVlTGl0ZXJhbCxcbiAgICAgIG1keEpzeEZsb3dUYWdBdHRyaWJ1dGVWYWx1ZUxpdGVyYWxWYWx1ZTogZGF0YSxcbiAgICAgIG1keEpzeEZsb3dUYWdBdHRyaWJ1dGVWYWx1ZUV4cHJlc3Npb246XG4gICAgICAgIGV4aXRNZHhKc3hUYWdBdHRyaWJ1dGVWYWx1ZUV4cHJlc3Npb24sXG4gICAgICBtZHhKc3hGbG93VGFnQXR0cmlidXRlVmFsdWVFeHByZXNzaW9uVmFsdWU6IGRhdGEsXG4gICAgICBtZHhKc3hGbG93VGFnU2VsZkNsb3NpbmdNYXJrZXI6IGV4aXRNZHhKc3hUYWdTZWxmQ2xvc2luZ01hcmtlcixcbiAgICAgIG1keEpzeEZsb3dUYWc6IGV4aXRNZHhKc3hUYWcsXG5cbiAgICAgIG1keEpzeFRleHRUYWdDbG9zaW5nTWFya2VyOiBleGl0TWR4SnN4VGFnQ2xvc2luZ01hcmtlcixcbiAgICAgIG1keEpzeFRleHRUYWdOYW1lUHJpbWFyeTogZXhpdE1keEpzeFRhZ05hbWVQcmltYXJ5LFxuICAgICAgbWR4SnN4VGV4dFRhZ05hbWVNZW1iZXI6IGV4aXRNZHhKc3hUYWdOYW1lTWVtYmVyLFxuICAgICAgbWR4SnN4VGV4dFRhZ05hbWVMb2NhbDogZXhpdE1keEpzeFRhZ05hbWVMb2NhbCxcbiAgICAgIG1keEpzeFRleHRUYWdFeHByZXNzaW9uQXR0cmlidXRlOiBleGl0TWR4SnN4VGFnRXhwcmVzc2lvbkF0dHJpYnV0ZSxcbiAgICAgIG1keEpzeFRleHRUYWdFeHByZXNzaW9uQXR0cmlidXRlVmFsdWU6IGRhdGEsXG4gICAgICBtZHhKc3hUZXh0VGFnQXR0cmlidXRlTmFtZVByaW1hcnk6IGV4aXRNZHhKc3hUYWdBdHRyaWJ1dGVOYW1lUHJpbWFyeSxcbiAgICAgIG1keEpzeFRleHRUYWdBdHRyaWJ1dGVOYW1lTG9jYWw6IGV4aXRNZHhKc3hUYWdBdHRyaWJ1dGVOYW1lTG9jYWwsXG4gICAgICBtZHhKc3hUZXh0VGFnQXR0cmlidXRlVmFsdWVMaXRlcmFsOiBleGl0TWR4SnN4VGFnQXR0cmlidXRlVmFsdWVMaXRlcmFsLFxuICAgICAgbWR4SnN4VGV4dFRhZ0F0dHJpYnV0ZVZhbHVlTGl0ZXJhbFZhbHVlOiBkYXRhLFxuICAgICAgbWR4SnN4VGV4dFRhZ0F0dHJpYnV0ZVZhbHVlRXhwcmVzc2lvbjpcbiAgICAgICAgZXhpdE1keEpzeFRhZ0F0dHJpYnV0ZVZhbHVlRXhwcmVzc2lvbixcbiAgICAgIG1keEpzeFRleHRUYWdBdHRyaWJ1dGVWYWx1ZUV4cHJlc3Npb25WYWx1ZTogZGF0YSxcbiAgICAgIG1keEpzeFRleHRUYWdTZWxmQ2xvc2luZ01hcmtlcjogZXhpdE1keEpzeFRhZ1NlbGZDbG9zaW5nTWFya2VyLFxuICAgICAgbWR4SnN4VGV4dFRhZzogZXhpdE1keEpzeFRhZ1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBidWZmZXIoKSB7XG4gICAgdGhpcy5idWZmZXIoKVxuICB9XG5cbiAgLyoqXG4gICAqIENvcHkgYSBwb2ludC1saWtlIHZhbHVlLlxuICAgKlxuICAgKiBAcGFyYW0ge1BvaW50fSBkXG4gICAqICAgUG9pbnQtbGlrZSB2YWx1ZS5cbiAgICogQHJldHVybnMge1BvaW50fVxuICAgKiAgIHVuaXN0IHBvaW50LlxuICAgKi9cbiAgZnVuY3Rpb24gcG9pbnQoZCkge1xuICAgIHJldHVybiB7bGluZTogZC5saW5lLCBjb2x1bW46IGQuY29sdW1uLCBvZmZzZXQ6IGQub2Zmc2V0fVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0Zyb21NYXJrZG93bkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIGRhdGEodG9rZW4pIHtcbiAgICB0aGlzLmNvbmZpZy5lbnRlci5kYXRhLmNhbGwodGhpcywgdG9rZW4pXG4gICAgdGhpcy5jb25maWcuZXhpdC5kYXRhLmNhbGwodGhpcywgdG9rZW4pXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gZW50ZXJNZHhKc3hUYWcodG9rZW4pIHtcbiAgICAvKiogQHR5cGUge1RhZ30gKi9cbiAgICBjb25zdCB0YWcgPSB7XG4gICAgICBuYW1lOiB1bmRlZmluZWQsXG4gICAgICBhdHRyaWJ1dGVzOiBbXSxcbiAgICAgIGNsb3NlOiBmYWxzZSxcbiAgICAgIHNlbGZDbG9zaW5nOiBmYWxzZSxcbiAgICAgIHN0YXJ0OiB0b2tlbi5zdGFydCxcbiAgICAgIGVuZDogdG9rZW4uZW5kXG4gICAgfVxuICAgIGlmICghdGhpcy5kYXRhLm1keEpzeFRhZ1N0YWNrKSB0aGlzLmRhdGEubWR4SnN4VGFnU3RhY2sgPSBbXVxuICAgIHRoaXMuZGF0YS5tZHhKc3hUYWcgPSB0YWdcbiAgICB0aGlzLmJ1ZmZlcigpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gZW50ZXJNZHhKc3hUYWdDbG9zaW5nTWFya2VyKHRva2VuKSB7XG4gICAgY29uc3Qgc3RhY2sgPSB0aGlzLmRhdGEubWR4SnN4VGFnU3RhY2tcbiAgICBhc3NlcnQoc3RhY2ssICdleHBlY3RlZCBgbWR4SnN4VGFnU3RhY2tgJylcblxuICAgIGlmIChzdGFjay5sZW5ndGggPT09IDApIHtcbiAgICAgIHRocm93IG5ldyBWRmlsZU1lc3NhZ2UoXG4gICAgICAgICdVbmV4cGVjdGVkIGNsb3Npbmcgc2xhc2ggYC9gIGluIHRhZywgZXhwZWN0ZWQgYW4gb3BlbiB0YWcgZmlyc3QnLFxuICAgICAgICB7c3RhcnQ6IHRva2VuLnN0YXJ0LCBlbmQ6IHRva2VuLmVuZH0sXG4gICAgICAgICdtZGFzdC11dGlsLW1keC1qc3g6dW5leHBlY3RlZC1jbG9zaW5nLXNsYXNoJ1xuICAgICAgKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBlbnRlck1keEpzeFRhZ0FueUF0dHJpYnV0ZSh0b2tlbikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuXG4gICAgaWYgKHRhZy5jbG9zZSkge1xuICAgICAgdGhyb3cgbmV3IFZGaWxlTWVzc2FnZShcbiAgICAgICAgJ1VuZXhwZWN0ZWQgYXR0cmlidXRlIGluIGNsb3NpbmcgdGFnLCBleHBlY3RlZCB0aGUgZW5kIG9mIHRoZSB0YWcnLFxuICAgICAgICB7c3RhcnQ6IHRva2VuLnN0YXJ0LCBlbmQ6IHRva2VuLmVuZH0sXG4gICAgICAgICdtZGFzdC11dGlsLW1keC1qc3g6dW5leHBlY3RlZC1hdHRyaWJ1dGUnXG4gICAgICApXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0Zyb21NYXJrZG93bkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIGVudGVyTWR4SnN4VGFnU2VsZkNsb3NpbmdNYXJrZXIodG9rZW4pIHtcbiAgICBjb25zdCB0YWcgPSB0aGlzLmRhdGEubWR4SnN4VGFnXG4gICAgYXNzZXJ0KHRhZywgJ2V4cGVjdGVkIGBtZHhKc3hUYWdgJylcblxuICAgIGlmICh0YWcuY2xvc2UpIHtcbiAgICAgIHRocm93IG5ldyBWRmlsZU1lc3NhZ2UoXG4gICAgICAgICdVbmV4cGVjdGVkIHNlbGYtY2xvc2luZyBzbGFzaCBgL2AgaW4gY2xvc2luZyB0YWcsIGV4cGVjdGVkIHRoZSBlbmQgb2YgdGhlIHRhZycsXG4gICAgICAgIHtzdGFydDogdG9rZW4uc3RhcnQsIGVuZDogdG9rZW4uZW5kfSxcbiAgICAgICAgJ21kYXN0LXV0aWwtbWR4LWpzeDp1bmV4cGVjdGVkLXNlbGYtY2xvc2luZy1zbGFzaCdcbiAgICAgIClcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gZXhpdE1keEpzeFRhZ0Nsb3NpbmdNYXJrZXIoKSB7XG4gICAgY29uc3QgdGFnID0gdGhpcy5kYXRhLm1keEpzeFRhZ1xuICAgIGFzc2VydCh0YWcsICdleHBlY3RlZCBgbWR4SnN4VGFnYCcpXG4gICAgdGFnLmNsb3NlID0gdHJ1ZVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0Zyb21NYXJrZG93bkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIGV4aXRNZHhKc3hUYWdOYW1lUHJpbWFyeSh0b2tlbikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuICAgIHRhZy5uYW1lID0gdGhpcy5zbGljZVNlcmlhbGl6ZSh0b2tlbilcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBleGl0TWR4SnN4VGFnTmFtZU1lbWJlcih0b2tlbikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuICAgIHRhZy5uYW1lICs9ICcuJyArIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gZXhpdE1keEpzeFRhZ05hbWVMb2NhbCh0b2tlbikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuICAgIHRhZy5uYW1lICs9ICc6JyArIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gZW50ZXJNZHhKc3hUYWdBdHRyaWJ1dGUodG9rZW4pIHtcbiAgICBjb25zdCB0YWcgPSB0aGlzLmRhdGEubWR4SnN4VGFnXG4gICAgYXNzZXJ0KHRhZywgJ2V4cGVjdGVkIGBtZHhKc3hUYWdgJylcbiAgICBlbnRlck1keEpzeFRhZ0FueUF0dHJpYnV0ZS5jYWxsKHRoaXMsIHRva2VuKVxuICAgIHRhZy5hdHRyaWJ1dGVzLnB1c2goe1xuICAgICAgdHlwZTogJ21keEpzeEF0dHJpYnV0ZScsXG4gICAgICBuYW1lOiAnJyxcbiAgICAgIHZhbHVlOiBudWxsLFxuICAgICAgcG9zaXRpb246IHtcbiAgICAgICAgc3RhcnQ6IHBvaW50KHRva2VuLnN0YXJ0KSxcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogYGVuZGAgd2lsbCBiZSBwYXRjaGVkIGxhdGVyLlxuICAgICAgICBlbmQ6IHVuZGVmaW5lZFxuICAgICAgfVxuICAgIH0pXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gZW50ZXJNZHhKc3hUYWdFeHByZXNzaW9uQXR0cmlidXRlKHRva2VuKSB7XG4gICAgY29uc3QgdGFnID0gdGhpcy5kYXRhLm1keEpzeFRhZ1xuICAgIGFzc2VydCh0YWcsICdleHBlY3RlZCBgbWR4SnN4VGFnYCcpXG4gICAgZW50ZXJNZHhKc3hUYWdBbnlBdHRyaWJ1dGUuY2FsbCh0aGlzLCB0b2tlbilcbiAgICB0YWcuYXR0cmlidXRlcy5wdXNoKHtcbiAgICAgIHR5cGU6ICdtZHhKc3hFeHByZXNzaW9uQXR0cmlidXRlJyxcbiAgICAgIHZhbHVlOiAnJyxcbiAgICAgIHBvc2l0aW9uOiB7XG4gICAgICAgIHN0YXJ0OiBwb2ludCh0b2tlbi5zdGFydCksXG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IGBlbmRgIHdpbGwgYmUgcGF0Y2hlZCBsYXRlci5cbiAgICAgICAgZW5kOiB1bmRlZmluZWRcbiAgICAgIH1cbiAgICB9KVxuICAgIHRoaXMuYnVmZmVyKClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBleGl0TWR4SnN4VGFnRXhwcmVzc2lvbkF0dHJpYnV0ZSh0b2tlbikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuICAgIGNvbnN0IHRhaWwgPSB0YWcuYXR0cmlidXRlc1t0YWcuYXR0cmlidXRlcy5sZW5ndGggLSAxXVxuICAgIGFzc2VydCh0YWlsLnR5cGUgPT09ICdtZHhKc3hFeHByZXNzaW9uQXR0cmlidXRlJylcbiAgICBjb25zdCBlc3RyZWUgPSB0b2tlbi5lc3RyZWVcblxuICAgIHRhaWwudmFsdWUgPSB0aGlzLnJlc3VtZSgpXG4gICAgYXNzZXJ0KHRhaWwucG9zaXRpb24gIT09IHVuZGVmaW5lZClcbiAgICB0YWlsLnBvc2l0aW9uLmVuZCA9IHBvaW50KHRva2VuLmVuZClcblxuICAgIGlmIChlc3RyZWUpIHtcbiAgICAgIHRhaWwuZGF0YSA9IHtlc3RyZWV9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0Zyb21NYXJrZG93bkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIGV4aXRNZHhKc3hUYWdBdHRyaWJ1dGVOYW1lUHJpbWFyeSh0b2tlbikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuICAgIGNvbnN0IG5vZGUgPSB0YWcuYXR0cmlidXRlc1t0YWcuYXR0cmlidXRlcy5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdtZHhKc3hBdHRyaWJ1dGUnKVxuICAgIG5vZGUubmFtZSA9IHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gICAgYXNzZXJ0KG5vZGUucG9zaXRpb24gIT09IHVuZGVmaW5lZClcbiAgICBub2RlLnBvc2l0aW9uLmVuZCA9IHBvaW50KHRva2VuLmVuZClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBleGl0TWR4SnN4VGFnQXR0cmlidXRlTmFtZUxvY2FsKHRva2VuKSB7XG4gICAgY29uc3QgdGFnID0gdGhpcy5kYXRhLm1keEpzeFRhZ1xuICAgIGFzc2VydCh0YWcsICdleHBlY3RlZCBgbWR4SnN4VGFnYCcpXG4gICAgY29uc3Qgbm9kZSA9IHRhZy5hdHRyaWJ1dGVzW3RhZy5hdHRyaWJ1dGVzLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUudHlwZSA9PT0gJ21keEpzeEF0dHJpYnV0ZScpXG4gICAgbm9kZS5uYW1lICs9ICc6JyArIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gICAgYXNzZXJ0KG5vZGUucG9zaXRpb24gIT09IHVuZGVmaW5lZClcbiAgICBub2RlLnBvc2l0aW9uLmVuZCA9IHBvaW50KHRva2VuLmVuZClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBleGl0TWR4SnN4VGFnQXR0cmlidXRlVmFsdWVMaXRlcmFsKHRva2VuKSB7XG4gICAgY29uc3QgdGFnID0gdGhpcy5kYXRhLm1keEpzeFRhZ1xuICAgIGFzc2VydCh0YWcsICdleHBlY3RlZCBgbWR4SnN4VGFnYCcpXG4gICAgY29uc3Qgbm9kZSA9IHRhZy5hdHRyaWJ1dGVzW3RhZy5hdHRyaWJ1dGVzLmxlbmd0aCAtIDFdXG4gICAgbm9kZS52YWx1ZSA9IHBhcnNlRW50aXRpZXModGhpcy5yZXN1bWUoKSwge25vblRlcm1pbmF0ZWQ6IGZhbHNlfSlcbiAgICBhc3NlcnQobm9kZS5wb3NpdGlvbiAhPT0gdW5kZWZpbmVkKVxuICAgIG5vZGUucG9zaXRpb24uZW5kID0gcG9pbnQodG9rZW4uZW5kKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0Zyb21NYXJrZG93bkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIGV4aXRNZHhKc3hUYWdBdHRyaWJ1dGVWYWx1ZUV4cHJlc3Npb24odG9rZW4pIHtcbiAgICBjb25zdCB0YWcgPSB0aGlzLmRhdGEubWR4SnN4VGFnXG4gICAgYXNzZXJ0KHRhZywgJ2V4cGVjdGVkIGBtZHhKc3hUYWdgJylcbiAgICBjb25zdCB0YWlsID0gdGFnLmF0dHJpYnV0ZXNbdGFnLmF0dHJpYnV0ZXMubGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQodGFpbC50eXBlID09PSAnbWR4SnN4QXR0cmlidXRlJylcbiAgICAvKiogQHR5cGUge01keEpzeEF0dHJpYnV0ZVZhbHVlRXhwcmVzc2lvbn0gKi9cbiAgICBjb25zdCBub2RlID0ge3R5cGU6ICdtZHhKc3hBdHRyaWJ1dGVWYWx1ZUV4cHJlc3Npb24nLCB2YWx1ZTogdGhpcy5yZXN1bWUoKX1cbiAgICBjb25zdCBlc3RyZWUgPSB0b2tlbi5lc3RyZWVcblxuICAgIGlmIChlc3RyZWUpIHtcbiAgICAgIG5vZGUuZGF0YSA9IHtlc3RyZWV9XG4gICAgfVxuXG4gICAgdGFpbC52YWx1ZSA9IG5vZGVcbiAgICBhc3NlcnQodGFpbC5wb3NpdGlvbiAhPT0gdW5kZWZpbmVkKVxuICAgIHRhaWwucG9zaXRpb24uZW5kID0gcG9pbnQodG9rZW4uZW5kKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0Zyb21NYXJrZG93bkhhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIGV4aXRNZHhKc3hUYWdTZWxmQ2xvc2luZ01hcmtlcigpIHtcbiAgICBjb25zdCB0YWcgPSB0aGlzLmRhdGEubWR4SnN4VGFnXG4gICAgYXNzZXJ0KHRhZywgJ2V4cGVjdGVkIGBtZHhKc3hUYWdgJylcblxuICAgIHRhZy5zZWxmQ2xvc2luZyA9IHRydWVcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBleGl0TWR4SnN4VGFnKHRva2VuKSB7XG4gICAgY29uc3QgdGFnID0gdGhpcy5kYXRhLm1keEpzeFRhZ1xuICAgIGFzc2VydCh0YWcsICdleHBlY3RlZCBgbWR4SnN4VGFnYCcpXG4gICAgY29uc3Qgc3RhY2sgPSB0aGlzLmRhdGEubWR4SnN4VGFnU3RhY2tcbiAgICBhc3NlcnQoc3RhY2ssICdleHBlY3RlZCBgbWR4SnN4VGFnU3RhY2tgJylcbiAgICBjb25zdCB0YWlsID0gc3RhY2tbc3RhY2subGVuZ3RoIC0gMV1cblxuICAgIGlmICh0YWcuY2xvc2UgJiYgdGFpbC5uYW1lICE9PSB0YWcubmFtZSkge1xuICAgICAgdGhyb3cgbmV3IFZGaWxlTWVzc2FnZShcbiAgICAgICAgJ1VuZXhwZWN0ZWQgY2xvc2luZyB0YWcgYCcgK1xuICAgICAgICAgIHNlcmlhbGl6ZUFiYnJldmlhdGVkVGFnKHRhZykgK1xuICAgICAgICAgICdgLCBleHBlY3RlZCBjb3JyZXNwb25kaW5nIGNsb3NpbmcgdGFnIGZvciBgJyArXG4gICAgICAgICAgc2VyaWFsaXplQWJicmV2aWF0ZWRUYWcodGFpbCkgK1xuICAgICAgICAgICdgICgnICtcbiAgICAgICAgICBzdHJpbmdpZnlQb3NpdGlvbih0YWlsKSArXG4gICAgICAgICAgJyknLFxuICAgICAgICB7c3RhcnQ6IHRva2VuLnN0YXJ0LCBlbmQ6IHRva2VuLmVuZH0sXG4gICAgICAgICdtZGFzdC11dGlsLW1keC1qc3g6ZW5kLXRhZy1taXNtYXRjaCdcbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBFbmQgb2YgYSB0YWcsIHNvIGRyb3AgdGhlIGJ1ZmZlci5cbiAgICB0aGlzLnJlc3VtZSgpXG5cbiAgICBpZiAodGFnLmNsb3NlKSB7XG4gICAgICBzdGFjay5wb3AoKVxuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLmVudGVyKFxuICAgICAgICB7XG4gICAgICAgICAgdHlwZTpcbiAgICAgICAgICAgIHRva2VuLnR5cGUgPT09ICdtZHhKc3hUZXh0VGFnJ1xuICAgICAgICAgICAgICA/ICdtZHhKc3hUZXh0RWxlbWVudCdcbiAgICAgICAgICAgICAgOiAnbWR4SnN4Rmxvd0VsZW1lbnQnLFxuICAgICAgICAgIG5hbWU6IHRhZy5uYW1lIHx8IG51bGwsXG4gICAgICAgICAgYXR0cmlidXRlczogdGFnLmF0dHJpYnV0ZXMsXG4gICAgICAgICAgY2hpbGRyZW46IFtdXG4gICAgICAgIH0sXG4gICAgICAgIHRva2VuLFxuICAgICAgICBvbkVycm9yUmlnaHRJc1RhZ1xuICAgICAgKVxuICAgIH1cblxuICAgIGlmICh0YWcuc2VsZkNsb3NpbmcgfHwgdGFnLmNsb3NlKSB7XG4gICAgICB0aGlzLmV4aXQodG9rZW4sIG9uRXJyb3JMZWZ0SXNUYWcpXG4gICAgfSBlbHNlIHtcbiAgICAgIHN0YWNrLnB1c2godGFnKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtPbkVudGVyRXJyb3J9XG4gICAqL1xuICBmdW5jdGlvbiBvbkVycm9yUmlnaHRJc1RhZyhjbG9zaW5nLCBvcGVuKSB7XG4gICAgY29uc3Qgc3RhY2sgPSB0aGlzLmRhdGEubWR4SnN4VGFnU3RhY2tcbiAgICBhc3NlcnQoc3RhY2ssICdleHBlY3RlZCBgbWR4SnN4VGFnU3RhY2tgJylcbiAgICBjb25zdCB0YWcgPSBzdGFja1tzdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydCh0YWcsICdleHBlY3RlZCBgbWR4SnN4VGFnYCcpXG4gICAgY29uc3QgcGxhY2UgPSBjbG9zaW5nID8gJyBiZWZvcmUgdGhlIGVuZCBvZiBgJyArIGNsb3NpbmcudHlwZSArICdgJyA6ICcnXG4gICAgY29uc3QgcG9zaXRpb24gPSBjbG9zaW5nXG4gICAgICA/IHtzdGFydDogY2xvc2luZy5zdGFydCwgZW5kOiBjbG9zaW5nLmVuZH1cbiAgICAgIDogdW5kZWZpbmVkXG5cbiAgICB0aHJvdyBuZXcgVkZpbGVNZXNzYWdlKFxuICAgICAgJ0V4cGVjdGVkIGEgY2xvc2luZyB0YWcgZm9yIGAnICtcbiAgICAgICAgc2VyaWFsaXplQWJicmV2aWF0ZWRUYWcodGFnKSArXG4gICAgICAgICdgICgnICtcbiAgICAgICAgc3RyaW5naWZ5UG9zaXRpb24oe3N0YXJ0OiBvcGVuLnN0YXJ0LCBlbmQ6IG9wZW4uZW5kfSkgK1xuICAgICAgICAnKScgK1xuICAgICAgICBwbGFjZSxcbiAgICAgIHBvc2l0aW9uLFxuICAgICAgJ21kYXN0LXV0aWwtbWR4LWpzeDplbmQtdGFnLW1pc21hdGNoJ1xuICAgIClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtPbkV4aXRFcnJvcn1cbiAgICovXG4gIGZ1bmN0aW9uIG9uRXJyb3JMZWZ0SXNUYWcoYSwgYikge1xuICAgIGNvbnN0IHRhZyA9IHRoaXMuZGF0YS5tZHhKc3hUYWdcbiAgICBhc3NlcnQodGFnLCAnZXhwZWN0ZWQgYG1keEpzeFRhZ2AnKVxuXG4gICAgdGhyb3cgbmV3IFZGaWxlTWVzc2FnZShcbiAgICAgICdFeHBlY3RlZCB0aGUgY2xvc2luZyB0YWcgYCcgK1xuICAgICAgICBzZXJpYWxpemVBYmJyZXZpYXRlZFRhZyh0YWcpICtcbiAgICAgICAgJ2AgZWl0aGVyIGFmdGVyIHRoZSBlbmQgb2YgYCcgK1xuICAgICAgICBiLnR5cGUgK1xuICAgICAgICAnYCAoJyArXG4gICAgICAgIHN0cmluZ2lmeVBvc2l0aW9uKGIuZW5kKSArXG4gICAgICAgICcpIG9yIGFub3RoZXIgb3BlbmluZyB0YWcgYWZ0ZXIgdGhlIHN0YXJ0IG9mIGAnICtcbiAgICAgICAgYi50eXBlICtcbiAgICAgICAgJ2AgKCcgK1xuICAgICAgICBzdHJpbmdpZnlQb3NpdGlvbihiLnN0YXJ0KSArXG4gICAgICAgICcpJyxcbiAgICAgIHtzdGFydDogYS5zdGFydCwgZW5kOiBhLmVuZH0sXG4gICAgICAnbWRhc3QtdXRpbC1tZHgtanN4OmVuZC10YWctbWlzbWF0Y2gnXG4gICAgKVxuICB9XG5cbiAgLyoqXG4gICAqIFNlcmlhbGl6ZSBhIHRhZywgZXhjbHVkaW5nIGF0dHJpYnV0ZXMuXG4gICAqIGBzZWxmLWNsb3NpbmdgIGlzIG5vdCBzdXBwb3J0ZWQsIGJlY2F1c2Ugd2UgZG9u4oCZdCBuZWVkIGl0IHlldC5cbiAgICpcbiAgICogQHBhcmFtIHtUYWd9IHRhZ1xuICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgKi9cbiAgZnVuY3Rpb24gc2VyaWFsaXplQWJicmV2aWF0ZWRUYWcodGFnKSB7XG4gICAgcmV0dXJuICc8JyArICh0YWcuY2xvc2UgPyAnLycgOiAnJykgKyAodGFnLm5hbWUgfHwgJycpICsgJz4nXG4gIH1cbn1cblxuLyoqXG4gKiBDcmVhdGUgYW4gZXh0ZW5zaW9uIGZvciBgbWRhc3QtdXRpbC10by1tYXJrZG93bmAgdG8gZW5hYmxlIE1EWCBKU1guXG4gKlxuICogVGhpcyBleHRlbnNpb24gY29uZmlndXJlcyBgbWRhc3QtdXRpbC10by1tYXJrZG93bmAgd2l0aFxuICogYG9wdGlvbnMuZmVuY2VzOiB0cnVlYCBhbmQgYG9wdGlvbnMucmVzb3VyY2VMaW5rOiB0cnVlYCB0b28sIGRvIG5vdFxuICogb3ZlcndyaXRlIHRoZW0hXG4gKlxuICogQHBhcmFtIHtUb01hcmtkb3duT3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uIChvcHRpb25hbCkuXG4gKiBAcmV0dXJucyB7VG9NYXJrZG93bkV4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWRhc3QtdXRpbC10by1tYXJrZG93bmAgdG8gZW5hYmxlIE1EWCBKU1guXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtZHhKc3hUb01hcmtkb3duKG9wdGlvbnMpIHtcbiAgY29uc3Qgb3B0aW9uc18gPSBvcHRpb25zIHx8IHt9XG4gIGNvbnN0IHF1b3RlID0gb3B0aW9uc18ucXVvdGUgfHwgJ1wiJ1xuICBjb25zdCBxdW90ZVNtYXJ0ID0gb3B0aW9uc18ucXVvdGVTbWFydCB8fCBmYWxzZVxuICBjb25zdCB0aWdodFNlbGZDbG9zaW5nID0gb3B0aW9uc18udGlnaHRTZWxmQ2xvc2luZyB8fCBmYWxzZVxuICBjb25zdCBwcmludFdpZHRoID0gb3B0aW9uc18ucHJpbnRXaWR0aCB8fCBOdW1iZXIuUE9TSVRJVkVfSU5GSU5JVFlcbiAgY29uc3QgYWx0ZXJuYXRpdmUgPSBxdW90ZSA9PT0gJ1wiJyA/IFwiJ1wiIDogJ1wiJ1xuXG4gIGlmIChxdW90ZSAhPT0gJ1wiJyAmJiBxdW90ZSAhPT0gXCInXCIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBhdHRyaWJ1dGUgdmFsdWVzIHdpdGggYCcgK1xuICAgICAgICBxdW90ZSArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5xdW90ZWAsIGV4cGVjdGVkIGBcImAsIG9yIGBcXCdgJ1xuICAgIClcbiAgfVxuXG4gIG1keEVsZW1lbnQucGVlayA9IHBlZWtFbGVtZW50XG5cbiAgcmV0dXJuIHtcbiAgICBoYW5kbGVyczoge1xuICAgICAgbWR4SnN4Rmxvd0VsZW1lbnQ6IG1keEVsZW1lbnQsXG4gICAgICBtZHhKc3hUZXh0RWxlbWVudDogbWR4RWxlbWVudFxuICAgIH0sXG4gICAgdW5zYWZlOiBbXG4gICAgICB7Y2hhcmFjdGVyOiAnPCcsIGluQ29uc3RydWN0OiBbJ3BocmFzaW5nJ119LFxuICAgICAge2F0QnJlYWs6IHRydWUsIGNoYXJhY3RlcjogJzwnfVxuICAgIF0sXG4gICAgLy8gQWx3YXlzIGdlbmVyYXRlIGZlbmNlZCBjb2RlIChuZXZlciBpbmRlbnRlZCBjb2RlKS5cbiAgICBmZW5jZXM6IHRydWUsXG4gICAgLy8gQWx3YXlzIGdlbmVyYXRlIGxpbmtzIHdpdGggcmVzb3VyY2VzIChuZXZlciBhdXRvbGlua3MpLlxuICAgIHJlc291cmNlTGluazogdHJ1ZVxuICB9XG5cbiAgLyoqXG4gICAqIEB0eXBlIHtUb01hcmtkb3duSGFuZGxlfVxuICAgKiBAcGFyYW0ge01keEpzeEZsb3dFbGVtZW50IHwgTWR4SnN4VGV4dEVsZW1lbnR9IG5vZGVcbiAgICovXG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBjb21wbGV4aXR5XG4gIGZ1bmN0aW9uIG1keEVsZW1lbnQobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgICBjb25zdCBmbG93ID0gbm9kZS50eXBlID09PSAnbWR4SnN4Rmxvd0VsZW1lbnQnXG4gICAgY29uc3Qgc2VsZkNsb3NpbmcgPSBub2RlLm5hbWVcbiAgICAgID8gIW5vZGUuY2hpbGRyZW4gfHwgbm9kZS5jaGlsZHJlbi5sZW5ndGggPT09IDBcbiAgICAgIDogZmFsc2VcbiAgICBjb25zdCBkZXB0aCA9IGluZmVyRGVwdGgoc3RhdGUpXG4gICAgY29uc3QgY3VycmVudEluZGVudCA9IGNyZWF0ZUluZGVudChkZXB0aClcbiAgICBjb25zdCB0cmFja2VyT25lTGluZSA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcbiAgICBjb25zdCB0cmFja2VyTXVsdGlMaW5lID0gc3RhdGUuY3JlYXRlVHJhY2tlcihpbmZvKVxuICAgIC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn0gKi9cbiAgICBjb25zdCBzZXJpYWxpemVkQXR0cmlidXRlcyA9IFtdXG4gICAgY29uc3QgcHJlZml4ID0gKGZsb3cgPyBjdXJyZW50SW5kZW50IDogJycpICsgJzwnICsgKG5vZGUubmFtZSB8fCAnJylcbiAgICBjb25zdCBleGl0ID0gc3RhdGUuZW50ZXIobm9kZS50eXBlKVxuXG4gICAgdHJhY2tlck9uZUxpbmUubW92ZShwcmVmaXgpXG4gICAgdHJhY2tlck11bHRpTGluZS5tb3ZlKHByZWZpeClcblxuICAgIC8vIE5vbmUuXG4gICAgaWYgKG5vZGUuYXR0cmlidXRlcyAmJiBub2RlLmF0dHJpYnV0ZXMubGVuZ3RoID4gMCkge1xuICAgICAgaWYgKCFub2RlLm5hbWUpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW5ub3Qgc2VyaWFsaXplIGZyYWdtZW50IHcvIGF0dHJpYnV0ZXMnKVxuICAgICAgfVxuXG4gICAgICBsZXQgaW5kZXggPSAtMVxuICAgICAgd2hpbGUgKCsraW5kZXggPCBub2RlLmF0dHJpYnV0ZXMubGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IGF0dHJpYnV0ZSA9IG5vZGUuYXR0cmlidXRlc1tpbmRleF1cbiAgICAgICAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gICAgICAgIGxldCByZXN1bHRcblxuICAgICAgICBpZiAoYXR0cmlidXRlLnR5cGUgPT09ICdtZHhKc3hFeHByZXNzaW9uQXR0cmlidXRlJykge1xuICAgICAgICAgIHJlc3VsdCA9ICd7JyArIChhdHRyaWJ1dGUudmFsdWUgfHwgJycpICsgJ30nXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaWYgKCFhdHRyaWJ1dGUubmFtZSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDYW5ub3Qgc2VyaWFsaXplIGF0dHJpYnV0ZSB3L28gbmFtZScpXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc3QgdmFsdWUgPSBhdHRyaWJ1dGUudmFsdWVcbiAgICAgICAgICBjb25zdCBsZWZ0ID0gYXR0cmlidXRlLm5hbWVcbiAgICAgICAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICAgICAgICBsZXQgcmlnaHQgPSAnJ1xuXG4gICAgICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIC8vIEVtcHR5LlxuICAgICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgcmlnaHQgPSAneycgKyAodmFsdWUudmFsdWUgfHwgJycpICsgJ30nXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIElmIHRoZSBhbHRlcm5hdGl2ZSBpcyBsZXNzIGNvbW1vbiB0aGFuIGBxdW90ZWAsIHN3aXRjaC5cbiAgICAgICAgICAgIGNvbnN0IGFwcGxpZWRRdW90ZSA9XG4gICAgICAgICAgICAgIHF1b3RlU21hcnQgJiYgY2NvdW50KHZhbHVlLCBxdW90ZSkgPiBjY291bnQodmFsdWUsIGFsdGVybmF0aXZlKVxuICAgICAgICAgICAgICAgID8gYWx0ZXJuYXRpdmVcbiAgICAgICAgICAgICAgICA6IHF1b3RlXG4gICAgICAgICAgICByaWdodCA9XG4gICAgICAgICAgICAgIGFwcGxpZWRRdW90ZSArXG4gICAgICAgICAgICAgIHN0cmluZ2lmeUVudGl0aWVzTGlnaHQodmFsdWUsIHtzdWJzZXQ6IFthcHBsaWVkUXVvdGVdfSkgK1xuICAgICAgICAgICAgICBhcHBsaWVkUXVvdGVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICByZXN1bHQgPSBsZWZ0ICsgKHJpZ2h0ID8gJz0nIDogJycpICsgcmlnaHRcbiAgICAgICAgfVxuXG4gICAgICAgIHNlcmlhbGl6ZWRBdHRyaWJ1dGVzLnB1c2gocmVzdWx0KVxuICAgICAgfVxuICAgIH1cblxuICAgIGxldCBhdHRyaWJ1dGVzT25UaGVpck93bkxpbmUgPSBmYWxzZVxuICAgIGNvbnN0IGF0dHJpYnV0ZXNPbk9uZUxpbmUgPSBzZXJpYWxpemVkQXR0cmlidXRlcy5qb2luKCcgJylcblxuICAgIGlmIChcbiAgICAgIC8vIEJsb2NrOlxuICAgICAgZmxvdyAmJlxuICAgICAgLy8gSW5jbHVkaW5nIGEgbGluZSBlbmRpbmcgKGV4cHJlc3Npb25zKS5cbiAgICAgICgvXFxyP1xcbnxcXHIvLnRlc3QoYXR0cmlidXRlc09uT25lTGluZSkgfHxcbiAgICAgICAgLy8gQ3VycmVudCBwb3NpdGlvbiAoaW5jbHVkaW5nIGA8dGFnYCkuXG4gICAgICAgIHRyYWNrZXJPbmVMaW5lLmN1cnJlbnQoKS5ub3cuY29sdW1uICtcbiAgICAgICAgICAvLyAtMSBiZWNhdXNlIGNvbHVtbnMsICsxIGZvciBgIGAgYmVmb3JlIGF0dHJpYnV0ZXMuXG4gICAgICAgICAgLy8gQXR0cmlidXRlcyBqb2luZWQgYnkgc3BhY2VzLlxuICAgICAgICAgIGF0dHJpYnV0ZXNPbk9uZUxpbmUubGVuZ3RoICtcbiAgICAgICAgICAvLyBgIC8+YC5cbiAgICAgICAgICAoc2VsZkNsb3NpbmcgPyAodGlnaHRTZWxmQ2xvc2luZyA/IDIgOiAzKSA6IDEpID5cbiAgICAgICAgICBwcmludFdpZHRoKVxuICAgICkge1xuICAgICAgYXR0cmlidXRlc09uVGhlaXJPd25MaW5lID0gdHJ1ZVxuICAgIH1cblxuICAgIGxldCB0cmFja2VyID0gdHJhY2tlck9uZUxpbmVcbiAgICBsZXQgdmFsdWUgPSBwcmVmaXhcblxuICAgIGlmIChhdHRyaWJ1dGVzT25UaGVpck93bkxpbmUpIHtcbiAgICAgIHRyYWNrZXIgPSB0cmFja2VyTXVsdGlMaW5lXG5cbiAgICAgIGxldCBpbmRleCA9IC0xXG5cbiAgICAgIHdoaWxlICgrK2luZGV4IDwgc2VyaWFsaXplZEF0dHJpYnV0ZXMubGVuZ3RoKSB7XG4gICAgICAgIC8vIE9ubHkgaW5kZW50IGZpcnN0IGxpbmUgb2Ygb2YgYXR0cmlidXRlcywgd2UgY2Fu4oCZdCBpbmRlbnQgYXR0cmlidXRlXG4gICAgICAgIC8vIHZhbHVlcy5cbiAgICAgICAgc2VyaWFsaXplZEF0dHJpYnV0ZXNbaW5kZXhdID1cbiAgICAgICAgICBjdXJyZW50SW5kZW50ICsgaW5kZW50ICsgc2VyaWFsaXplZEF0dHJpYnV0ZXNbaW5kZXhdXG4gICAgICB9XG5cbiAgICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShcbiAgICAgICAgJ1xcbicgKyBzZXJpYWxpemVkQXR0cmlidXRlcy5qb2luKCdcXG4nKSArICdcXG4nICsgY3VycmVudEluZGVudFxuICAgICAgKVxuICAgIH0gZWxzZSBpZiAoYXR0cmlidXRlc09uT25lTGluZSkge1xuICAgICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKCcgJyArIGF0dHJpYnV0ZXNPbk9uZUxpbmUpXG4gICAgfVxuXG4gICAgaWYgKHNlbGZDbG9zaW5nKSB7XG4gICAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoXG4gICAgICAgICh0aWdodFNlbGZDbG9zaW5nIHx8IGF0dHJpYnV0ZXNPblRoZWlyT3duTGluZSA/ICcnIDogJyAnKSArICcvJ1xuICAgICAgKVxuICAgIH1cblxuICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnPicpXG5cbiAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHtcbiAgICAgIGlmIChub2RlLnR5cGUgPT09ICdtZHhKc3hUZXh0RWxlbWVudCcpIHtcbiAgICAgICAgdmFsdWUgKz0gdHJhY2tlci5tb3ZlKFxuICAgICAgICAgIHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIHtcbiAgICAgICAgICAgIC4uLnRyYWNrZXIuY3VycmVudCgpLFxuICAgICAgICAgICAgYmVmb3JlOiAnPicsXG4gICAgICAgICAgICBhZnRlcjogJzwnXG4gICAgICAgICAgfSlcbiAgICAgICAgKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdHJhY2tlci5zaGlmdCgyKVxuICAgICAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoJ1xcbicpXG4gICAgICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZShjb250YWluZXJGbG93KG5vZGUsIHN0YXRlLCB0cmFja2VyLmN1cnJlbnQoKSkpXG4gICAgICAgIHZhbHVlICs9IHRyYWNrZXIubW92ZSgnXFxuJylcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIXNlbGZDbG9zaW5nKSB7XG4gICAgICB2YWx1ZSArPSB0cmFja2VyLm1vdmUoXG4gICAgICAgIChmbG93ID8gY3VycmVudEluZGVudCA6ICcnKSArICc8LycgKyAobm9kZS5uYW1lIHx8ICcnKSArICc+J1xuICAgICAgKVxuICAgIH1cblxuICAgIGV4aXQoKVxuICAgIHJldHVybiB2YWx1ZVxuICB9XG59XG5cbi8vIE1vZGlmaWVkIGNvcHkgb2Y6XG4vLyA8aHR0cHM6Ly9naXRodWIuY29tL3N5bnRheC10cmVlL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vYmxvYi9hMzgxY2JjL2xpYi91dGlsL2NvbnRhaW5lci1mbG93LmpzPi5cbi8vXG4vLyBUbyBkbzogYWRkIGBpbmRlbnRgIHN1cHBvcnQgdG8gYG1kYXN0LXV0aWwtdG8tbWFya2Rvd25gLlxuLy8gQXMgaW5kZW50cyBhcmUgb25seSB1c2VkIGZvciBKU1gsIGl04oCZcyBmaW5lIGZvciBub3csIGJ1dCBwZXJoYXBzIGJldHRlclxuLy8gdGhlcmUuXG4vKipcbiAqIEBwYXJhbSB7TWR4SnN4Rmxvd0VsZW1lbnR9IHBhcmVudFxuICogICBQYXJlbnQgb2YgZmxvdyBub2Rlcy5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEBwYXJhbSB7UmV0dXJuVHlwZTxUcmFja2VyWydjdXJyZW50J10+fSBpbmZvXG4gKiAgIEluZm8gb24gd2hlcmUgd2UgYXJlIGluIHRoZSBkb2N1bWVudCB3ZSBhcmUgZ2VuZXJhdGluZy5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFNlcmlhbGl6ZWQgY2hpbGRyZW4sIGpvaW5lZCBieSAoYmxhbmspIGxpbmVzLlxuICovXG5mdW5jdGlvbiBjb250YWluZXJGbG93KHBhcmVudCwgc3RhdGUsIGluZm8pIHtcbiAgY29uc3QgaW5kZXhTdGFjayA9IHN0YXRlLmluZGV4U3RhY2tcbiAgY29uc3QgY2hpbGRyZW4gPSBwYXJlbnQuY2hpbGRyZW5cbiAgY29uc3QgdHJhY2tlciA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcbiAgY29uc3QgY3VycmVudEluZGVudCA9IGNyZWF0ZUluZGVudChpbmZlckRlcHRoKHN0YXRlKSlcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmc+fSAqL1xuICBjb25zdCByZXN1bHRzID0gW11cbiAgbGV0IGluZGV4ID0gLTFcblxuICBpbmRleFN0YWNrLnB1c2goLTEpXG5cbiAgd2hpbGUgKCsraW5kZXggPCBjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICBjb25zdCBjaGlsZCA9IGNoaWxkcmVuW2luZGV4XVxuXG4gICAgaW5kZXhTdGFja1tpbmRleFN0YWNrLmxlbmd0aCAtIDFdID0gaW5kZXhcblxuICAgIGNvbnN0IGNoaWxkSW5mbyA9IHtiZWZvcmU6ICdcXG4nLCBhZnRlcjogJ1xcbicsIC4uLnRyYWNrZXIuY3VycmVudCgpfVxuXG4gICAgY29uc3QgcmVzdWx0ID0gc3RhdGUuaGFuZGxlKGNoaWxkLCBwYXJlbnQsIHN0YXRlLCBjaGlsZEluZm8pXG5cbiAgICBjb25zdCBzZXJpYWxpemVkQ2hpbGQgPVxuICAgICAgY2hpbGQudHlwZSA9PT0gJ21keEpzeEZsb3dFbGVtZW50J1xuICAgICAgICA/IHJlc3VsdFxuICAgICAgICA6IHN0YXRlLmluZGVudExpbmVzKHJlc3VsdCwgZnVuY3Rpb24gKGxpbmUsIF8sIGJsYW5rKSB7XG4gICAgICAgICAgICByZXR1cm4gKGJsYW5rID8gJycgOiBjdXJyZW50SW5kZW50KSArIGxpbmVcbiAgICAgICAgICB9KVxuXG4gICAgcmVzdWx0cy5wdXNoKHRyYWNrZXIubW92ZShzZXJpYWxpemVkQ2hpbGQpKVxuXG4gICAgaWYgKGNoaWxkLnR5cGUgIT09ICdsaXN0Jykge1xuICAgICAgc3RhdGUuYnVsbGV0TGFzdFVzZWQgPSB1bmRlZmluZWRcbiAgICB9XG5cbiAgICBpZiAoaW5kZXggPCBjaGlsZHJlbi5sZW5ndGggLSAxKSB7XG4gICAgICByZXN1bHRzLnB1c2godHJhY2tlci5tb3ZlKCdcXG5cXG4nKSlcbiAgICB9XG4gIH1cblxuICBpbmRleFN0YWNrLnBvcCgpXG5cbiAgcmV0dXJuIHJlc3VsdHMuam9pbignJylcbn1cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge251bWJlcn1cbiAqL1xuZnVuY3Rpb24gaW5mZXJEZXB0aChzdGF0ZSkge1xuICBsZXQgZGVwdGggPSAwXG4gIGxldCBpbmRleCA9IHN0YXRlLnN0YWNrLmxlbmd0aFxuXG4gIHdoaWxlICgtLWluZGV4ID4gLTEpIHtcbiAgICBjb25zdCBuYW1lID0gc3RhdGUuc3RhY2tbaW5kZXhdXG5cbiAgICBpZiAobmFtZSA9PT0gJ2Jsb2NrcXVvdGUnIHx8IG5hbWUgPT09ICdsaXN0SXRlbScpIGJyZWFrXG4gICAgaWYgKG5hbWUgPT09ICdtZHhKc3hGbG93RWxlbWVudCcpIGRlcHRoKytcbiAgfVxuXG4gIHJldHVybiBkZXB0aFxufVxuXG4vKipcbiAqIEBwYXJhbSB7bnVtYmVyfSBkZXB0aFxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZnVuY3Rpb24gY3JlYXRlSW5kZW50KGRlcHRoKSB7XG4gIHJldHVybiBpbmRlbnQucmVwZWF0KGRlcHRoKVxufVxuXG4vKipcbiAqIEB0eXBlIHtUb01hcmtkb3duSGFuZGxlfVxuICovXG5mdW5jdGlvbiBwZWVrRWxlbWVudCgpIHtcbiAgcmV0dXJuICc8J1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-mdx-jsx/lib/index.js\n");

/***/ })

};
;