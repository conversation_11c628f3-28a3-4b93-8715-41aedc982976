"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-pdf";
exports.ids = ["vendor-chunks/react-pdf"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Document.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Document.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dequal */ \"(ssr)/./node_modules/dequal/dist/index.mjs\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./DocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _LinkService_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./LinkService.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\");\n/* harmony import */ var _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PasswordResponses.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { PDFDataRangeTransport } = pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__;\nconst defaultOnPassword = (callback, reason)=>{\n    switch(reason){\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].NEED_PASSWORD:\n            {\n                const password = prompt('Enter the password to open this PDF file.');\n                callback(password);\n                break;\n            }\n        case _PasswordResponses_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"].INCORRECT_PASSWORD:\n            {\n                const password = prompt('Invalid password. Please try again.');\n                callback(password);\n                break;\n            }\n        default:\n    }\n};\nfunction isParameterObject(file) {\n    return typeof file === 'object' && file !== null && ('data' in file || 'range' in file || 'url' in file);\n}\n/**\n * Loads a document passed using `file` prop.\n */ const Document = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function Document(_a, ref) {\n    var { children, className, error = 'Failed to load PDF file.', externalLinkRel, externalLinkTarget, file, inputRef, imageResourcesPath, loading = 'Loading PDF…', noData = 'No PDF file specified.', onItemClick, onLoadError: onLoadErrorProps, onLoadProgress, onLoadSuccess: onLoadSuccessProps, onPassword = defaultOnPassword, onSourceError: onSourceErrorProps, onSourceSuccess: onSourceSuccessProps, options, renderMode, rotate } = _a, otherProps = __rest(_a, [\n        \"children\",\n        \"className\",\n        \"error\",\n        \"externalLinkRel\",\n        \"externalLinkTarget\",\n        \"file\",\n        \"inputRef\",\n        \"imageResourcesPath\",\n        \"loading\",\n        \"noData\",\n        \"onItemClick\",\n        \"onLoadError\",\n        \"onLoadProgress\",\n        \"onLoadSuccess\",\n        \"onPassword\",\n        \"onSourceError\",\n        \"onSourceSuccess\",\n        \"options\",\n        \"renderMode\",\n        \"rotate\"\n    ]);\n    const [sourceState, sourceDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: source, error: sourceError } = sourceState;\n    const [pdfState, pdfDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: pdf, error: pdfError } = pdfState;\n    const linkService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new _LinkService_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]());\n    const pages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const prevFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    const prevOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    if (file && file !== prevFile.current && isParameterObject(file)) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(!(0,dequal__WEBPACK_IMPORTED_MODULE_5__.dequal)(file, prevFile.current), `File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"file\" prop.`);\n        prevFile.current = file;\n    }\n    // Detect non-memoized changes in options prop\n    if (options && options !== prevOptions.current) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(!(0,dequal__WEBPACK_IMPORTED_MODULE_5__.dequal)(options, prevOptions.current), `Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"options\" prop.`);\n        prevOptions.current = options;\n    }\n    const viewer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        // Handling jumping to internal links target\n        scrollPageIntoView: {\n            \"Document.Document.useRef[viewer]\": (args)=>{\n                const { dest, pageNumber, pageIndex = pageNumber - 1 } = args;\n                // First, check if custom handling of onItemClick was provided\n                if (onItemClick) {\n                    onItemClick({\n                        dest,\n                        pageIndex,\n                        pageNumber\n                    });\n                    return;\n                }\n                // If not, try to look for target page within the <Document>.\n                const page = pages.current[pageIndex];\n                if (page) {\n                    // Scroll to the page automatically\n                    page.scrollIntoView();\n                    return;\n                }\n                warning__WEBPACK_IMPORTED_MODULE_4__(false, `An internal link leading to page ${pageNumber} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`);\n            }\n        }[\"Document.Document.useRef[viewer]\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"Document.Document.useImperativeHandle\": ()=>({\n                linkService,\n                pages,\n                viewer\n            })\n    }[\"Document.Document.useImperativeHandle\"], []);\n    /**\n     * Called when a document source is resolved correctly\n     */ function onSourceSuccess() {\n        if (onSourceSuccessProps) {\n            onSourceSuccessProps();\n        }\n    }\n    /**\n     * Called when a document source failed to be resolved correctly\n     */ function onSourceError() {\n        if (!sourceError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, sourceError.toString());\n        if (onSourceErrorProps) {\n            onSourceErrorProps(sourceError);\n        }\n    }\n    function resetSource() {\n        sourceDispatch({\n            type: 'RESET'\n        });\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: See https://github.com/biomejs/biome/issues/3080\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(resetSource, [\n        file,\n        sourceDispatch\n    ]);\n    const findDocumentSource = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Document.Document.useCallback[findDocumentSource]\": ()=>__awaiter(this, void 0, void 0, {\n                \"Document.Document.useCallback[findDocumentSource]\": function*() {\n                    if (!file) {\n                        return null;\n                    }\n                    // File is a string\n                    if (typeof file === 'string') {\n                        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isDataURI)(file)) {\n                            const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.dataURItoByteString)(file);\n                            return {\n                                data: fileByteString\n                            };\n                        }\n                        (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.displayCORSWarning)();\n                        return {\n                            url: file\n                        };\n                    }\n                    // File is PDFDataRangeTransport\n                    if (file instanceof PDFDataRangeTransport) {\n                        return {\n                            range: file\n                        };\n                    }\n                    // File is an ArrayBuffer\n                    if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isArrayBuffer)(file)) {\n                        return {\n                            data: file\n                        };\n                    }\n                    /**\n         * The cases below are browser-only.\n         * If you're running on a non-browser environment, these cases will be of no use.\n         */ if (_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isBrowser) {\n                        // File is a Blob\n                        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isBlob)(file)) {\n                            const data = yield (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.loadFromFile)(file);\n                            return {\n                                data\n                            };\n                        }\n                    }\n                    // At this point, file must be an object\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(typeof file === 'object', 'Invalid parameter in file, need either Uint8Array, string or a parameter object');\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(isParameterObject(file), 'Invalid parameter object: need either .data, .range or .url');\n                    // File .url is a string\n                    if ('url' in file && typeof file.url === 'string') {\n                        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.isDataURI)(file.url)) {\n                            const { url } = file, otherParams = __rest(file, [\n                                \"url\"\n                            ]);\n                            const fileByteString = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.dataURItoByteString)(url);\n                            return Object.assign({\n                                data: fileByteString\n                            }, otherParams);\n                        }\n                        (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.displayCORSWarning)();\n                    }\n                    return file;\n                }\n            }[\"Document.Document.useCallback[findDocumentSource]\"])\n    }[\"Document.Document.useCallback[findDocumentSource]\"], [\n        file\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Document.Document.useEffect\": ()=>{\n            const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(findDocumentSource());\n            cancellable.promise.then({\n                \"Document.Document.useEffect\": (nextSource)=>{\n                    sourceDispatch({\n                        type: 'RESOLVE',\n                        value: nextSource\n                    });\n                }\n            }[\"Document.Document.useEffect\"]).catch({\n                \"Document.Document.useEffect\": (error)=>{\n                    sourceDispatch({\n                        type: 'REJECT',\n                        error\n                    });\n                }\n            }[\"Document.Document.useEffect\"]);\n            return ({\n                \"Document.Document.useEffect\": ()=>{\n                    (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.cancelRunningTask)(cancellable);\n                }\n            })[\"Document.Document.useEffect\"];\n        }\n    }[\"Document.Document.useEffect\"], [\n        findDocumentSource,\n        sourceDispatch\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Document.Document.useEffect\": ()=>{\n            if (typeof source === 'undefined') {\n                return;\n            }\n            if (source === false) {\n                onSourceError();\n                return;\n            }\n            onSourceSuccess();\n        }\n    }[\"Document.Document.useEffect\"], [\n        source\n    ]);\n    /**\n     * Called when a document is read successfully\n     */ function onLoadSuccess() {\n        if (!pdf) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onLoadSuccessProps) {\n            onLoadSuccessProps(pdf);\n        }\n        pages.current = new Array(pdf.numPages);\n        linkService.current.setDocument(pdf);\n    }\n    /**\n     * Called when a document failed to read successfully\n     */ function onLoadError() {\n        if (!pdfError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, pdfError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pdfError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on source change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetDocument() {\n        pdfDispatch({\n            type: 'RESET'\n        });\n    }, [\n        pdfDispatch,\n        source\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadDocument() {\n        if (!source) {\n            return;\n        }\n        const documentInitParams = options ? Object.assign(Object.assign({}, source), options) : source;\n        const destroyable = pdfjs_dist__WEBPACK_IMPORTED_MODULE_6__.getDocument(documentInitParams);\n        if (onLoadProgress) {\n            destroyable.onProgress = onLoadProgress;\n        }\n        if (onPassword) {\n            destroyable.onPassword = onPassword;\n        }\n        const loadingTask = destroyable;\n        const loadingPromise = loadingTask.promise.then({\n            \"Document.Document.useEffect.loadDocument.loadingPromise\": (nextPdf)=>{\n                pdfDispatch({\n                    type: 'RESOLVE',\n                    value: nextPdf\n                });\n            }\n        }[\"Document.Document.useEffect.loadDocument.loadingPromise\"]).catch({\n            \"Document.Document.useEffect.loadDocument.loadingPromise\": (error)=>{\n                if (loadingTask.destroyed) {\n                    return;\n                }\n                pdfDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"Document.Document.useEffect.loadDocument.loadingPromise\"]);\n        return ({\n            \"Document.Document.useEffect.loadDocument\": ()=>{\n                loadingPromise.finally({\n                    \"Document.Document.useEffect.loadDocument\": ()=>loadingTask.destroy()\n                }[\"Document.Document.useEffect.loadDocument\"]);\n            }\n        })[\"Document.Document.useEffect.loadDocument\"];\n    }, [\n        options,\n        pdfDispatch,\n        source\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Document.Document.useEffect\": ()=>{\n            if (typeof pdf === 'undefined') {\n                return;\n            }\n            if (pdf === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"Document.Document.useEffect\"], [\n        pdf\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function setupLinkService() {\n        linkService.current.setViewer(viewer.current);\n        linkService.current.setExternalLinkRel(externalLinkRel);\n        linkService.current.setExternalLinkTarget(externalLinkTarget);\n    }, [\n        externalLinkRel,\n        externalLinkTarget\n    ]);\n    const registerPage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Document.Document.useCallback[registerPage]\": (pageIndex, ref)=>{\n            pages.current[pageIndex] = ref;\n        }\n    }[\"Document.Document.useCallback[registerPage]\"], []);\n    const unregisterPage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Document.Document.useCallback[unregisterPage]\": (pageIndex)=>{\n            delete pages.current[pageIndex];\n        }\n    }[\"Document.Document.useCallback[unregisterPage]\"], []);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Document.Document.useMemo[childContext]\": ()=>({\n                imageResourcesPath,\n                linkService: linkService.current,\n                onItemClick,\n                pdf,\n                registerPage,\n                renderMode,\n                rotate,\n                unregisterPage\n            })\n    }[\"Document.Document.useMemo[childContext]\"], [\n        imageResourcesPath,\n        onItemClick,\n        pdf,\n        registerPage,\n        renderMode,\n        rotate,\n        unregisterPage\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Document.Document.useMemo[eventProps]\": ()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(otherProps, {\n                \"Document.Document.useMemo[eventProps]\": ()=>pdf\n            }[\"Document.Document.useMemo[eventProps]\"])\n    }[\"Document.Document.useMemo[eventProps]\"], // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        pdf\n    ]);\n    function renderChildren() {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, {\n            value: childContext,\n            children: children\n        });\n    }\n    function renderContent() {\n        if (!file) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"no-data\",\n                children: typeof noData === 'function' ? noData() : noData\n            });\n        }\n        if (pdf === undefined || pdf === null) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"loading\",\n                children: typeof loading === 'function' ? loading() : loading\n            });\n        }\n        if (pdf === false) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"error\",\n                children: typeof error === 'function' ? error() : error\n            });\n        }\n        return renderChildren();\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('react-pdf__Document', className),\n        // Assertion is needed for React 18 compatibility\n        ref: inputRef,\n        style: {\n            ['--scale-factor']: '1'\n        }\n    }, eventProps, {\n        children: renderContent()\n    }));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Document);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js":
/*!************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/DocumentContext.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst documentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (documentContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL0RvY3VtZW50Q29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs2REFDc0M7QUFDdEMsTUFBTUMsZ0NBQWtCRCxvREFBYUEsQ0FBQztBQUN0QyxpRUFBZUMsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxyZWFjdC1wZGZcXGRpc3RcXGVzbVxcRG9jdW1lbnRDb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5jb25zdCBkb2N1bWVudENvbnRleHQgPSBjcmVhdGVDb250ZXh0KG51bGwpO1xuZXhwb3J0IGRlZmF1bHQgZG9jdW1lbnRDb250ZXh0O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJkb2N1bWVudENvbnRleHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/LinkService.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LinkService)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst DEFAULT_LINK_REL = 'noopener noreferrer nofollow';\nclass LinkService {\n    constructor() {\n        this.externalLinkEnabled = true;\n        this.externalLinkRel = undefined;\n        this.externalLinkTarget = undefined;\n        this.isInPresentationMode = false;\n        this.pdfDocument = undefined;\n        this.pdfViewer = undefined;\n    }\n    setDocument(pdfDocument) {\n        this.pdfDocument = pdfDocument;\n    }\n    setViewer(pdfViewer) {\n        this.pdfViewer = pdfViewer;\n    }\n    setExternalLinkRel(externalLinkRel) {\n        this.externalLinkRel = externalLinkRel;\n    }\n    setExternalLinkTarget(externalLinkTarget) {\n        this.externalLinkTarget = externalLinkTarget;\n    }\n    setHistory() {\n        // Intentionally empty\n    }\n    get pagesCount() {\n        return this.pdfDocument ? this.pdfDocument.numPages : 0;\n    }\n    get page() {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        return this.pdfViewer.currentPageNumber || 0;\n    }\n    set page(value) {\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        this.pdfViewer.currentPageNumber = value;\n    }\n    get rotation() {\n        return 0;\n    }\n    set rotation(_value) {\n        // Intentionally empty\n    }\n    goToDestination(dest) {\n        return new Promise((resolve) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dest, 'Destination is not specified.');\n            if (typeof dest === 'string') {\n                this.pdfDocument.getDestination(dest).then(resolve);\n            }\n            else if (Array.isArray(dest)) {\n                resolve(dest);\n            }\n            else {\n                dest.then(resolve);\n            }\n        }).then((explicitDest) => {\n            (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(explicitDest), `\"${explicitDest}\" is not a valid destination array.`);\n            const destRef = explicitDest[0];\n            new Promise((resolve) => {\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfDocument, 'PDF document not loaded.');\n                if (destRef instanceof Object) {\n                    this.pdfDocument\n                        .getPageIndex(destRef)\n                        .then((pageIndex) => {\n                        resolve(pageIndex);\n                    })\n                        .catch(() => {\n                        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid page reference.`);\n                    });\n                }\n                else if (typeof destRef === 'number') {\n                    resolve(destRef);\n                }\n                else {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, `\"${destRef}\" is not a valid destination reference.`);\n                }\n            }).then((pageIndex) => {\n                const pageNumber = pageIndex + 1;\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n                (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n                this.pdfViewer.scrollPageIntoView({\n                    dest: explicitDest,\n                    pageIndex,\n                    pageNumber,\n                });\n            });\n        });\n    }\n    navigateTo(dest) {\n        this.goToDestination(dest);\n    }\n    goToPage(pageNumber) {\n        const pageIndex = pageNumber - 1;\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.pdfViewer, 'PDF viewer is not initialized.');\n        (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n        this.pdfViewer.scrollPageIntoView({\n            pageIndex,\n            pageNumber,\n        });\n    }\n    addLinkAttributes(link, url, newWindow) {\n        link.href = url;\n        link.rel = this.externalLinkRel || DEFAULT_LINK_REL;\n        link.target = newWindow ? '_blank' : this.externalLinkTarget || '';\n    }\n    getDestinationHash() {\n        return '#';\n    }\n    getAnchorUrl() {\n        return '#';\n    }\n    setHash() {\n        // Intentionally empty\n    }\n    executeNamedAction() {\n        // Intentionally empty\n    }\n    cachePageRef() {\n        // Intentionally empty\n    }\n    isPageVisible() {\n        return true;\n    }\n    isPageCached() {\n        return true;\n    }\n    executeSetOCGState() {\n        // Intentionally empty\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/LinkService.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Message.js":
/*!****************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Message.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\nfunction Message({ children, type }) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: `react-pdf__message react-pdf__message--${type}`, children: children });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL01lc3NhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDakMsbUJBQW1CLGdCQUFnQjtBQUNsRCxXQUFXLHNEQUFJLFVBQVUscURBQXFELEtBQUssdUJBQXVCO0FBQzFHIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxNZXNzYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNZXNzYWdlKHsgY2hpbGRyZW4sIHR5cGUgfSkge1xuICAgIHJldHVybiBfanN4KFwiZGl2XCIsIHsgY2xhc3NOYW1lOiBgcmVhY3QtcGRmX19tZXNzYWdlIHJlYWN0LXBkZl9fbWVzc2FnZS0tJHt0eXBlfWAsIGNoaWxkcmVuOiBjaGlsZHJlbiB9KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page.js":
/*!*************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var make_event_props__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-event-props */ \"(ssr)/./node_modules/make-event-props/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! merge-refs */ \"(ssr)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n/* harmony import */ var _Message_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Message.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Message.js\");\n/* harmony import */ var _Page_Canvas_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Page/Canvas.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/Canvas.js\");\n/* harmony import */ var _Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Page/TextLayer.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\");\n/* harmony import */ var _Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Page/AnnotationLayer.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useDocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst defaultScale = 1;\n/**\n * Displays a page.\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function, however some advanced functions like linking between pages inside a document may not be working correctly.\n */ function Page(props) {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { _className = 'react-pdf__Page', _enableRegisterUnregisterPage = true, canvasBackground, canvasRef, children, className, customRenderer: CustomRenderer, customTextRenderer, devicePixelRatio, error = 'Failed to load the page.', height, inputRef, loading = 'Loading page…', noData = 'No page specified.', onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, onGetTextError: onGetTextErrorProps, onGetTextSuccess: onGetTextSuccessProps, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, onRenderTextLayerError: onRenderTextLayerErrorProps, onRenderTextLayerSuccess: onRenderTextLayerSuccessProps, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf, registerPage, renderAnnotationLayer: renderAnnotationLayerProps = true, renderForms = false, renderMode = 'canvas', renderTextLayer: renderTextLayerProps = true, rotate: rotateProps, scale: scaleProps = defaultScale, unregisterPage, width } = mergedProps, otherProps = __rest(mergedProps, [\n        \"_className\",\n        \"_enableRegisterUnregisterPage\",\n        \"canvasBackground\",\n        \"canvasRef\",\n        \"children\",\n        \"className\",\n        \"customRenderer\",\n        \"customTextRenderer\",\n        \"devicePixelRatio\",\n        \"error\",\n        \"height\",\n        \"inputRef\",\n        \"loading\",\n        \"noData\",\n        \"onGetAnnotationsError\",\n        \"onGetAnnotationsSuccess\",\n        \"onGetStructTreeError\",\n        \"onGetStructTreeSuccess\",\n        \"onGetTextError\",\n        \"onGetTextSuccess\",\n        \"onLoadError\",\n        \"onLoadSuccess\",\n        \"onRenderAnnotationLayerError\",\n        \"onRenderAnnotationLayerSuccess\",\n        \"onRenderError\",\n        \"onRenderSuccess\",\n        \"onRenderTextLayerError\",\n        \"onRenderTextLayerSuccess\",\n        \"pageIndex\",\n        \"pageNumber\",\n        \"pdf\",\n        \"registerPage\",\n        \"renderAnnotationLayer\",\n        \"renderForms\",\n        \"renderMode\",\n        \"renderTextLayer\",\n        \"rotate\",\n        \"scale\",\n        \"unregisterPage\",\n        \"width\"\n    ]);\n    const [pageState, pageDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { value: page, error: pageError } = pageState;\n    const pageElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, 'Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    const pageIndex = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageNumberProps) ? pageNumberProps - 1 : pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null;\n    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndexProps) ? pageIndexProps + 1 : null;\n    const rotate = rotateProps !== null && rotateProps !== void 0 ? rotateProps : page ? page.rotate : null;\n    const scale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Page.useMemo[scale]\": ()=>{\n            if (!page) {\n                return null;\n            }\n            // Be default, we'll render page at 100% * scale width.\n            let pageScale = 1;\n            // Passing scale explicitly null would cause the page not to render\n            const scaleWithDefault = scaleProps !== null && scaleProps !== void 0 ? scaleProps : defaultScale;\n            // If width/height is defined, calculate the scale of the page so it could be of desired width.\n            if (width || height) {\n                const viewport = page.getViewport({\n                    scale: 1,\n                    rotation: rotate\n                });\n                if (width) {\n                    pageScale = width / viewport.width;\n                } else if (height) {\n                    pageScale = height / viewport.height;\n                }\n            }\n            return scaleWithDefault * pageScale;\n        }\n    }[\"Page.useMemo[scale]\"], [\n        height,\n        page,\n        rotate,\n        scaleProps,\n        width\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function hook() {\n        return ({\n            \"Page.useEffect.hook\": ()=>{\n                if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex)) {\n                    // Impossible, but TypeScript doesn't know that\n                    return;\n                }\n                if (_enableRegisterUnregisterPage && unregisterPage) {\n                    unregisterPage(pageIndex);\n                }\n            }\n        })[\"Page.useEffect.hook\"];\n    }, [\n        _enableRegisterUnregisterPage,\n        pdf,\n        pageIndex,\n        unregisterPage\n    ]);\n    /**\n     * Called when a page is loaded successfully\n     */ function onLoadSuccess() {\n        if (onLoadSuccessProps) {\n            if (!page || !scale) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            onLoadSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.makePageCallback)(page, scale));\n        }\n        if (_enableRegisterUnregisterPage && registerPage) {\n            if (!(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex) || !pageElement.current) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            registerPage(pageIndex, pageElement.current);\n        }\n    }\n    /**\n     * Called when a page failed to load\n     */ function onLoadError() {\n        if (!pageError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, pageError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pageError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf and pageIndex change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetPage() {\n        pageDispatch({\n            type: 'RESET'\n        });\n    }, [\n        pageDispatch,\n        pdf,\n        pageIndex\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadPage() {\n        if (!pdf || !pageNumber) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pdf.getPage(pageNumber));\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"Page.useEffect.loadPage\": (nextPage)=>{\n                pageDispatch({\n                    type: 'RESOLVE',\n                    value: nextPage\n                });\n            }\n        }[\"Page.useEffect.loadPage\"]).catch({\n            \"Page.useEffect.loadPage\": (error)=>{\n                pageDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"Page.useEffect.loadPage\"]);\n        return ({\n            \"Page.useEffect.loadPage\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask)\n        })[\"Page.useEffect.loadPage\"];\n    }, [\n        pageDispatch,\n        pdf,\n        pageNumber\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            if (page === undefined) {\n                return;\n            }\n            if (page === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"Page.useEffect\"], [\n        page,\n        scale\n    ]);\n    const childContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Page.useMemo[childContext]\": ()=>// Technically there cannot be page without pageIndex, pageNumber, rotate and scale, but TypeScript doesn't know that\n            page && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(pageIndex) && pageNumber && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(rotate) && (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.isProvided)(scale) ? {\n                _className,\n                canvasBackground,\n                customTextRenderer,\n                devicePixelRatio,\n                onGetAnnotationsError: onGetAnnotationsErrorProps,\n                onGetAnnotationsSuccess: onGetAnnotationsSuccessProps,\n                onGetStructTreeError: onGetStructTreeErrorProps,\n                onGetStructTreeSuccess: onGetStructTreeSuccessProps,\n                onGetTextError: onGetTextErrorProps,\n                onGetTextSuccess: onGetTextSuccessProps,\n                onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps,\n                onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps,\n                onRenderError: onRenderErrorProps,\n                onRenderSuccess: onRenderSuccessProps,\n                onRenderTextLayerError: onRenderTextLayerErrorProps,\n                onRenderTextLayerSuccess: onRenderTextLayerSuccessProps,\n                page,\n                pageIndex,\n                pageNumber,\n                renderForms,\n                renderTextLayer: renderTextLayerProps,\n                rotate,\n                scale\n            } : null\n    }[\"Page.useMemo[childContext]\"], [\n        _className,\n        canvasBackground,\n        customTextRenderer,\n        devicePixelRatio,\n        onGetAnnotationsErrorProps,\n        onGetAnnotationsSuccessProps,\n        onGetStructTreeErrorProps,\n        onGetStructTreeSuccessProps,\n        onGetTextErrorProps,\n        onGetTextSuccessProps,\n        onRenderAnnotationLayerErrorProps,\n        onRenderAnnotationLayerSuccessProps,\n        onRenderErrorProps,\n        onRenderSuccessProps,\n        onRenderTextLayerErrorProps,\n        onRenderTextLayerSuccessProps,\n        page,\n        pageIndex,\n        pageNumber,\n        renderForms,\n        renderTextLayerProps,\n        rotate,\n        scale\n    ]);\n    const eventProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Page.useMemo[eventProps]\": ()=>(0,make_event_props__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(otherProps, {\n                \"Page.useMemo[eventProps]\": ()=>page ? scale ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.makePageCallback)(page, scale) : undefined : page\n            }[\"Page.useMemo[eventProps]\"])\n    }[\"Page.useMemo[eventProps]\"], // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [\n        otherProps,\n        page,\n        scale\n    ]);\n    const pageKey = `${pageIndex}@${scale}/${rotate}`;\n    function renderMainLayer() {\n        switch(renderMode){\n            case 'custom':\n                {\n                    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(CustomRenderer, `renderMode was set to \"custom\", but no customRenderer was passed.`);\n                    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CustomRenderer, {}, `${pageKey}_custom`);\n                }\n            case 'none':\n                return null;\n            case 'canvas':\n            default:\n                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_Canvas_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    canvasRef: canvasRef\n                }, `${pageKey}_canvas`);\n        }\n    }\n    function renderTextLayer() {\n        if (!renderTextLayerProps) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_TextLayer_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, `${pageKey}_text`);\n    }\n    function renderAnnotationLayer() {\n        if (!renderAnnotationLayerProps) {\n            return null;\n        }\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Page_AnnotationLayer_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, `${pageKey}_annotations`);\n    }\n    function renderChildren() {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_PageContext_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"].Provider, {\n            value: childContext,\n            children: [\n                renderMainLayer(),\n                renderTextLayer(),\n                renderAnnotationLayer(),\n                children\n            ]\n        });\n    }\n    function renderContent() {\n        if (!pageNumber) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"no-data\",\n                children: typeof noData === 'function' ? noData() : noData\n            });\n        }\n        if (pdf === null || page === undefined || page === null) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"loading\",\n                children: typeof loading === 'function' ? loading() : loading\n            });\n        }\n        if (pdf === false || page === false) {\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Message_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                type: \"error\",\n                children: typeof error === 'function' ? error() : error\n            });\n        }\n        return renderChildren();\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_className, className),\n        \"data-page-number\": pageNumber,\n        // Assertion is needed for React 18 compatibility\n        ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(inputRef, pageElement),\n        style: {\n            ['--scale-factor']: `${scale}`,\n            backgroundColor: canvasBackground || 'white',\n            position: 'relative',\n            minWidth: 'min-content',\n            minHeight: 'min-content'\n        }\n    }, eventProps, {\n        children: renderContent()\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PageContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nconst pageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pageContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1BhZ2VDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OzZEQUNzQztBQUN0QyxNQUFNQyw0QkFBY0Qsb0RBQWFBLENBQUM7QUFDbEMsaUVBQWVDLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXFBhZ2VDb250ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5jb25zdCBwYWdlQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQobnVsbCk7XG5leHBvcnQgZGVmYXVsdCBwYWdlQ29udGV4dDtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwicGFnZUNvbnRleHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnnotationLayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/useDocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction AnnotationLayer() {\n    const documentContext = (0,_shared_hooks_useDocumentContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, documentContext), pageContext);\n    const { imageResourcesPath, linkService, onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, page, pdf, renderForms, rotate, scale = 1 } = mergedProps;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pdf, 'Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(page, 'Attempted to load page annotations, but no page was specified.');\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(linkService, 'Attempted to load page annotations, but no linkService was specified.');\n    const [annotationsState, annotationsDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { value: annotations, error: annotationsError } = annotationsState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_4__(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-annotation-layer'), 10) === 1, 'AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations');\n    function onLoadSuccess() {\n        if (!annotations) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetAnnotationsSuccessProps) {\n            onGetAnnotationsSuccessProps(annotations);\n        }\n    }\n    function onLoadError() {\n        if (!annotationsError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, annotationsError.toString());\n        if (onGetAnnotationsErrorProps) {\n            onGetAnnotationsErrorProps(annotationsError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetAnnotations() {\n        annotationsDispatch({\n            type: 'RESET'\n        });\n    }, [\n        annotationsDispatch,\n        page\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadAnnotations() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(page.getAnnotations());\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"AnnotationLayer.useEffect.loadAnnotations\": (nextAnnotations)=>{\n                annotationsDispatch({\n                    type: 'RESOLVE',\n                    value: nextAnnotations\n                });\n            }\n        }[\"AnnotationLayer.useEffect.loadAnnotations\"]).catch({\n            \"AnnotationLayer.useEffect.loadAnnotations\": (error)=>{\n                annotationsDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"AnnotationLayer.useEffect.loadAnnotations\"]);\n        return ({\n            \"AnnotationLayer.useEffect.loadAnnotations\": ()=>{\n                (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_10__.cancelRunningTask)(runningTask);\n            }\n        })[\"AnnotationLayer.useEffect.loadAnnotations\"];\n    }, [\n        annotationsDispatch,\n        page\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnnotationLayer.useEffect\": ()=>{\n            if (annotations === undefined) {\n                return;\n            }\n            if (annotations === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"AnnotationLayer.useEffect\"], [\n        annotations\n    ]);\n    function onRenderSuccess() {\n        if (onRenderAnnotationLayerSuccessProps) {\n            onRenderAnnotationLayerSuccessProps();\n        }\n    }\n    function onRenderError(error) {\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, `${error}`);\n        if (onRenderAnnotationLayerErrorProps) {\n            onRenderAnnotationLayerErrorProps(error);\n        }\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnnotationLayer.useMemo[viewport]\": ()=>page.getViewport({\n                scale,\n                rotation: rotate\n            })\n    }[\"AnnotationLayer.useMemo[viewport]\"], [\n        page,\n        rotate,\n        scale\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function renderAnnotationLayer() {\n        if (!pdf || !page || !linkService || !annotations) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        const clonedViewport = viewport.clone({\n            dontFlip: true\n        });\n        const annotationLayerParameters = {\n            accessibilityManager: null,\n            annotationCanvasMap: null,\n            annotationEditorUIManager: null,\n            div: layer,\n            l10n: null,\n            page,\n            structTreeLayer: null,\n            viewport: clonedViewport\n        };\n        const renderParameters = {\n            annotations,\n            annotationStorage: pdf.annotationStorage,\n            div: layer,\n            imageResourcesPath,\n            linkService,\n            page,\n            renderForms,\n            viewport: clonedViewport\n        };\n        layer.innerHTML = '';\n        try {\n            new pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__.AnnotationLayer(annotationLayerParameters).render(renderParameters);\n            // Intentional immediate callback\n            onRenderSuccess();\n        } catch (error) {\n            onRenderError(error);\n        }\n        return ({\n            \"AnnotationLayer.useEffect.renderAnnotationLayer\": ()=>{\n            // TODO: Cancel running task?\n            }\n        })[\"AnnotationLayer.useEffect.renderAnnotationLayer\"];\n    }, [\n        annotations,\n        imageResourcesPath,\n        linkService,\n        page,\n        pdf,\n        renderForms,\n        viewport\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('react-pdf__Page__annotations', 'annotationLayer'),\n        ref: layerElement\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/Canvas.js":
/*!********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/Canvas.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Canvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var merge_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! merge-refs */ \"(ssr)/./node_modules/merge-refs/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _StructTree_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../StructTree.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst ANNOTATION_MODE = pdfjs_dist__WEBPACK_IMPORTED_MODULE_4__.AnnotationMode;\nfunction Canvas(props) {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, pageContext), props);\n    const { _className, canvasBackground, devicePixelRatio = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.getDevicePixelRatio)(), onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, page, renderForms, renderTextLayer, rotate, scale } = mergedProps;\n    const { canvasRef } = props;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(page, 'Attempted to render page canvas, but no page was specified.');\n    const canvasElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /**\n     * Called when a page is rendered successfully.\n     */ function onRenderSuccess() {\n        if (!page) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onRenderSuccessProps) {\n            onRenderSuccessProps((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.makePageCallback)(page, scale));\n        }\n    }\n    /**\n     * Called when a page fails to render.\n     */ function onRenderError(error) {\n        if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.isCancelException)(error)) {\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, error.toString());\n        if (onRenderErrorProps) {\n            onRenderErrorProps(error);\n        }\n    }\n    const renderViewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Canvas.useMemo[renderViewport]\": ()=>page.getViewport({\n                scale: scale * devicePixelRatio,\n                rotation: rotate\n            })\n    }[\"Canvas.useMemo[renderViewport]\"], [\n        devicePixelRatio,\n        page,\n        rotate,\n        scale\n    ]);\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Canvas.useMemo[viewport]\": ()=>page.getViewport({\n                scale,\n                rotation: rotate\n            })\n    }[\"Canvas.useMemo[viewport]\"], [\n        page,\n        rotate,\n        scale\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function drawPageOnCanvas() {\n        if (!page) {\n            return;\n        }\n        // Ensures the canvas will be re-rendered from scratch. Otherwise all form data will stay.\n        page.cleanup();\n        const { current: canvas } = canvasElement;\n        if (!canvas) {\n            return;\n        }\n        canvas.width = renderViewport.width;\n        canvas.height = renderViewport.height;\n        canvas.style.width = `${Math.floor(viewport.width)}px`;\n        canvas.style.height = `${Math.floor(viewport.height)}px`;\n        canvas.style.visibility = 'hidden';\n        const renderContext = {\n            annotationMode: renderForms ? ANNOTATION_MODE.ENABLE_FORMS : ANNOTATION_MODE.ENABLE,\n            canvasContext: canvas.getContext('2d', {\n                alpha: false\n            }),\n            viewport: renderViewport\n        };\n        if (canvasBackground) {\n            renderContext.background = canvasBackground;\n        }\n        const cancellable = page.render(renderContext);\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"Canvas.useEffect.drawPageOnCanvas\": ()=>{\n                canvas.style.visibility = '';\n                onRenderSuccess();\n            }\n        }[\"Canvas.useEffect.drawPageOnCanvas\"]).catch(onRenderError);\n        return ({\n            \"Canvas.useEffect.drawPageOnCanvas\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_6__.cancelRunningTask)(runningTask)\n        })[\"Canvas.useEffect.drawPageOnCanvas\"];\n    }, [\n        canvasBackground,\n        page,\n        renderForms,\n        renderViewport,\n        viewport\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Canvas.useCallback[cleanup]\": ()=>{\n            const { current: canvas } = canvasElement;\n            /**\n         * Zeroing the width and height cause most browsers to release graphics\n         * resources immediately, which can greatly reduce memory consumption.\n         */ if (canvas) {\n                canvas.width = 0;\n                canvas.height = 0;\n            }\n        }\n    }[\"Canvas.useCallback[cleanup]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Canvas.useEffect\": ()=>cleanup\n    }[\"Canvas.useEffect\"], [\n        cleanup\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"canvas\", {\n        className: `${_className}__canvas`,\n        dir: \"ltr\",\n        ref: (0,merge_refs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(canvasRef, canvasElement),\n        style: {\n            display: 'block',\n            userSelect: 'none'\n        },\n        children: renderTextLayer ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_StructTree_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}) : null\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/Canvas.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/Page/TextLayer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TextLayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.mjs\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction isTextItem(item) {\n    return 'str' in item;\n}\nfunction TextLayer() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { customTextRenderer, onGetTextError, onGetTextSuccess, onRenderTextLayerError, onRenderTextLayerSuccess, page, pageIndex, pageNumber, rotate, scale } = pageContext;\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(page, 'Attempted to load page text content, but no page was specified.');\n    const [textContentState, textContentDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { value: textContent, error: textContentError } = textContentState;\n    const layerElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    warning__WEBPACK_IMPORTED_MODULE_4__(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-text-layer'), 10) === 1, 'TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer');\n    /**\n     * Called when a page text content is read successfully\n     */ function onLoadSuccess() {\n        if (!textContent) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetTextSuccess) {\n            onGetTextSuccess(textContent);\n        }\n    }\n    /**\n     * Called when a page text content failed to read successfully\n     */ function onLoadError() {\n        if (!textContentError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_4__(false, textContentError.toString());\n        if (onGetTextError) {\n            onGetTextError(textContentError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetTextContent() {\n        textContentDispatch({\n            type: 'RESET'\n        });\n    }, [\n        page,\n        textContentDispatch\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadTextContent() {\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(page.getTextContent());\n        const runningTask = cancellable;\n        cancellable.promise.then({\n            \"TextLayer.useEffect.loadTextContent\": (nextTextContent)=>{\n                textContentDispatch({\n                    type: 'RESOLVE',\n                    value: nextTextContent\n                });\n            }\n        }[\"TextLayer.useEffect.loadTextContent\"]).catch({\n            \"TextLayer.useEffect.loadTextContent\": (error)=>{\n                textContentDispatch({\n                    type: 'REJECT',\n                    error\n                });\n            }\n        }[\"TextLayer.useEffect.loadTextContent\"]);\n        return ({\n            \"TextLayer.useEffect.loadTextContent\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(runningTask)\n        })[\"TextLayer.useEffect.loadTextContent\"];\n    }, [\n        page,\n        textContentDispatch\n    ]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextLayer.useEffect\": ()=>{\n            if (textContent === undefined) {\n                return;\n            }\n            if (textContent === false) {\n                onLoadError();\n                return;\n            }\n            onLoadSuccess();\n        }\n    }[\"TextLayer.useEffect\"], [\n        textContent\n    ]);\n    /**\n     * Called when a text layer is rendered successfully\n     */ const onRenderSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextLayer.useCallback[onRenderSuccess]\": ()=>{\n            if (onRenderTextLayerSuccess) {\n                onRenderTextLayerSuccess();\n            }\n        }\n    }[\"TextLayer.useCallback[onRenderSuccess]\"], [\n        onRenderTextLayerSuccess\n    ]);\n    /**\n     * Called when a text layer failed to render successfully\n     */ const onRenderError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TextLayer.useCallback[onRenderError]\": (error)=>{\n            warning__WEBPACK_IMPORTED_MODULE_4__(false, error.toString());\n            if (onRenderTextLayerError) {\n                onRenderTextLayerError(error);\n            }\n        }\n    }[\"TextLayer.useCallback[onRenderError]\"], [\n        onRenderTextLayerError\n    ]);\n    function onMouseDown() {\n        const layer = layerElement.current;\n        if (!layer) {\n            return;\n        }\n        layer.classList.add('selecting');\n    }\n    function onMouseUp() {\n        const layer = layerElement.current;\n        if (!layer) {\n            return;\n        }\n        layer.classList.remove('selecting');\n    }\n    const viewport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TextLayer.useMemo[viewport]\": ()=>page.getViewport({\n                scale,\n                rotation: rotate\n            })\n    }[\"TextLayer.useMemo[viewport]\"], [\n        page,\n        rotate,\n        scale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(function renderTextLayer() {\n        if (!page || !textContent) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        layer.innerHTML = '';\n        const textContentSource = page.streamTextContent({\n            includeMarkedContent: true\n        });\n        const parameters = {\n            container: layer,\n            textContentSource,\n            viewport\n        };\n        const cancellable = new pdfjs_dist__WEBPACK_IMPORTED_MODULE_5__.TextLayer(parameters);\n        const runningTask = cancellable;\n        cancellable.render().then({\n            \"TextLayer.useLayoutEffect.renderTextLayer\": ()=>{\n                const end = document.createElement('div');\n                end.className = 'endOfContent';\n                layer.append(end);\n                const layerChildren = layer.querySelectorAll('[role=\"presentation\"]');\n                if (customTextRenderer) {\n                    let index = 0;\n                    textContent.items.forEach({\n                        \"TextLayer.useLayoutEffect.renderTextLayer\": (item, itemIndex)=>{\n                            if (!isTextItem(item)) {\n                                return;\n                            }\n                            const child = layerChildren[index];\n                            if (!child) {\n                                return;\n                            }\n                            const content = customTextRenderer(Object.assign({\n                                pageIndex,\n                                pageNumber,\n                                itemIndex\n                            }, item));\n                            child.innerHTML = content;\n                            index += item.str && item.hasEOL ? 2 : 1;\n                        }\n                    }[\"TextLayer.useLayoutEffect.renderTextLayer\"]);\n                }\n                // Intentional immediate callback\n                onRenderSuccess();\n            }\n        }[\"TextLayer.useLayoutEffect.renderTextLayer\"]).catch(onRenderError);\n        return ({\n            \"TextLayer.useLayoutEffect.renderTextLayer\": ()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_9__.cancelRunningTask)(runningTask)\n        })[\"TextLayer.useLayoutEffect.renderTextLayer\"];\n    }, [\n        customTextRenderer,\n        onRenderError,\n        onRenderSuccess,\n        page,\n        pageIndex,\n        pageNumber,\n        textContent,\n        viewport\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])('react-pdf__Page__textContent', 'textLayer'),\n        onMouseUp: onMouseUp,\n        onMouseDown: onMouseDown,\n        ref: layerElement\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/PasswordResponses.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// As defined in https://github.com/mozilla/pdf.js/blob/d9fac3459609a807be6506fb3441b5da4b154d14/src/shared/util.js#L371-L374\nconst PasswordResponses = {\n    NEED_PASSWORD: 1,\n    INCORRECT_PASSWORD: 2,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PasswordResponses);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1Bhc3N3b3JkUmVzcG9uc2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsaUJBQWlCLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXFBhc3N3b3JkUmVzcG9uc2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFzIGRlZmluZWQgaW4gaHR0cHM6Ly9naXRodWIuY29tL21vemlsbGEvcGRmLmpzL2Jsb2IvZDlmYWMzNDU5NjA5YTgwN2JlNjUwNmZiMzQ0MWI1ZGE0YjE1NGQxNC9zcmMvc2hhcmVkL3V0aWwuanMjTDM3MS1MMzc0XG5jb25zdCBQYXNzd29yZFJlc3BvbnNlcyA9IHtcbiAgICBORUVEX1BBU1NXT1JEOiAxLFxuICAgIElOQ09SUkVDVF9QQVNTV09SRDogMixcbn07XG5leHBvcnQgZGVmYXVsdCBQYXNzd29yZFJlc3BvbnNlcztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/PasswordResponses.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTree.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StructTree)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! make-cancellable-promise */ \"(ssr)/./node_modules/make-cancellable-promise/dist/esm/index.js\");\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var _StructTreeItem_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StructTreeItem.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\");\n/* harmony import */ var _shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared/hooks/usePageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\");\n/* harmony import */ var _shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/hooks/useResolver.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./shared/utils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\");\n\n\n\n\n\n\n\n\n\nfunction StructTree() {\n    const pageContext = (0,_shared_hooks_usePageContext_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(pageContext, 'Unable to find Page context.');\n    const { onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, } = pageContext;\n    const [structTreeState, structTreeDispatch] = (0,_shared_hooks_useResolver_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const { value: structTree, error: structTreeError } = structTreeState;\n    const { customTextRenderer, page } = pageContext;\n    function onLoadSuccess() {\n        if (!structTree) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetStructTreeSuccessProps) {\n            onGetStructTreeSuccessProps(structTree);\n        }\n    }\n    function onLoadError() {\n        if (!structTreeError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning__WEBPACK_IMPORTED_MODULE_3__(false, structTreeError.toString());\n        if (onGetStructTreeErrorProps) {\n            onGetStructTreeErrorProps(structTreeError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function resetStructTree() {\n        structTreeDispatch({ type: 'RESET' });\n    }, [structTreeDispatch, page]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function loadStructTree() {\n        if (customTextRenderer) {\n            // TODO: Document why this is necessary\n            return;\n        }\n        if (!page) {\n            return;\n        }\n        const cancellable = (0,make_cancellable_promise__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(page.getStructTree());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextStructTree) => {\n            structTreeDispatch({ type: 'RESOLVE', value: nextStructTree });\n        })\n            .catch((error) => {\n            structTreeDispatch({ type: 'REJECT', error });\n        });\n        return () => (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_7__.cancelRunningTask)(runningTask);\n    }, [customTextRenderer, page, structTreeDispatch]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        if (structTree === undefined) {\n            return;\n        }\n        if (structTree === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [structTree]);\n    if (!structTree) {\n        return null;\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_StructTreeItem_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"], { className: \"react-pdf__Page__structTree structTree\", node: structTree });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1N0cnVjdFRyZWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFnRDtBQUNkO0FBQ3FCO0FBQ2hCO0FBQ1Q7QUFDbUI7QUFDYTtBQUNOO0FBQ0Y7QUFDdkM7QUFDZix3QkFBd0IsMkVBQWM7QUFDdEMsSUFBSSwwREFBUztBQUNiLFlBQVksd0dBQXdHO0FBQ3BILGtEQUFrRCx3RUFBVztBQUM3RCxZQUFZLDRDQUE0QztBQUN4RCxZQUFZLDJCQUEyQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxvQ0FBTztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdEQUFTO0FBQ2IsNkJBQTZCLGVBQWU7QUFDNUMsS0FBSztBQUNMLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixvRUFBZTtBQUMzQztBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsd0NBQXdDO0FBQ3pFLFNBQVM7QUFDVDtBQUNBLGlDQUFpQyx1QkFBdUI7QUFDeEQsU0FBUztBQUNULHFCQUFxQixtRUFBaUI7QUFDdEMsS0FBSztBQUNMO0FBQ0EsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsV0FBVyxzREFBSSxDQUFDLDBEQUFjLElBQUksdUVBQXVFO0FBQ3pHIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxTdHJ1Y3RUcmVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgbWFrZUNhbmNlbGxhYmxlIGZyb20gJ21ha2UtY2FuY2VsbGFibGUtcHJvbWlzZSc7XG5pbXBvcnQgaW52YXJpYW50IGZyb20gJ3RpbnktaW52YXJpYW50JztcbmltcG9ydCB3YXJuaW5nIGZyb20gJ3dhcm5pbmcnO1xuaW1wb3J0IFN0cnVjdFRyZWVJdGVtIGZyb20gJy4vU3RydWN0VHJlZUl0ZW0uanMnO1xuaW1wb3J0IHVzZVBhZ2VDb250ZXh0IGZyb20gJy4vc2hhcmVkL2hvb2tzL3VzZVBhZ2VDb250ZXh0LmpzJztcbmltcG9ydCB1c2VSZXNvbHZlciBmcm9tICcuL3NoYXJlZC9ob29rcy91c2VSZXNvbHZlci5qcyc7XG5pbXBvcnQgeyBjYW5jZWxSdW5uaW5nVGFzayB9IGZyb20gJy4vc2hhcmVkL3V0aWxzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0cnVjdFRyZWUoKSB7XG4gICAgY29uc3QgcGFnZUNvbnRleHQgPSB1c2VQYWdlQ29udGV4dCgpO1xuICAgIGludmFyaWFudChwYWdlQ29udGV4dCwgJ1VuYWJsZSB0byBmaW5kIFBhZ2UgY29udGV4dC4nKTtcbiAgICBjb25zdCB7IG9uR2V0U3RydWN0VHJlZUVycm9yOiBvbkdldFN0cnVjdFRyZWVFcnJvclByb3BzLCBvbkdldFN0cnVjdFRyZWVTdWNjZXNzOiBvbkdldFN0cnVjdFRyZWVTdWNjZXNzUHJvcHMsIH0gPSBwYWdlQ29udGV4dDtcbiAgICBjb25zdCBbc3RydWN0VHJlZVN0YXRlLCBzdHJ1Y3RUcmVlRGlzcGF0Y2hdID0gdXNlUmVzb2x2ZXIoKTtcbiAgICBjb25zdCB7IHZhbHVlOiBzdHJ1Y3RUcmVlLCBlcnJvcjogc3RydWN0VHJlZUVycm9yIH0gPSBzdHJ1Y3RUcmVlU3RhdGU7XG4gICAgY29uc3QgeyBjdXN0b21UZXh0UmVuZGVyZXIsIHBhZ2UgfSA9IHBhZ2VDb250ZXh0O1xuICAgIGZ1bmN0aW9uIG9uTG9hZFN1Y2Nlc3MoKSB7XG4gICAgICAgIGlmICghc3RydWN0VHJlZSkge1xuICAgICAgICAgICAgLy8gSW1wb3NzaWJsZSwgYnV0IFR5cGVTY3JpcHQgZG9lc24ndCBrbm93IHRoYXRcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAob25HZXRTdHJ1Y3RUcmVlU3VjY2Vzc1Byb3BzKSB7XG4gICAgICAgICAgICBvbkdldFN0cnVjdFRyZWVTdWNjZXNzUHJvcHMoc3RydWN0VHJlZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gb25Mb2FkRXJyb3IoKSB7XG4gICAgICAgIGlmICghc3RydWN0VHJlZUVycm9yKSB7XG4gICAgICAgICAgICAvLyBJbXBvc3NpYmxlLCBidXQgVHlwZVNjcmlwdCBkb2Vzbid0IGtub3cgdGhhdFxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHdhcm5pbmcoZmFsc2UsIHN0cnVjdFRyZWVFcnJvci50b1N0cmluZygpKTtcbiAgICAgICAgaWYgKG9uR2V0U3RydWN0VHJlZUVycm9yUHJvcHMpIHtcbiAgICAgICAgICAgIG9uR2V0U3RydWN0VHJlZUVycm9yUHJvcHMoc3RydWN0VHJlZUVycm9yKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyBiaW9tZS1pZ25vcmUgbGludC9jb3JyZWN0bmVzcy91c2VFeGhhdXN0aXZlRGVwZW5kZW5jaWVzOiB1c2VFZmZlY3QgaW50ZW50aW9uYWxseSB0cmlnZ2VyZWQgb24gcGFnZSBjaGFuZ2VcbiAgICB1c2VFZmZlY3QoZnVuY3Rpb24gcmVzZXRTdHJ1Y3RUcmVlKCkge1xuICAgICAgICBzdHJ1Y3RUcmVlRGlzcGF0Y2goeyB0eXBlOiAnUkVTRVQnIH0pO1xuICAgIH0sIFtzdHJ1Y3RUcmVlRGlzcGF0Y2gsIHBhZ2VdKTtcbiAgICB1c2VFZmZlY3QoZnVuY3Rpb24gbG9hZFN0cnVjdFRyZWUoKSB7XG4gICAgICAgIGlmIChjdXN0b21UZXh0UmVuZGVyZXIpIHtcbiAgICAgICAgICAgIC8vIFRPRE86IERvY3VtZW50IHdoeSB0aGlzIGlzIG5lY2Vzc2FyeVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICghcGFnZSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNhbmNlbGxhYmxlID0gbWFrZUNhbmNlbGxhYmxlKHBhZ2UuZ2V0U3RydWN0VHJlZSgpKTtcbiAgICAgICAgY29uc3QgcnVubmluZ1Rhc2sgPSBjYW5jZWxsYWJsZTtcbiAgICAgICAgY2FuY2VsbGFibGUucHJvbWlzZVxuICAgICAgICAgICAgLnRoZW4oKG5leHRTdHJ1Y3RUcmVlKSA9PiB7XG4gICAgICAgICAgICBzdHJ1Y3RUcmVlRGlzcGF0Y2goeyB0eXBlOiAnUkVTT0xWRScsIHZhbHVlOiBuZXh0U3RydWN0VHJlZSB9KTtcbiAgICAgICAgfSlcbiAgICAgICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgICAgIHN0cnVjdFRyZWVEaXNwYXRjaCh7IHR5cGU6ICdSRUpFQ1QnLCBlcnJvciB9KTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiAoKSA9PiBjYW5jZWxSdW5uaW5nVGFzayhydW5uaW5nVGFzayk7XG4gICAgfSwgW2N1c3RvbVRleHRSZW5kZXJlciwgcGFnZSwgc3RydWN0VHJlZURpc3BhdGNoXSk7XG4gICAgLy8gYmlvbWUtaWdub3JlIGxpbnQvY29ycmVjdG5lc3MvdXNlRXhoYXVzdGl2ZURlcGVuZGVuY2llczogT21taXR0ZWQgY2FsbGJhY2tzIHNvIHRoZXkgYXJlIG5vdCBjYWxsZWQgZXZlcnkgdGltZSB0aGV5IGNoYW5nZVxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChzdHJ1Y3RUcmVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc3RydWN0VHJlZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIG9uTG9hZEVycm9yKCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgb25Mb2FkU3VjY2VzcygpO1xuICAgIH0sIFtzdHJ1Y3RUcmVlXSk7XG4gICAgaWYgKCFzdHJ1Y3RUcmVlKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gX2pzeChTdHJ1Y3RUcmVlSXRlbSwgeyBjbGFzc05hbWU6IFwicmVhY3QtcGRmX19QYWdlX19zdHJ1Y3RUcmVlIHN0cnVjdFRyZWVcIiwgbm9kZTogc3RydWN0VHJlZSB9KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/StructTree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/StructTreeItem.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StructTreeItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/structTreeUtils.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\");\n\n\n\nfunction StructTreeItem({ className, node, }) {\n    const attributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => (0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.getAttributes)(node), [node]);\n    const children = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n        if (!(0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isStructTreeNode)(node)) {\n            return null;\n        }\n        if ((0,_shared_structTreeUtils_js__WEBPACK_IMPORTED_MODULE_2__.isStructTreeNodeWithOnlyContentChild)(node)) {\n            return null;\n        }\n        return node.children.map((child, index) => {\n            return (\n            // biome-ignore lint/suspicious/noArrayIndexKey: index is stable here\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(StructTreeItem, { node: child }, index));\n        });\n    }, [node]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", Object.assign({ className: className }, attributes, { children: children })));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL1N0cnVjdFRyZWVJdGVtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0Q7QUFDaEI7QUFDcUY7QUFDdEcsMEJBQTBCLGtCQUFrQjtBQUMzRCx1QkFBdUIsOENBQU8sT0FBTyx5RUFBYTtBQUNsRCxxQkFBcUIsOENBQU87QUFDNUIsYUFBYSw0RUFBZ0I7QUFDN0I7QUFDQTtBQUNBLFlBQVksZ0dBQW9DO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFJLG1CQUFtQixhQUFhO0FBQ2hELFNBQVM7QUFDVCxLQUFLO0FBQ0wsWUFBWSxzREFBSSx5QkFBeUIsc0JBQXNCLGdCQUFnQixvQkFBb0I7QUFDbkciLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXFN0cnVjdFRyZWVJdGVtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0QXR0cmlidXRlcywgaXNTdHJ1Y3RUcmVlTm9kZSwgaXNTdHJ1Y3RUcmVlTm9kZVdpdGhPbmx5Q29udGVudENoaWxkLCB9IGZyb20gJy4vc2hhcmVkL3N0cnVjdFRyZWVVdGlscy5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTdHJ1Y3RUcmVlSXRlbSh7IGNsYXNzTmFtZSwgbm9kZSwgfSkge1xuICAgIGNvbnN0IGF0dHJpYnV0ZXMgPSB1c2VNZW1vKCgpID0+IGdldEF0dHJpYnV0ZXMobm9kZSksIFtub2RlXSk7XG4gICAgY29uc3QgY2hpbGRyZW4gPSB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgaWYgKCFpc1N0cnVjdFRyZWVOb2RlKG5vZGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNTdHJ1Y3RUcmVlTm9kZVdpdGhPbmx5Q29udGVudENoaWxkKG5vZGUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbm9kZS5jaGlsZHJlbi5tYXAoKGNoaWxkLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIC8vIGJpb21lLWlnbm9yZSBsaW50L3N1c3BpY2lvdXMvbm9BcnJheUluZGV4S2V5OiBpbmRleCBpcyBzdGFibGUgaGVyZVxuICAgICAgICAgICAgX2pzeChTdHJ1Y3RUcmVlSXRlbSwgeyBub2RlOiBjaGlsZCB9LCBpbmRleCkpO1xuICAgICAgICB9KTtcbiAgICB9LCBbbm9kZV0pO1xuICAgIHJldHVybiAoX2pzeChcInNwYW5cIiwgT2JqZWN0LmFzc2lnbih7IGNsYXNzTmFtZTogY2xhc3NOYW1lIH0sIGF0dHJpYnV0ZXMsIHsgY2hpbGRyZW46IGNoaWxkcmVuIH0pKSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/StructTreeItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADING_PATTERN: () => (/* binding */ HEADING_PATTERN),\n/* harmony export */   PDF_ROLE_TO_HTML_ROLE: () => (/* binding */ PDF_ROLE_TO_HTML_ROLE)\n/* harmony export */ });\n// From pdfjs-dist/lib/web/struct_tree_layer_builder.js\nconst PDF_ROLE_TO_HTML_ROLE = {\n    // Document level structure types\n    Document: null, // There's a \"document\" role, but it doesn't make sense here.\n    DocumentFragment: null,\n    // Grouping level structure types\n    Part: 'group',\n    Sect: 'group', // XXX: There's a \"section\" role, but it's abstract.\n    Div: 'group',\n    Aside: 'note',\n    NonStruct: 'none',\n    // Block level structure types\n    P: null,\n    // H<n>,\n    H: 'heading',\n    Title: null,\n    FENote: 'note',\n    // Sub-block level structure type\n    Sub: 'group',\n    // General inline level structure types\n    Lbl: null,\n    Span: null,\n    Em: null,\n    Strong: null,\n    Link: 'link',\n    Annot: 'note',\n    Form: 'form',\n    // Ruby and Warichu structure types\n    Ruby: null,\n    RB: null,\n    RT: null,\n    RP: null,\n    Warichu: null,\n    WT: null,\n    WP: null,\n    // List standard structure types\n    L: 'list',\n    LI: 'listitem',\n    LBody: null,\n    // Table standard structure types\n    Table: 'table',\n    TR: 'row',\n    TH: 'columnheader',\n    TD: 'cell',\n    THead: 'columnheader',\n    TBody: null,\n    TFoot: null,\n    // Standard structure type Caption\n    Caption: null,\n    // Standard structure type Figure\n    Figure: 'figure',\n    // Standard structure type Formula\n    Formula: null,\n    // standard structure type Artifact\n    Artifact: null,\n};\nconst HEADING_PATTERN = /^H(\\d+)$/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useDocumentContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../DocumentContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/DocumentContext.js\");\n\n\nfunction useDocumentContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_DocumentContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VEb2N1bWVudENvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW1DO0FBQ29CO0FBQ3hDO0FBQ2YsV0FBVyxpREFBVSxDQUFDLDJEQUFlO0FBQ3JDIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXHJlYWN0LXBkZlxcZGlzdFxcZXNtXFxzaGFyZWRcXGhvb2tzXFx1c2VEb2N1bWVudENvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBEb2N1bWVudENvbnRleHQgZnJvbSAnLi4vLi4vRG9jdW1lbnRDb250ZXh0LmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZURvY3VtZW50Q29udGV4dCgpIHtcbiAgICByZXR1cm4gdXNlQ29udGV4dChEb2N1bWVudENvbnRleHQpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ usePageContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _PageContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../PageContext.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/PageContext.js\");\n\n\nfunction usePageContext() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_PageContext_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VQYWdlQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDWTtBQUNoQztBQUNmLFdBQVcsaURBQVUsQ0FBQyx1REFBVztBQUNqQyIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxyZWFjdC1wZGZcXGRpc3RcXGVzbVxcc2hhcmVkXFxob29rc1xcdXNlUGFnZUNvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBQYWdlQ29udGV4dCBmcm9tICcuLi8uLi9QYWdlQ29udGV4dC5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VQYWdlQ29udGV4dCgpIHtcbiAgICByZXR1cm4gdXNlQ29udGV4dChQYWdlQ29udGV4dCk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useResolver)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction reducer(state, action) {\n    switch (action.type) {\n        case 'RESOLVE':\n            return { value: action.value, error: undefined };\n        case 'REJECT':\n            return { value: false, error: action.error };\n        case 'RESET':\n            return { value: undefined, error: undefined };\n        default:\n            return state;\n    }\n}\nfunction useResolver() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((reducer), { value: undefined, error: undefined });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcGRmL2Rpc3QvZXNtL3NoYXJlZC9ob29rcy91c2VSZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtQztBQUNuQztBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmLFdBQVcsaURBQVUsY0FBYyxvQ0FBb0M7QUFDdkUiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccmVhY3QtcGRmXFxkaXN0XFxlc21cXHNoYXJlZFxcaG9va3NcXHVzZVJlc29sdmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVJlZHVjZXIgfSBmcm9tICdyZWFjdCc7XG5mdW5jdGlvbiByZWR1Y2VyKHN0YXRlLCBhY3Rpb24pIHtcbiAgICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgICAgIGNhc2UgJ1JFU09MVkUnOlxuICAgICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IGFjdGlvbi52YWx1ZSwgZXJyb3I6IHVuZGVmaW5lZCB9O1xuICAgICAgICBjYXNlICdSRUpFQ1QnOlxuICAgICAgICAgICAgcmV0dXJuIHsgdmFsdWU6IGZhbHNlLCBlcnJvcjogYWN0aW9uLmVycm9yIH07XG4gICAgICAgIGNhc2UgJ1JFU0VUJzpcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiB1bmRlZmluZWQsIGVycm9yOiB1bmRlZmluZWQgfTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9XG59XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VSZXNvbHZlcigpIHtcbiAgICByZXR1cm4gdXNlUmVkdWNlcigocmVkdWNlciksIHsgdmFsdWU6IHVuZGVmaW5lZCwgZXJyb3I6IHVuZGVmaW5lZCB9KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributes: () => (/* binding */ getAttributes),\n/* harmony export */   getBaseAttributes: () => (/* binding */ getBaseAttributes),\n/* harmony export */   getRoleAttributes: () => (/* binding */ getRoleAttributes),\n/* harmony export */   isPdfRole: () => (/* binding */ isPdfRole),\n/* harmony export */   isStructTreeNode: () => (/* binding */ isStructTreeNode),\n/* harmony export */   isStructTreeNodeWithOnlyContentChild: () => (/* binding */ isStructTreeNodeWithOnlyContentChild)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/react-pdf/dist/esm/shared/constants.js\");\n\nfunction isPdfRole(role) {\n    return role in _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE;\n}\nfunction isStructTreeNode(node) {\n    return 'children' in node;\n}\nfunction isStructTreeNodeWithOnlyContentChild(node) {\n    if (!isStructTreeNode(node)) {\n        return false;\n    }\n    return node.children.length === 1 && 0 in node.children && 'id' in node.children[0];\n}\nfunction getRoleAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        const { role } = node;\n        const matches = role.match(_constants_js__WEBPACK_IMPORTED_MODULE_0__.HEADING_PATTERN);\n        if (matches) {\n            attributes.role = 'heading';\n            attributes['aria-level'] = Number(matches[1]);\n        }\n        else if (isPdfRole(role)) {\n            const htmlRole = _constants_js__WEBPACK_IMPORTED_MODULE_0__.PDF_ROLE_TO_HTML_ROLE[role];\n            if (htmlRole) {\n                attributes.role = htmlRole;\n            }\n        }\n    }\n    return attributes;\n}\nfunction getBaseAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        if (node.alt !== undefined) {\n            attributes['aria-label'] = node.alt;\n        }\n        if (node.lang !== undefined) {\n            attributes.lang = node.lang;\n        }\n        if (isStructTreeNodeWithOnlyContentChild(node)) {\n            const [child] = node.children;\n            if (child) {\n                const childAttributes = getBaseAttributes(child);\n                return Object.assign(Object.assign({}, attributes), childAttributes);\n            }\n        }\n    }\n    else {\n        if ('id' in node) {\n            attributes['aria-owns'] = node.id;\n        }\n    }\n    return attributes;\n}\nfunction getAttributes(node) {\n    if (!node) {\n        return null;\n    }\n    return Object.assign(Object.assign({}, getRoleAttributes(node)), getBaseAttributes(node));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-pdf/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelRunningTask: () => (/* binding */ cancelRunningTask),\n/* harmony export */   dataURItoByteString: () => (/* binding */ dataURItoByteString),\n/* harmony export */   displayCORSWarning: () => (/* binding */ displayCORSWarning),\n/* harmony export */   displayWorkerWarning: () => (/* binding */ displayWorkerWarning),\n/* harmony export */   getDevicePixelRatio: () => (/* binding */ getDevicePixelRatio),\n/* harmony export */   isArrayBuffer: () => (/* binding */ isArrayBuffer),\n/* harmony export */   isBlob: () => (/* binding */ isBlob),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isCancelException: () => (/* binding */ isCancelException),\n/* harmony export */   isDataURI: () => (/* binding */ isDataURI),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isLocalFileSystem: () => (/* binding */ isLocalFileSystem),\n/* harmony export */   isProvided: () => (/* binding */ isProvided),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadFromFile: () => (/* binding */ loadFromFile),\n/* harmony export */   makePageCallback: () => (/* binding */ makePageCallback)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n\n\n/**\n * Checks if we're running in a browser environment.\n */\nconst isBrowser = typeof window !== 'undefined';\n/**\n * Checks whether we're running from a local file system.\n */\nconst isLocalFileSystem = isBrowser && window.location.protocol === 'file:';\n/**\n * Checks whether a variable is defined.\n *\n * @param {*} variable Variable to check\n */\nfunction isDefined(variable) {\n    return typeof variable !== 'undefined';\n}\n/**\n * Checks whether a variable is defined and not null.\n *\n * @param {*} variable Variable to check\n */\nfunction isProvided(variable) {\n    return isDefined(variable) && variable !== null;\n}\n/**\n * Checks whether a variable provided is a string.\n *\n * @param {*} variable Variable to check\n */\nfunction isString(variable) {\n    return typeof variable === 'string';\n}\n/**\n * Checks whether a variable provided is an ArrayBuffer.\n *\n * @param {*} variable Variable to check\n */\nfunction isArrayBuffer(variable) {\n    return variable instanceof ArrayBuffer;\n}\n/**\n * Checks whether a variable provided is a Blob.\n *\n * @param {*} variable Variable to check\n */\nfunction isBlob(variable) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isBrowser, 'isBlob can only be used in a browser environment');\n    return variable instanceof Blob;\n}\n/**\n * Checks whether a variable provided is a data URI.\n *\n * @param {*} variable String to check\n */\nfunction isDataURI(variable) {\n    return isString(variable) && /^data:/.test(variable);\n}\nfunction dataURItoByteString(dataURI) {\n    (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isDataURI(dataURI), 'Invalid data URI.');\n    const [headersString = '', dataString = ''] = dataURI.split(',');\n    const headers = headersString.split(';');\n    if (headers.indexOf('base64') !== -1) {\n        return atob(dataString);\n    }\n    return unescape(dataString);\n}\nfunction getDevicePixelRatio() {\n    return (isBrowser && window.devicePixelRatio) || 1;\n}\nconst allowFileAccessFromFilesTip = 'On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.';\nfunction displayCORSWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction displayWorkerWarning() {\n    warning__WEBPACK_IMPORTED_MODULE_1__(!isLocalFileSystem, `Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nfunction cancelRunningTask(runningTask) {\n    if (runningTask === null || runningTask === void 0 ? void 0 : runningTask.cancel)\n        runningTask.cancel();\n}\nfunction makePageCallback(page, scale) {\n    Object.defineProperty(page, 'width', {\n        get() {\n            return this.view[2] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'height', {\n        get() {\n            return this.view[3] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalWidth', {\n        get() {\n            return this.view[2];\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalHeight', {\n        get() {\n            return this.view[3];\n        },\n        configurable: true,\n    });\n    return page;\n}\nfunction isCancelException(error) {\n    return error.name === 'RenderingCancelledException';\n}\nfunction loadFromFile(file) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n            if (!reader.result) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            resolve(reader.result);\n        };\n        reader.onerror = (event) => {\n            if (!event.target) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            const { error } = event.target;\n            if (!error) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            switch (error.code) {\n                case error.NOT_FOUND_ERR:\n                    return reject(new Error('Error while reading a file: File not found.'));\n                case error.SECURITY_ERR:\n                    return reject(new Error('Error while reading a file: Security error.'));\n                case error.ABORT_ERR:\n                    return reject(new Error('Error while reading a file: Aborted.'));\n                default:\n                    return reject(new Error('Error while reading a file.'));\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-pdf/dist/esm/shared/utils.js\n");

/***/ })

};
;