"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/downshift";
exports.ids = ["vendor-chunks/downshift"];
exports.modules = {

/***/ "(ssr)/./node_modules/downshift/dist/downshift.esm.js":
/*!******************************************************!*\
  !*** ./node_modules/downshift/dist/downshift.esm.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Downshift$1),\n/* harmony export */   resetIdCounter: () => (/* binding */ resetIdCounter),\n/* harmony export */   useCombobox: () => (/* binding */ useCombobox),\n/* harmony export */   useMultipleSelection: () => (/* binding */ useMultipleSelection),\n/* harmony export */   useSelect: () => (/* binding */ useSelect)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_is__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/react-is/index.js\");\n/* harmony import */ var compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! compute-scroll-into-view */ \"(ssr)/./node_modules/compute-scroll-into-view/dist/index.js\");\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\n\n\n\n\n\n\n\n\n\nvar idCounter = 0;\n\n/**\n * Accepts a parameter and returns it if it's a function\n * or a noop function if it's not. This allows us to\n * accept a callback, but not worry about it if it's not\n * passed.\n * @param {Function} cb the callback\n * @return {Function} a function\n */\nfunction cbToCb(cb) {\n  return typeof cb === 'function' ? cb : noop;\n}\nfunction noop() {}\n\n/**\n * Scroll node into view if necessary\n * @param {HTMLElement} node the element that should scroll into view\n * @param {HTMLElement} menuNode the menu element of the component\n */\nfunction scrollIntoView(node, menuNode) {\n  if (!node) {\n    return;\n  }\n  var actions = (0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(node, {\n    boundary: menuNode,\n    block: 'nearest',\n    scrollMode: 'if-needed'\n  });\n  actions.forEach(function (_ref) {\n    var el = _ref.el,\n      top = _ref.top,\n      left = _ref.left;\n    el.scrollTop = top;\n    el.scrollLeft = left;\n  });\n}\n\n/**\n * @param {HTMLElement} parent the parent node\n * @param {HTMLElement} child the child node\n * @param {Window} environment The window context where downshift renders.\n * @return {Boolean} whether the parent is the child or the child is in the parent\n */\nfunction isOrContainsNode(parent, child, environment) {\n  var result = parent === child || child instanceof environment.Node && parent.contains && parent.contains(child);\n  return result;\n}\n\n/**\n * Simple debounce implementation. Will call the given\n * function once after the time given has passed since\n * it was last called.\n * @param {Function} fn the function to call after the time\n * @param {Number} time the time to wait\n * @return {Function} the debounced function\n */\nfunction debounce(fn, time) {\n  var timeoutId;\n  function cancel() {\n    if (timeoutId) {\n      clearTimeout(timeoutId);\n    }\n  }\n  function wrapper() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    cancel();\n    timeoutId = setTimeout(function () {\n      timeoutId = null;\n      fn.apply(void 0, args);\n    }, time);\n  }\n  wrapper.cancel = cancel;\n  return wrapper;\n}\n\n/**\n * This is intended to be used to compose event handlers.\n * They are executed in order until one of them sets\n * `event.preventDownshiftDefault = true`.\n * @param {...Function} fns the event handler functions\n * @return {Function} the event handler to add to an element\n */\nfunction callAllEventHandlers() {\n  for (var _len2 = arguments.length, fns = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    fns[_key2] = arguments[_key2];\n  }\n  return function (event) {\n    for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      args[_key3 - 1] = arguments[_key3];\n    }\n    return fns.some(function (fn) {\n      if (fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n      return event.preventDownshiftDefault || event.hasOwnProperty('nativeEvent') && event.nativeEvent.preventDownshiftDefault;\n    });\n  };\n}\nfunction handleRefs() {\n  for (var _len4 = arguments.length, refs = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n    refs[_key4] = arguments[_key4];\n  }\n  return function (node) {\n    refs.forEach(function (ref) {\n      if (typeof ref === 'function') {\n        ref(node);\n      } else if (ref) {\n        ref.current = node;\n      }\n    });\n  };\n}\n\n/**\n * This generates a unique ID for an instance of Downshift\n * @return {String} the unique ID\n */\nfunction generateId() {\n  return String(idCounter++);\n}\n\n/**\n * Resets idCounter to 0. Used for SSR.\n */\nfunction resetIdCounter() {\n  idCounter = 0;\n}\n\n/**\n * Default implementation for status message. Only added when menu is open.\n * Will specify if there are results in the list, and if so, how many,\n * and what keys are relevant.\n *\n * @param {Object} param the downshift state and other relevant properties\n * @return {String} the a11y status message\n */\nfunction getA11yStatusMessage$1(_ref2) {\n  var isOpen = _ref2.isOpen,\n    resultCount = _ref2.resultCount,\n    previousResultCount = _ref2.previousResultCount;\n  if (!isOpen) {\n    return '';\n  }\n  if (!resultCount) {\n    return 'No results are available.';\n  }\n  if (resultCount !== previousResultCount) {\n    return resultCount + \" result\" + (resultCount === 1 ? ' is' : 's are') + \" available, use up and down arrow keys to navigate. Press Enter key to select.\";\n  }\n  return '';\n}\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array\n * otherwise returns the argument\n * @param {*} arg the maybe-array\n * @param {*} defaultValue the value if arg is falsey not defined\n * @return {*} the arg or it's first item\n */\nfunction unwrapArray(arg, defaultValue) {\n  arg = Array.isArray(arg) ? /* istanbul ignore next (preact) */arg[0] : arg;\n  if (!arg && defaultValue) {\n    return defaultValue;\n  } else {\n    return arg;\n  }\n}\n\n/**\n * @param {Object} element (P)react element\n * @return {Boolean} whether it's a DOM element\n */\nfunction isDOMElement(element) {\n\n  // then we assume this is react\n  return typeof element.type === 'string';\n}\n\n/**\n * @param {Object} element (P)react element\n * @return {Object} the props\n */\nfunction getElementProps(element) {\n  return element.props;\n}\n\n/**\n * Throws a helpful error message for required properties. Useful\n * to be used as a default in destructuring or object params.\n * @param {String} fnName the function name\n * @param {String} propName the prop name\n */\nfunction requiredProp(fnName, propName) {\n  // eslint-disable-next-line no-console\n  console.error(\"The property \\\"\" + propName + \"\\\" is required in \\\"\" + fnName + \"\\\"\");\n}\nvar stateKeys = ['highlightedIndex', 'inputValue', 'isOpen', 'selectedItem', 'type'];\n/**\n * @param {Object} state the state object\n * @return {Object} state that is relevant to downshift\n */\nfunction pickState(state) {\n  if (state === void 0) {\n    state = {};\n  }\n  var result = {};\n  stateKeys.forEach(function (k) {\n    if (state.hasOwnProperty(k)) {\n      result[k] = state[k];\n    }\n  });\n  return result;\n}\n\n/**\n * This will perform a shallow merge of the given state object\n * with the state coming from props\n * (for the controlled component scenario)\n * This is used in state updater functions so they're referencing\n * the right state regardless of where it comes from.\n *\n * @param {Object} state The state of the component/hook.\n * @param {Object} props The props that may contain controlled values.\n * @returns {Object} The merged controlled state.\n */\nfunction getState(state, props) {\n  return Object.keys(state).reduce(function (prevState, key) {\n    prevState[key] = isControlledProp(props, key) ? props[key] : state[key];\n    return prevState;\n  }, {});\n}\n\n/**\n * This determines whether a prop is a \"controlled prop\" meaning it is\n * state which is controlled by the outside of this component rather\n * than within this component.\n *\n * @param {Object} props The props that may contain controlled values.\n * @param {String} key the key to check\n * @return {Boolean} whether it is a controlled controlled prop\n */\nfunction isControlledProp(props, key) {\n  return props[key] !== undefined;\n}\n\n/**\n * Normalizes the 'key' property of a KeyboardEvent in IE/Edge\n * @param {Object} event a keyboardEvent object\n * @return {String} keyboard key\n */\nfunction normalizeArrowKey(event) {\n  var key = event.key,\n    keyCode = event.keyCode;\n  /* istanbul ignore next (ie) */\n  if (keyCode >= 37 && keyCode <= 40 && key.indexOf('Arrow') !== 0) {\n    return \"Arrow\" + key;\n  }\n  return key;\n}\n\n/**\n * Simple check if the value passed is object literal\n * @param {*} obj any things\n * @return {Boolean} whether it's object literal\n */\nfunction isPlainObject(obj) {\n  return Object.prototype.toString.call(obj) === '[object Object]';\n}\n\n/**\n * Returns the new index in the list, in a circular way. If next value is out of bonds from the total,\n * it will wrap to either 0 or itemCount - 1.\n *\n * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.\n * @param {number} baseIndex The initial position to move from.\n * @param {number} itemCount The total number of items.\n * @param {Function} getItemNodeFromIndex Used to check if item is disabled.\n * @param {boolean} circular Specify if navigation is circular. Default is true.\n * @returns {number} The new index after the move.\n */\nfunction getNextWrappingIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {\n  if (circular === void 0) {\n    circular = true;\n  }\n  if (itemCount === 0) {\n    return -1;\n  }\n  var itemsLastIndex = itemCount - 1;\n  if (typeof baseIndex !== 'number' || baseIndex < 0 || baseIndex >= itemCount) {\n    baseIndex = moveAmount > 0 ? -1 : itemsLastIndex + 1;\n  }\n  var newIndex = baseIndex + moveAmount;\n  if (newIndex < 0) {\n    newIndex = circular ? itemsLastIndex : 0;\n  } else if (newIndex > itemsLastIndex) {\n    newIndex = circular ? 0 : itemsLastIndex;\n  }\n  var nonDisabledNewIndex = getNextNonDisabledIndex(moveAmount, newIndex, itemCount, getItemNodeFromIndex, circular);\n  if (nonDisabledNewIndex === -1) {\n    return baseIndex >= itemCount ? -1 : baseIndex;\n  }\n  return nonDisabledNewIndex;\n}\n\n/**\n * Returns the next index in the list of an item that is not disabled.\n *\n * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.\n * @param {number} baseIndex The initial position to move from.\n * @param {number} itemCount The total number of items.\n * @param {Function} getItemNodeFromIndex Used to check if item is disabled.\n * @param {boolean} circular Specify if navigation is circular. Default is true.\n * @returns {number} The new index. Returns baseIndex if item is not disabled. Returns next non-disabled item otherwise. If no non-disabled found it will return -1.\n */\nfunction getNextNonDisabledIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {\n  var currentElementNode = getItemNodeFromIndex(baseIndex);\n  if (!currentElementNode || !currentElementNode.hasAttribute('disabled')) {\n    return baseIndex;\n  }\n  if (moveAmount > 0) {\n    for (var index = baseIndex + 1; index < itemCount; index++) {\n      if (!getItemNodeFromIndex(index).hasAttribute('disabled')) {\n        return index;\n      }\n    }\n  } else {\n    for (var _index = baseIndex - 1; _index >= 0; _index--) {\n      if (!getItemNodeFromIndex(_index).hasAttribute('disabled')) {\n        return _index;\n      }\n    }\n  }\n  if (circular) {\n    return moveAmount > 0 ? getNextNonDisabledIndex(1, 0, itemCount, getItemNodeFromIndex, false) : getNextNonDisabledIndex(-1, itemCount - 1, itemCount, getItemNodeFromIndex, false);\n  }\n  return -1;\n}\n\n/**\n * Checks if event target is within the downshift elements.\n *\n * @param {EventTarget} target Target to check.\n * @param {HTMLElement[]} downshiftElements The elements that form downshift (list, toggle button etc).\n * @param {Window} environment The window context where downshift renders.\n * @param {boolean} checkActiveElement Whether to also check activeElement.\n *\n * @returns {boolean} Whether or not the target is within downshift elements.\n */\nfunction targetWithinDownshift(target, downshiftElements, environment, checkActiveElement) {\n  if (checkActiveElement === void 0) {\n    checkActiveElement = true;\n  }\n  return downshiftElements.some(function (contextNode) {\n    return contextNode && (isOrContainsNode(contextNode, target, environment) || checkActiveElement && isOrContainsNode(contextNode, environment.document.activeElement, environment));\n  });\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validateControlledUnchanged = noop;\n/* istanbul ignore next */\nif (true) {\n  validateControlledUnchanged = function validateControlledUnchanged(state, prevProps, nextProps) {\n    var warningDescription = \"This prop should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled Downshift element for the lifetime of the component. More info: https://github.com/downshift-js/downshift#control-props\";\n    Object.keys(state).forEach(function (propKey) {\n      if (prevProps[propKey] !== undefined && nextProps[propKey] === undefined) {\n        // eslint-disable-next-line no-console\n        console.error(\"downshift: A component has changed the controlled prop \\\"\" + propKey + \"\\\" to be uncontrolled. \" + warningDescription);\n      } else if (prevProps[propKey] === undefined && nextProps[propKey] !== undefined) {\n        // eslint-disable-next-line no-console\n        console.error(\"downshift: A component has changed the uncontrolled prop \\\"\" + propKey + \"\\\" to be controlled. \" + warningDescription);\n      }\n    });\n  };\n}\n\nvar cleanupStatus = debounce(function (documentProp) {\n  getStatusDiv(documentProp).textContent = '';\n}, 500);\n\n/**\n * @param {String} status the status message\n * @param {Object} documentProp document passed by the user.\n */\nfunction setStatus(status, documentProp) {\n  var div = getStatusDiv(documentProp);\n  if (!status) {\n    return;\n  }\n  div.textContent = status;\n  cleanupStatus(documentProp);\n}\n\n/**\n * Get the status node or create it if it does not already exist.\n * @param {Object} documentProp document passed by the user.\n * @return {HTMLElement} the status node.\n */\nfunction getStatusDiv(documentProp) {\n  if (documentProp === void 0) {\n    documentProp = document;\n  }\n  var statusDiv = documentProp.getElementById('a11y-status-message');\n  if (statusDiv) {\n    return statusDiv;\n  }\n  statusDiv = documentProp.createElement('div');\n  statusDiv.setAttribute('id', 'a11y-status-message');\n  statusDiv.setAttribute('role', 'status');\n  statusDiv.setAttribute('aria-live', 'polite');\n  statusDiv.setAttribute('aria-relevant', 'additions text');\n  Object.assign(statusDiv.style, {\n    border: '0',\n    clip: 'rect(0 0 0 0)',\n    height: '1px',\n    margin: '-1px',\n    overflow: 'hidden',\n    padding: '0',\n    position: 'absolute',\n    width: '1px'\n  });\n  documentProp.body.appendChild(statusDiv);\n  return statusDiv;\n}\n\nvar unknown =  true ? '__autocomplete_unknown__' : 0;\nvar mouseUp =  true ? '__autocomplete_mouseup__' : 0;\nvar itemMouseEnter =  true ? '__autocomplete_item_mouseenter__' : 0;\nvar keyDownArrowUp =  true ? '__autocomplete_keydown_arrow_up__' : 0;\nvar keyDownArrowDown =  true ? '__autocomplete_keydown_arrow_down__' : 0;\nvar keyDownEscape =  true ? '__autocomplete_keydown_escape__' : 0;\nvar keyDownEnter =  true ? '__autocomplete_keydown_enter__' : 0;\nvar keyDownHome =  true ? '__autocomplete_keydown_home__' : 0;\nvar keyDownEnd =  true ? '__autocomplete_keydown_end__' : 0;\nvar clickItem =  true ? '__autocomplete_click_item__' : 0;\nvar blurInput =  true ? '__autocomplete_blur_input__' : 0;\nvar changeInput =  true ? '__autocomplete_change_input__' : 0;\nvar keyDownSpaceButton =  true ? '__autocomplete_keydown_space_button__' : 0;\nvar clickButton =  true ? '__autocomplete_click_button__' : 0;\nvar blurButton =  true ? '__autocomplete_blur_button__' : 0;\nvar controlledPropUpdatedSelectedItem =  true ? '__autocomplete_controlled_prop_updated_selected_item__' : 0;\nvar touchEnd =  true ? '__autocomplete_touchend__' : 0;\n\nvar stateChangeTypes$3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  unknown: unknown,\n  mouseUp: mouseUp,\n  itemMouseEnter: itemMouseEnter,\n  keyDownArrowUp: keyDownArrowUp,\n  keyDownArrowDown: keyDownArrowDown,\n  keyDownEscape: keyDownEscape,\n  keyDownEnter: keyDownEnter,\n  keyDownHome: keyDownHome,\n  keyDownEnd: keyDownEnd,\n  clickItem: clickItem,\n  blurInput: blurInput,\n  changeInput: changeInput,\n  keyDownSpaceButton: keyDownSpaceButton,\n  clickButton: clickButton,\n  blurButton: blurButton,\n  controlledPropUpdatedSelectedItem: controlledPropUpdatedSelectedItem,\n  touchEnd: touchEnd\n});\n\nvar _excluded$4 = [\"refKey\", \"ref\"],\n  _excluded2$3 = [\"onClick\", \"onPress\", \"onKeyDown\", \"onKeyUp\", \"onBlur\"],\n  _excluded3$2 = [\"onKeyDown\", \"onBlur\", \"onChange\", \"onInput\", \"onChangeText\"],\n  _excluded4$1 = [\"refKey\", \"ref\"],\n  _excluded5 = [\"onMouseMove\", \"onMouseDown\", \"onClick\", \"onPress\", \"index\", \"item\"];\nvar Downshift = /*#__PURE__*/function () {\n  var Downshift = /*#__PURE__*/function (_Component) {\n    (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Downshift, _Component);\n    function Downshift(_props) {\n      var _this;\n      _this = _Component.call(this, _props) || this;\n      // fancy destructuring + defaults + aliases\n      // this basically says each value of state should either be set to\n      // the initial value or the default value if the initial value is not provided\n      _this.id = _this.props.id || \"downshift-\" + generateId();\n      _this.menuId = _this.props.menuId || _this.id + \"-menu\";\n      _this.labelId = _this.props.labelId || _this.id + \"-label\";\n      _this.inputId = _this.props.inputId || _this.id + \"-input\";\n      _this.getItemId = _this.props.getItemId || function (index) {\n        return _this.id + \"-item-\" + index;\n      };\n      _this.input = null;\n      _this.items = [];\n      // itemCount can be changed asynchronously\n      // from within downshift (so it can't come from a prop)\n      // this is why we store it as an instance and use\n      // getItemCount rather than just use items.length\n      // (to support windowing + async)\n      _this.itemCount = null;\n      _this.previousResultCount = 0;\n      _this.timeoutIds = [];\n      /**\n       * @param {Function} fn the function to call after the time\n       * @param {Number} time the time to wait\n       */\n      _this.internalSetTimeout = function (fn, time) {\n        var id = setTimeout(function () {\n          _this.timeoutIds = _this.timeoutIds.filter(function (i) {\n            return i !== id;\n          });\n          fn();\n        }, time);\n        _this.timeoutIds.push(id);\n      };\n      _this.setItemCount = function (count) {\n        _this.itemCount = count;\n      };\n      _this.unsetItemCount = function () {\n        _this.itemCount = null;\n      };\n      _this.setHighlightedIndex = function (highlightedIndex, otherStateToSet) {\n        if (highlightedIndex === void 0) {\n          highlightedIndex = _this.props.defaultHighlightedIndex;\n        }\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          highlightedIndex: highlightedIndex\n        }, otherStateToSet));\n      };\n      _this.clearSelection = function (cb) {\n        _this.internalSetState({\n          selectedItem: null,\n          inputValue: '',\n          highlightedIndex: _this.props.defaultHighlightedIndex,\n          isOpen: _this.props.defaultIsOpen\n        }, cb);\n      };\n      _this.selectItem = function (item, otherStateToSet, cb) {\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          isOpen: _this.props.defaultIsOpen,\n          highlightedIndex: _this.props.defaultHighlightedIndex,\n          selectedItem: item,\n          inputValue: _this.props.itemToString(item)\n        }, otherStateToSet), cb);\n      };\n      _this.selectItemAtIndex = function (itemIndex, otherStateToSet, cb) {\n        var item = _this.items[itemIndex];\n        if (item == null) {\n          return;\n        }\n        _this.selectItem(item, otherStateToSet, cb);\n      };\n      _this.selectHighlightedItem = function (otherStateToSet, cb) {\n        return _this.selectItemAtIndex(_this.getState().highlightedIndex, otherStateToSet, cb);\n      };\n      // any piece of our state can live in two places:\n      // 1. Uncontrolled: it's internal (this.state)\n      //    We will call this.setState to update that state\n      // 2. Controlled: it's external (this.props)\n      //    We will call this.props.onStateChange to update that state\n      //\n      // In addition, we'll call this.props.onChange if the\n      // selectedItem is changed.\n      _this.internalSetState = function (stateToSet, cb) {\n        var isItemSelected, onChangeArg;\n        var onStateChangeArg = {};\n        var isStateToSetFunction = typeof stateToSet === 'function';\n\n        // we want to call `onInputValueChange` before the `setState` call\n        // so someone controlling the `inputValue` state gets notified of\n        // the input change as soon as possible. This avoids issues with\n        // preserving the cursor position.\n        // See https://github.com/downshift-js/downshift/issues/217 for more info.\n        if (!isStateToSetFunction && stateToSet.hasOwnProperty('inputValue')) {\n          _this.props.onInputValueChange(stateToSet.inputValue, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _this.getStateAndHelpers(), stateToSet));\n        }\n        return _this.setState(function (state) {\n          state = _this.getState(state);\n          var newStateToSet = isStateToSetFunction ? stateToSet(state) : stateToSet;\n\n          // Your own function that could modify the state that will be set.\n          newStateToSet = _this.props.stateReducer(state, newStateToSet);\n\n          // checks if an item is selected, regardless of if it's different from\n          // what was selected before\n          // used to determine if onSelect and onChange callbacks should be called\n          isItemSelected = newStateToSet.hasOwnProperty('selectedItem');\n          // this keeps track of the object we want to call with setState\n          var nextState = {};\n          // we need to call on change if the outside world is controlling any of our state\n          // and we're trying to update that state. OR if the selection has changed and we're\n          // trying to update the selection\n          if (isItemSelected && newStateToSet.selectedItem !== state.selectedItem) {\n            onChangeArg = newStateToSet.selectedItem;\n          }\n          newStateToSet.type = newStateToSet.type || unknown;\n          Object.keys(newStateToSet).forEach(function (key) {\n            // onStateChangeArg should only have the state that is\n            // actually changing\n            if (state[key] !== newStateToSet[key]) {\n              onStateChangeArg[key] = newStateToSet[key];\n            }\n            // the type is useful for the onStateChangeArg\n            // but we don't actually want to set it in internal state.\n            // this is an undocumented feature for now... Not all internalSetState\n            // calls support it and I'm not certain we want them to yet.\n            // But it enables users controlling the isOpen state to know when\n            // the isOpen state changes due to mouseup events which is quite handy.\n            if (key === 'type') {\n              return;\n            }\n            newStateToSet[key];\n            // if it's coming from props, then we don't care to set it internally\n            if (!isControlledProp(_this.props, key)) {\n              nextState[key] = newStateToSet[key];\n            }\n          });\n\n          // if stateToSet is a function, then we weren't able to call onInputValueChange\n          // earlier, so we'll call it now that we know what the inputValue state will be.\n          if (isStateToSetFunction && newStateToSet.hasOwnProperty('inputValue')) {\n            _this.props.onInputValueChange(newStateToSet.inputValue, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _this.getStateAndHelpers(), newStateToSet));\n          }\n          return nextState;\n        }, function () {\n          // call the provided callback if it's a function\n          cbToCb(cb)();\n\n          // only call the onStateChange and onChange callbacks if\n          // we have relevant information to pass them.\n          var hasMoreStateThanType = Object.keys(onStateChangeArg).length > 1;\n          if (hasMoreStateThanType) {\n            _this.props.onStateChange(onStateChangeArg, _this.getStateAndHelpers());\n          }\n          if (isItemSelected) {\n            _this.props.onSelect(stateToSet.selectedItem, _this.getStateAndHelpers());\n          }\n          if (onChangeArg !== undefined) {\n            _this.props.onChange(onChangeArg, _this.getStateAndHelpers());\n          }\n          // this is currently undocumented and therefore subject to change\n          // We'll try to not break it, but just be warned.\n          _this.props.onUserAction(onStateChangeArg, _this.getStateAndHelpers());\n        });\n      };\n      //////////////////////////// ROOT\n      _this.rootRef = function (node) {\n        return _this._rootNode = node;\n      };\n      _this.getRootProps = function (_temp, _temp2) {\n        var _extends2;\n        var _ref = _temp === void 0 ? {} : _temp,\n          _ref$refKey = _ref.refKey,\n          refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey,\n          ref = _ref.ref,\n          rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded$4);\n        var _ref2 = _temp2 === void 0 ? {} : _temp2,\n          _ref2$suppressRefErro = _ref2.suppressRefError,\n          suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n        // this is used in the render to know whether the user has called getRootProps.\n        // It uses that to know whether to apply the props automatically\n        _this.getRootProps.called = true;\n        _this.getRootProps.refKey = refKey;\n        _this.getRootProps.suppressRefError = suppressRefError;\n        var _this$getState = _this.getState(),\n          isOpen = _this$getState.isOpen;\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, _this.rootRef), _extends2.role = 'combobox', _extends2['aria-expanded'] = isOpen, _extends2['aria-haspopup'] = 'listbox', _extends2['aria-owns'] = isOpen ? _this.menuId : null, _extends2['aria-labelledby'] = _this.labelId, _extends2), rest);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ROOT\n      _this.keyDownHandlers = {\n        ArrowDown: function ArrowDown(event) {\n          var _this2 = this;\n          event.preventDefault();\n          if (this.getState().isOpen) {\n            var amount = event.shiftKey ? 5 : 1;\n            this.moveHighlightedIndex(amount, {\n              type: keyDownArrowDown\n            });\n          } else {\n            this.internalSetState({\n              isOpen: true,\n              type: keyDownArrowDown\n            }, function () {\n              var itemCount = _this2.getItemCount();\n              if (itemCount > 0) {\n                var _this2$getState = _this2.getState(),\n                  highlightedIndex = _this2$getState.highlightedIndex;\n                var nextHighlightedIndex = getNextWrappingIndex(1, highlightedIndex, itemCount, function (index) {\n                  return _this2.getItemNodeFromIndex(index);\n                });\n                _this2.setHighlightedIndex(nextHighlightedIndex, {\n                  type: keyDownArrowDown\n                });\n              }\n            });\n          }\n        },\n        ArrowUp: function ArrowUp(event) {\n          var _this3 = this;\n          event.preventDefault();\n          if (this.getState().isOpen) {\n            var amount = event.shiftKey ? -5 : -1;\n            this.moveHighlightedIndex(amount, {\n              type: keyDownArrowUp\n            });\n          } else {\n            this.internalSetState({\n              isOpen: true,\n              type: keyDownArrowUp\n            }, function () {\n              var itemCount = _this3.getItemCount();\n              if (itemCount > 0) {\n                var _this3$getState = _this3.getState(),\n                  highlightedIndex = _this3$getState.highlightedIndex;\n                var nextHighlightedIndex = getNextWrappingIndex(-1, highlightedIndex, itemCount, function (index) {\n                  return _this3.getItemNodeFromIndex(index);\n                });\n                _this3.setHighlightedIndex(nextHighlightedIndex, {\n                  type: keyDownArrowUp\n                });\n              }\n            });\n          }\n        },\n        Enter: function Enter(event) {\n          if (event.which === 229) {\n            return;\n          }\n          var _this$getState2 = this.getState(),\n            isOpen = _this$getState2.isOpen,\n            highlightedIndex = _this$getState2.highlightedIndex;\n          if (isOpen && highlightedIndex != null) {\n            event.preventDefault();\n            var item = this.items[highlightedIndex];\n            var itemNode = this.getItemNodeFromIndex(highlightedIndex);\n            if (item == null || itemNode && itemNode.hasAttribute('disabled')) {\n              return;\n            }\n            this.selectHighlightedItem({\n              type: keyDownEnter\n            });\n          }\n        },\n        Escape: function Escape(event) {\n          event.preventDefault();\n          this.reset((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            type: keyDownEscape\n          }, !this.state.isOpen && {\n            selectedItem: null,\n            inputValue: ''\n          }));\n        }\n      };\n      //////////////////////////// BUTTON\n      _this.buttonKeyDownHandlers = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _this.keyDownHandlers, {\n        ' ': function _(event) {\n          event.preventDefault();\n          this.toggleMenu({\n            type: keyDownSpaceButton\n          });\n        }\n      });\n      _this.inputKeyDownHandlers = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _this.keyDownHandlers, {\n        Home: function Home(event) {\n          var _this4 = this;\n          var _this$getState3 = this.getState(),\n            isOpen = _this$getState3.isOpen;\n          if (!isOpen) {\n            return;\n          }\n          event.preventDefault();\n          var itemCount = this.getItemCount();\n          if (itemCount <= 0 || !isOpen) {\n            return;\n          }\n\n          // get next non-disabled starting downwards from 0 if that's disabled.\n          var newHighlightedIndex = getNextNonDisabledIndex(1, 0, itemCount, function (index) {\n            return _this4.getItemNodeFromIndex(index);\n          }, false);\n          this.setHighlightedIndex(newHighlightedIndex, {\n            type: keyDownHome\n          });\n        },\n        End: function End(event) {\n          var _this5 = this;\n          var _this$getState4 = this.getState(),\n            isOpen = _this$getState4.isOpen;\n          if (!isOpen) {\n            return;\n          }\n          event.preventDefault();\n          var itemCount = this.getItemCount();\n          if (itemCount <= 0 || !isOpen) {\n            return;\n          }\n\n          // get next non-disabled starting upwards from last index if that's disabled.\n          var newHighlightedIndex = getNextNonDisabledIndex(-1, itemCount - 1, itemCount, function (index) {\n            return _this5.getItemNodeFromIndex(index);\n          }, false);\n          this.setHighlightedIndex(newHighlightedIndex, {\n            type: keyDownEnd\n          });\n        }\n      });\n      _this.getToggleButtonProps = function (_temp3) {\n        var _ref3 = _temp3 === void 0 ? {} : _temp3,\n          onClick = _ref3.onClick;\n          _ref3.onPress;\n          var onKeyDown = _ref3.onKeyDown,\n          onKeyUp = _ref3.onKeyUp,\n          onBlur = _ref3.onBlur,\n          rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, _excluded2$3);\n        var _this$getState5 = _this.getState(),\n          isOpen = _this$getState5.isOpen;\n        var enabledEventHandlers = {\n          onClick: callAllEventHandlers(onClick, _this.buttonHandleClick),\n          onKeyDown: callAllEventHandlers(onKeyDown, _this.buttonHandleKeyDown),\n          onKeyUp: callAllEventHandlers(onKeyUp, _this.buttonHandleKeyUp),\n          onBlur: callAllEventHandlers(onBlur, _this.buttonHandleBlur)\n        };\n        var eventHandlers = rest.disabled ? {} : enabledEventHandlers;\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          type: 'button',\n          role: 'button',\n          'aria-label': isOpen ? 'close menu' : 'open menu',\n          'aria-haspopup': true,\n          'data-toggle': true\n        }, eventHandlers, rest);\n      };\n      _this.buttonHandleKeyUp = function (event) {\n        // Prevent click event from emitting in Firefox\n        event.preventDefault();\n      };\n      _this.buttonHandleKeyDown = function (event) {\n        var key = normalizeArrowKey(event);\n        if (_this.buttonKeyDownHandlers[key]) {\n          _this.buttonKeyDownHandlers[key].call((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_this), event);\n        }\n      };\n      _this.buttonHandleClick = function (event) {\n        event.preventDefault();\n        // handle odd case for Safari and Firefox which\n        // don't give the button the focus properly.\n        /* istanbul ignore if (can't reasonably test this) */\n        if (_this.props.environment.document.activeElement === _this.props.environment.document.body) {\n          event.target.focus();\n        }\n        // to simplify testing components that use downshift, we'll not wrap this in a setTimeout\n        // if the NODE_ENV is test. With the proper build system, this should be dead code eliminated\n        // when building for production and should therefore have no impact on production code.\n        if (false) {} else {\n          // Ensure that toggle of menu occurs after the potential blur event in iOS\n          _this.internalSetTimeout(function () {\n            return _this.toggleMenu({\n              type: clickButton\n            });\n          });\n        }\n      };\n      _this.buttonHandleBlur = function (event) {\n        var blurTarget = event.target; // Save blur target for comparison with activeElement later\n        // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not body element\n        _this.internalSetTimeout(function () {\n          if (!_this.isMouseDown && (_this.props.environment.document.activeElement == null || _this.props.environment.document.activeElement.id !== _this.inputId) && _this.props.environment.document.activeElement !== blurTarget // Do nothing if we refocus the same element again (to solve issue in Safari on iOS)\n          ) {\n            _this.reset({\n              type: blurButton\n            });\n          }\n        });\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ BUTTON\n      /////////////////////////////// LABEL\n      _this.getLabelProps = function (props) {\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          htmlFor: _this.inputId,\n          id: _this.labelId\n        }, props);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ LABEL\n      /////////////////////////////// INPUT\n      _this.getInputProps = function (_temp4) {\n        var _ref4 = _temp4 === void 0 ? {} : _temp4,\n          onKeyDown = _ref4.onKeyDown,\n          onBlur = _ref4.onBlur,\n          onChange = _ref4.onChange,\n          onInput = _ref4.onInput;\n          _ref4.onChangeText;\n          var rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref4, _excluded3$2);\n        var onChangeKey;\n        var eventHandlers = {};\n\n        /* istanbul ignore next (preact) */\n        {\n          onChangeKey = 'onChange';\n        }\n        var _this$getState6 = _this.getState(),\n          inputValue = _this$getState6.inputValue,\n          isOpen = _this$getState6.isOpen,\n          highlightedIndex = _this$getState6.highlightedIndex;\n        if (!rest.disabled) {\n          var _eventHandlers;\n          eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, _this.inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, _this.inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, _this.inputHandleBlur), _eventHandlers);\n        }\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          'aria-autocomplete': 'list',\n          'aria-activedescendant': isOpen && typeof highlightedIndex === 'number' && highlightedIndex >= 0 ? _this.getItemId(highlightedIndex) : null,\n          'aria-controls': isOpen ? _this.menuId : null,\n          'aria-labelledby': rest && rest['aria-label'] ? undefined : _this.labelId,\n          // https://developer.mozilla.org/en-US/docs/Web/Security/Securing_your_site/Turning_off_form_autocompletion\n          // revert back since autocomplete=\"nope\" is ignored on latest Chrome and Opera\n          autoComplete: 'off',\n          value: inputValue,\n          id: _this.inputId\n        }, eventHandlers, rest);\n      };\n      _this.inputHandleKeyDown = function (event) {\n        var key = normalizeArrowKey(event);\n        if (key && _this.inputKeyDownHandlers[key]) {\n          _this.inputKeyDownHandlers[key].call((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_this), event);\n        }\n      };\n      _this.inputHandleChange = function (event) {\n        _this.internalSetState({\n          type: changeInput,\n          isOpen: true,\n          inputValue: event.target.value,\n          highlightedIndex: _this.props.defaultHighlightedIndex\n        });\n      };\n      _this.inputHandleBlur = function () {\n        // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not the body element\n        _this.internalSetTimeout(function () {\n          var downshiftButtonIsActive = _this.props.environment.document && !!_this.props.environment.document.activeElement && !!_this.props.environment.document.activeElement.dataset && _this.props.environment.document.activeElement.dataset.toggle && _this._rootNode && _this._rootNode.contains(_this.props.environment.document.activeElement);\n          if (!_this.isMouseDown && !downshiftButtonIsActive) {\n            _this.reset({\n              type: blurInput\n            });\n          }\n        });\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ INPUT\n      /////////////////////////////// MENU\n      _this.menuRef = function (node) {\n        _this._menuNode = node;\n      };\n      _this.getMenuProps = function (_temp5, _temp6) {\n        var _extends3;\n        var _ref5 = _temp5 === void 0 ? {} : _temp5,\n          _ref5$refKey = _ref5.refKey,\n          refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n          ref = _ref5.ref,\n          props = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref5, _excluded4$1);\n        var _ref6 = _temp6 === void 0 ? {} : _temp6,\n          _ref6$suppressRefErro = _ref6.suppressRefError,\n          suppressRefError = _ref6$suppressRefErro === void 0 ? false : _ref6$suppressRefErro;\n        _this.getMenuProps.called = true;\n        _this.getMenuProps.refKey = refKey;\n        _this.getMenuProps.suppressRefError = suppressRefError;\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, _this.menuRef), _extends3.role = 'listbox', _extends3['aria-labelledby'] = props && props['aria-label'] ? null : _this.labelId, _extends3.id = _this.menuId, _extends3), props);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ MENU\n      /////////////////////////////// ITEM\n      _this.getItemProps = function (_temp7) {\n        var _enabledEventHandlers;\n        var _ref7 = _temp7 === void 0 ? {} : _temp7,\n          onMouseMove = _ref7.onMouseMove,\n          onMouseDown = _ref7.onMouseDown,\n          onClick = _ref7.onClick;\n          _ref7.onPress;\n          var index = _ref7.index,\n          _ref7$item = _ref7.item,\n          item = _ref7$item === void 0 ?  false ? /* istanbul ignore next */0 : requiredProp('getItemProps', 'item') : _ref7$item,\n          rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref7, _excluded5);\n        if (index === undefined) {\n          _this.items.push(item);\n          index = _this.items.indexOf(item);\n        } else {\n          _this.items[index] = item;\n        }\n        var onSelectKey = 'onClick';\n        var customClickHandler = onClick;\n        var enabledEventHandlers = (_enabledEventHandlers = {\n          // onMouseMove is used over onMouseEnter here. onMouseMove\n          // is only triggered on actual mouse movement while onMouseEnter\n          // can fire on DOM changes, interrupting keyboard navigation\n          onMouseMove: callAllEventHandlers(onMouseMove, function () {\n            if (index === _this.getState().highlightedIndex) {\n              return;\n            }\n            _this.setHighlightedIndex(index, {\n              type: itemMouseEnter\n            });\n\n            // We never want to manually scroll when changing state based\n            // on `onMouseMove` because we will be moving the element out\n            // from under the user which is currently scrolling/moving the\n            // cursor\n            _this.avoidScrolling = true;\n            _this.internalSetTimeout(function () {\n              return _this.avoidScrolling = false;\n            }, 250);\n          }),\n          onMouseDown: callAllEventHandlers(onMouseDown, function (event) {\n            // This prevents the activeElement from being changed\n            // to the item so it can remain with the current activeElement\n            // which is a more common use case.\n            event.preventDefault();\n          })\n        }, _enabledEventHandlers[onSelectKey] = callAllEventHandlers(customClickHandler, function () {\n          _this.selectItemAtIndex(index, {\n            type: clickItem\n          });\n        }), _enabledEventHandlers);\n\n        // Passing down the onMouseDown handler to prevent redirect\n        // of the activeElement if clicking on disabled items\n        var eventHandlers = rest.disabled ? {\n          onMouseDown: enabledEventHandlers.onMouseDown\n        } : enabledEventHandlers;\n        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          id: _this.getItemId(index),\n          role: 'option',\n          'aria-selected': _this.getState().highlightedIndex === index\n        }, eventHandlers, rest);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ITEM\n      _this.clearItems = function () {\n        _this.items = [];\n      };\n      _this.reset = function (otherStateToSet, cb) {\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(function (_ref8) {\n          var selectedItem = _ref8.selectedItem;\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            isOpen: _this.props.defaultIsOpen,\n            highlightedIndex: _this.props.defaultHighlightedIndex,\n            inputValue: _this.props.itemToString(selectedItem)\n          }, otherStateToSet);\n        }, cb);\n      };\n      _this.toggleMenu = function (otherStateToSet, cb) {\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(function (_ref9) {\n          var isOpen = _ref9.isOpen;\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            isOpen: !isOpen\n          }, isOpen && {\n            highlightedIndex: _this.props.defaultHighlightedIndex\n          }, otherStateToSet);\n        }, function () {\n          var _this$getState7 = _this.getState(),\n            isOpen = _this$getState7.isOpen,\n            highlightedIndex = _this$getState7.highlightedIndex;\n          if (isOpen) {\n            if (_this.getItemCount() > 0 && typeof highlightedIndex === 'number') {\n              _this.setHighlightedIndex(highlightedIndex, otherStateToSet);\n            }\n          }\n          cbToCb(cb)();\n        });\n      };\n      _this.openMenu = function (cb) {\n        _this.internalSetState({\n          isOpen: true\n        }, cb);\n      };\n      _this.closeMenu = function (cb) {\n        _this.internalSetState({\n          isOpen: false\n        }, cb);\n      };\n      _this.updateStatus = debounce(function () {\n        var state = _this.getState();\n        var item = _this.items[state.highlightedIndex];\n        var resultCount = _this.getItemCount();\n        var status = _this.props.getA11yStatusMessage((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          itemToString: _this.props.itemToString,\n          previousResultCount: _this.previousResultCount,\n          resultCount: resultCount,\n          highlightedItem: item\n        }, state));\n        _this.previousResultCount = resultCount;\n        setStatus(status, _this.props.environment.document);\n      }, 200);\n      var _this$props = _this.props,\n        defaultHighlightedIndex = _this$props.defaultHighlightedIndex,\n        _this$props$initialHi = _this$props.initialHighlightedIndex,\n        _highlightedIndex = _this$props$initialHi === void 0 ? defaultHighlightedIndex : _this$props$initialHi,\n        defaultIsOpen = _this$props.defaultIsOpen,\n        _this$props$initialIs = _this$props.initialIsOpen,\n        _isOpen = _this$props$initialIs === void 0 ? defaultIsOpen : _this$props$initialIs,\n        _this$props$initialIn = _this$props.initialInputValue,\n        _inputValue = _this$props$initialIn === void 0 ? '' : _this$props$initialIn,\n        _this$props$initialSe = _this$props.initialSelectedItem,\n        _selectedItem = _this$props$initialSe === void 0 ? null : _this$props$initialSe;\n      var _state = _this.getState({\n        highlightedIndex: _highlightedIndex,\n        isOpen: _isOpen,\n        inputValue: _inputValue,\n        selectedItem: _selectedItem\n      });\n      if (_state.selectedItem != null && _this.props.initialInputValue === undefined) {\n        _state.inputValue = _this.props.itemToString(_state.selectedItem);\n      }\n      _this.state = _state;\n      return _this;\n    }\n    var _proto = Downshift.prototype;\n    /**\n     * Clear all running timeouts\n     */\n    _proto.internalClearTimeouts = function internalClearTimeouts() {\n      this.timeoutIds.forEach(function (id) {\n        clearTimeout(id);\n      });\n      this.timeoutIds = [];\n    }\n\n    /**\n     * Gets the state based on internal state or props\n     * If a state value is passed via props, then that\n     * is the value given, otherwise it's retrieved from\n     * stateToMerge\n     *\n     * @param {Object} stateToMerge defaults to this.state\n     * @return {Object} the state\n     */;\n    _proto.getState = function getState$1(stateToMerge) {\n      if (stateToMerge === void 0) {\n        stateToMerge = this.state;\n      }\n      return getState(stateToMerge, this.props);\n    };\n    _proto.getItemCount = function getItemCount() {\n      // things read better this way. They're in priority order:\n      // 1. `this.itemCount`\n      // 2. `this.props.itemCount`\n      // 3. `this.items.length`\n      var itemCount = this.items.length;\n      if (this.itemCount != null) {\n        itemCount = this.itemCount;\n      } else if (this.props.itemCount !== undefined) {\n        itemCount = this.props.itemCount;\n      }\n      return itemCount;\n    };\n    _proto.getItemNodeFromIndex = function getItemNodeFromIndex(index) {\n      return this.props.environment.document.getElementById(this.getItemId(index));\n    };\n    _proto.scrollHighlightedItemIntoView = function scrollHighlightedItemIntoView() {\n      /* istanbul ignore else (react-native) */\n      {\n        var node = this.getItemNodeFromIndex(this.getState().highlightedIndex);\n        this.props.scrollIntoView(node, this._menuNode);\n      }\n    };\n    _proto.moveHighlightedIndex = function moveHighlightedIndex(amount, otherStateToSet) {\n      var _this6 = this;\n      var itemCount = this.getItemCount();\n      var _this$getState8 = this.getState(),\n        highlightedIndex = _this$getState8.highlightedIndex;\n      if (itemCount > 0) {\n        var nextHighlightedIndex = getNextWrappingIndex(amount, highlightedIndex, itemCount, function (index) {\n          return _this6.getItemNodeFromIndex(index);\n        });\n        this.setHighlightedIndex(nextHighlightedIndex, otherStateToSet);\n      }\n    };\n    _proto.getStateAndHelpers = function getStateAndHelpers() {\n      var _this$getState9 = this.getState(),\n        highlightedIndex = _this$getState9.highlightedIndex,\n        inputValue = _this$getState9.inputValue,\n        selectedItem = _this$getState9.selectedItem,\n        isOpen = _this$getState9.isOpen;\n      var itemToString = this.props.itemToString;\n      var id = this.id;\n      var getRootProps = this.getRootProps,\n        getToggleButtonProps = this.getToggleButtonProps,\n        getLabelProps = this.getLabelProps,\n        getMenuProps = this.getMenuProps,\n        getInputProps = this.getInputProps,\n        getItemProps = this.getItemProps,\n        openMenu = this.openMenu,\n        closeMenu = this.closeMenu,\n        toggleMenu = this.toggleMenu,\n        selectItem = this.selectItem,\n        selectItemAtIndex = this.selectItemAtIndex,\n        selectHighlightedItem = this.selectHighlightedItem,\n        setHighlightedIndex = this.setHighlightedIndex,\n        clearSelection = this.clearSelection,\n        clearItems = this.clearItems,\n        reset = this.reset,\n        setItemCount = this.setItemCount,\n        unsetItemCount = this.unsetItemCount,\n        setState = this.internalSetState;\n      return {\n        // prop getters\n        getRootProps: getRootProps,\n        getToggleButtonProps: getToggleButtonProps,\n        getLabelProps: getLabelProps,\n        getMenuProps: getMenuProps,\n        getInputProps: getInputProps,\n        getItemProps: getItemProps,\n        // actions\n        reset: reset,\n        openMenu: openMenu,\n        closeMenu: closeMenu,\n        toggleMenu: toggleMenu,\n        selectItem: selectItem,\n        selectItemAtIndex: selectItemAtIndex,\n        selectHighlightedItem: selectHighlightedItem,\n        setHighlightedIndex: setHighlightedIndex,\n        clearSelection: clearSelection,\n        clearItems: clearItems,\n        setItemCount: setItemCount,\n        unsetItemCount: unsetItemCount,\n        setState: setState,\n        // props\n        itemToString: itemToString,\n        // derived\n        id: id,\n        // state\n        highlightedIndex: highlightedIndex,\n        inputValue: inputValue,\n        isOpen: isOpen,\n        selectedItem: selectedItem\n      };\n    };\n    _proto.componentDidMount = function componentDidMount() {\n      var _this7 = this;\n      /* istanbul ignore if (react-native) */\n      if ( true && this.getMenuProps.called && !this.getMenuProps.suppressRefError) {\n        validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);\n      }\n\n      /* istanbul ignore if (react-native) */\n      {\n        // this.isMouseDown helps us track whether the mouse is currently held down.\n        // This is useful when the user clicks on an item in the list, but holds the mouse\n        // down long enough for the list to disappear (because the blur event fires on the input)\n        // this.isMouseDown is used in the blur handler on the input to determine whether the blur event should\n        // trigger hiding the menu.\n        var onMouseDown = function onMouseDown() {\n          _this7.isMouseDown = true;\n        };\n        var onMouseUp = function onMouseUp(event) {\n          _this7.isMouseDown = false;\n          // if the target element or the activeElement is within a downshift node\n          // then we don't want to reset downshift\n          var contextWithinDownshift = targetWithinDownshift(event.target, [_this7._rootNode, _this7._menuNode], _this7.props.environment);\n          if (!contextWithinDownshift && _this7.getState().isOpen) {\n            _this7.reset({\n              type: mouseUp\n            }, function () {\n              return _this7.props.onOuterClick(_this7.getStateAndHelpers());\n            });\n          }\n        };\n        // Touching an element in iOS gives focus and hover states, but touching out of\n        // the element will remove hover, and persist the focus state, resulting in the\n        // blur event not being triggered.\n        // this.isTouchMove helps us track whether the user is tapping or swiping on a touch screen.\n        // If the user taps outside of Downshift, the component should be reset,\n        // but not if the user is swiping\n        var onTouchStart = function onTouchStart() {\n          _this7.isTouchMove = false;\n        };\n        var onTouchMove = function onTouchMove() {\n          _this7.isTouchMove = true;\n        };\n        var onTouchEnd = function onTouchEnd(event) {\n          var contextWithinDownshift = targetWithinDownshift(event.target, [_this7._rootNode, _this7._menuNode], _this7.props.environment, false);\n          if (!_this7.isTouchMove && !contextWithinDownshift && _this7.getState().isOpen) {\n            _this7.reset({\n              type: touchEnd\n            }, function () {\n              return _this7.props.onOuterClick(_this7.getStateAndHelpers());\n            });\n          }\n        };\n        var environment = this.props.environment;\n        environment.addEventListener('mousedown', onMouseDown);\n        environment.addEventListener('mouseup', onMouseUp);\n        environment.addEventListener('touchstart', onTouchStart);\n        environment.addEventListener('touchmove', onTouchMove);\n        environment.addEventListener('touchend', onTouchEnd);\n        this.cleanup = function () {\n          _this7.internalClearTimeouts();\n          _this7.updateStatus.cancel();\n          environment.removeEventListener('mousedown', onMouseDown);\n          environment.removeEventListener('mouseup', onMouseUp);\n          environment.removeEventListener('touchstart', onTouchStart);\n          environment.removeEventListener('touchmove', onTouchMove);\n          environment.removeEventListener('touchend', onTouchEnd);\n        };\n      }\n    };\n    _proto.shouldScroll = function shouldScroll(prevState, prevProps) {\n      var _ref10 = this.props.highlightedIndex === undefined ? this.getState() : this.props,\n        currentHighlightedIndex = _ref10.highlightedIndex;\n      var _ref11 = prevProps.highlightedIndex === undefined ? prevState : prevProps,\n        prevHighlightedIndex = _ref11.highlightedIndex;\n      var scrollWhenOpen = currentHighlightedIndex && this.getState().isOpen && !prevState.isOpen;\n      var scrollWhenNavigating = currentHighlightedIndex !== prevHighlightedIndex;\n      return scrollWhenOpen || scrollWhenNavigating;\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n      if (true) {\n        validateControlledUnchanged(this.state, prevProps, this.props);\n        /* istanbul ignore if (react-native) */\n        if (this.getMenuProps.called && !this.getMenuProps.suppressRefError) {\n          validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);\n        }\n      }\n      if (isControlledProp(this.props, 'selectedItem') && this.props.selectedItemChanged(prevProps.selectedItem, this.props.selectedItem)) {\n        this.internalSetState({\n          type: controlledPropUpdatedSelectedItem,\n          inputValue: this.props.itemToString(this.props.selectedItem)\n        });\n      }\n      if (!this.avoidScrolling && this.shouldScroll(prevState, prevProps)) {\n        this.scrollHighlightedItemIntoView();\n      }\n\n      /* istanbul ignore else (react-native) */\n      {\n        this.updateStatus();\n      }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.cleanup(); // avoids memory leak\n    };\n    _proto.render = function render() {\n      var children = unwrapArray(this.props.children, noop);\n      // because the items are rerendered every time we call the children\n      // we clear this out each render and it will be populated again as\n      // getItemProps is called.\n      this.clearItems();\n      // we reset this so we know whether the user calls getRootProps during\n      // this render. If they do then we don't need to do anything,\n      // if they don't then we need to clone the element they return and\n      // apply the props for them.\n      this.getRootProps.called = false;\n      this.getRootProps.refKey = undefined;\n      this.getRootProps.suppressRefError = undefined;\n      // we do something similar for getMenuProps\n      this.getMenuProps.called = false;\n      this.getMenuProps.refKey = undefined;\n      this.getMenuProps.suppressRefError = undefined;\n      // we do something similar for getLabelProps\n      this.getLabelProps.called = false;\n      // and something similar for getInputProps\n      this.getInputProps.called = false;\n      var element = unwrapArray(children(this.getStateAndHelpers()));\n      if (!element) {\n        return null;\n      }\n      if (this.getRootProps.called || this.props.suppressRefError) {\n        if ( true && !this.getRootProps.suppressRefError && !this.props.suppressRefError) {\n          validateGetRootPropsCalledCorrectly(element, this.getRootProps);\n        }\n        return element;\n      } else if (isDOMElement(element)) {\n        // they didn't apply the root props, but we can clone\n        // this and apply the props ourselves\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_4__.cloneElement)(element, this.getRootProps(getElementProps(element)));\n      }\n\n      /* istanbul ignore else */\n      if (true) {\n        // they didn't apply the root props, but they need to\n        // otherwise we can't query around the autocomplete\n\n        throw new Error('downshift: If you return a non-DOM element, you must apply the getRootProps function');\n      }\n\n      /* istanbul ignore next */\n      return undefined;\n    };\n    return Downshift;\n  }(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n  Downshift.defaultProps = {\n    defaultHighlightedIndex: null,\n    defaultIsOpen: false,\n    getA11yStatusMessage: getA11yStatusMessage$1,\n    itemToString: function itemToString(i) {\n      if (i == null) {\n        return '';\n      }\n      if ( true && isPlainObject(i) && !i.hasOwnProperty('toString')) {\n        // eslint-disable-next-line no-console\n        console.warn('downshift: An object was passed to the default implementation of `itemToString`. You should probably provide your own `itemToString` implementation. Please refer to the `itemToString` API documentation.', 'The object that was passed:', i);\n      }\n      return String(i);\n    },\n    onStateChange: noop,\n    onInputValueChange: noop,\n    onUserAction: noop,\n    onChange: noop,\n    onSelect: noop,\n    onOuterClick: noop,\n    selectedItemChanged: function selectedItemChanged(prevItem, item) {\n      return prevItem !== item;\n    },\n    environment: /* istanbul ignore next (ssr) */\n    typeof window === 'undefined' ? {} : window,\n    stateReducer: function stateReducer(state, stateToSet) {\n      return stateToSet;\n    },\n    suppressRefError: false,\n    scrollIntoView: scrollIntoView\n  };\n  Downshift.stateChangeTypes = stateChangeTypes$3;\n  return Downshift;\n}();\n true ? Downshift.propTypes = {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  defaultHighlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  defaultIsOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  initialHighlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  initialSelectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n  initialInputValue: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  initialIsOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  getA11yStatusMessage: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  itemToString: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onSelect: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onStateChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onInputValueChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onUserAction: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onOuterClick: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  selectedItemChanged: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  stateReducer: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  itemCount: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  id: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  environment: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n    addEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    removeEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    document: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n      getElementById: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n      activeElement: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n      body: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any)\n    })\n  }),\n  suppressRefError: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  scrollIntoView: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  // things we keep in state for uncontrolled components\n  // but can accept as props for controlled components\n  /* eslint-disable react/no-unused-prop-types */\n  selectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n  isOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  inputValue: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  highlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  labelId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  inputId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  menuId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  getItemId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func)\n  /* eslint-enable react/no-unused-prop-types */\n} : 0;\nvar Downshift$1 = Downshift;\nfunction validateGetMenuPropsCalledCorrectly(node, _ref12) {\n  var refKey = _ref12.refKey;\n  if (!node) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: The ref prop \\\"\" + refKey + \"\\\" from getMenuProps was not applied correctly on your menu element.\");\n  }\n}\nfunction validateGetRootPropsCalledCorrectly(element, _ref13) {\n  var refKey = _ref13.refKey;\n  var refKeySpecified = refKey !== 'ref';\n  var isComposite = !isDOMElement(element);\n  if (isComposite && !refKeySpecified && !(0,react_is__WEBPACK_IMPORTED_MODULE_5__.isForwardRef)(element)) {\n    // eslint-disable-next-line no-console\n    console.error('downshift: You returned a non-DOM element. You must specify a refKey in getRootProps');\n  } else if (!isComposite && refKeySpecified) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: You returned a DOM element. You should not specify a refKey in getRootProps. You specified \\\"\" + refKey + \"\\\"\");\n  }\n  if (!(0,react_is__WEBPACK_IMPORTED_MODULE_5__.isForwardRef)(element) && !getElementProps(element)[refKey]) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: You must apply the ref prop \\\"\" + refKey + \"\\\" from getRootProps onto your root element.\");\n  }\n}\n\nvar _excluded$3 = [\"isInitialMount\", \"highlightedIndex\", \"items\", \"environment\"];\nvar dropdownDefaultStateValues = {\n  highlightedIndex: -1,\n  isOpen: false,\n  selectedItem: null,\n  inputValue: ''\n};\nfunction callOnChangeProps(action, state, newState) {\n  var props = action.props,\n    type = action.type;\n  var changes = {};\n  Object.keys(state).forEach(function (key) {\n    invokeOnChangeHandler(key, action, state, newState);\n    if (newState[key] !== state[key]) {\n      changes[key] = newState[key];\n    }\n  });\n  if (props.onStateChange && Object.keys(changes).length) {\n    props.onStateChange((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      type: type\n    }, changes));\n  }\n}\nfunction invokeOnChangeHandler(key, action, state, newState) {\n  var props = action.props,\n    type = action.type;\n  var handler = \"on\" + capitalizeString(key) + \"Change\";\n  if (props[handler] && newState[key] !== undefined && newState[key] !== state[key]) {\n    props[handler]((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      type: type\n    }, newState));\n  }\n}\n\n/**\n * Default state reducer that returns the changes.\n *\n * @param {Object} s state.\n * @param {Object} a action with changes.\n * @returns {Object} changes.\n */\nfunction stateReducer(s, a) {\n  return a.changes;\n}\n\n/**\n * Returns a message to be added to aria-live region when item is selected.\n *\n * @param {Object} selectionParameters Parameters required to build the message.\n * @returns {string} The a11y message.\n */\nfunction getA11ySelectionMessage(selectionParameters) {\n  var selectedItem = selectionParameters.selectedItem,\n    itemToStringLocal = selectionParameters.itemToString;\n  return selectedItem ? itemToStringLocal(selectedItem) + \" has been selected.\" : '';\n}\n\n/**\n * Debounced call for updating the a11y message.\n */\nvar updateA11yStatus = debounce(function (getA11yMessage, document) {\n  setStatus(getA11yMessage(), document);\n}, 200);\n\n// istanbul ignore next\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_4__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_4__.useEffect;\nfunction useElementIds(_ref) {\n  var _ref$id = _ref.id,\n    id = _ref$id === void 0 ? \"downshift-\" + generateId() : _ref$id,\n    labelId = _ref.labelId,\n    menuId = _ref.menuId,\n    getItemId = _ref.getItemId,\n    toggleButtonId = _ref.toggleButtonId,\n    inputId = _ref.inputId;\n  var elementIdsRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({\n    labelId: labelId || id + \"-label\",\n    menuId: menuId || id + \"-menu\",\n    getItemId: getItemId || function (index) {\n      return id + \"-item-\" + index;\n    },\n    toggleButtonId: toggleButtonId || id + \"-toggle-button\",\n    inputId: inputId || id + \"-input\"\n  });\n  return elementIdsRef.current;\n}\nfunction getItemAndIndex(itemProp, indexProp, items, errorMessage) {\n  var item, index;\n  if (itemProp === undefined) {\n    if (indexProp === undefined) {\n      throw new Error(errorMessage);\n    }\n    item = items[indexProp];\n    index = indexProp;\n  } else {\n    index = indexProp === undefined ? items.indexOf(itemProp) : indexProp;\n    item = itemProp;\n  }\n  return [item, index];\n}\nfunction itemToString(item) {\n  return item ? String(item) : '';\n}\nfunction isAcceptedCharacterKey(key) {\n  return /^\\S{1}$/.test(key);\n}\nfunction capitalizeString(string) {\n  return \"\" + string.slice(0, 1).toUpperCase() + string.slice(1);\n}\nfunction useLatestRef(val) {\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(val);\n  // technically this is not \"concurrent mode safe\" because we're manipulating\n  // the value during render (so it's not idempotent). However, the places this\n  // hook is used is to support memoizing callbacks which will be called\n  // *during* render, so we need the latest values *during* render.\n  // If not for this, then we'd probably want to use useLayoutEffect instead.\n  ref.current = val;\n  return ref;\n}\n\n/**\n * Computes the controlled state using a the previous state, props,\n * two reducers, one from downshift and an optional one from the user.\n * Also calls the onChange handlers for state values that have changed.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useEnhancedReducer(reducer, initialState, props) {\n  var prevStateRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  var actionRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  var enhancedReducer = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (state, action) {\n    actionRef.current = action;\n    state = getState(state, action.props);\n    var changes = reducer(state, action);\n    var newState = action.props.stateReducer(state, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, action, {\n      changes: changes\n    }));\n    return newState;\n  }, [reducer]);\n  var _useReducer = (0,react__WEBPACK_IMPORTED_MODULE_4__.useReducer)(enhancedReducer, initialState),\n    state = _useReducer[0],\n    dispatch = _useReducer[1];\n  var propsRef = useLatestRef(props);\n  var dispatchWithProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (action) {\n    return dispatch((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      props: propsRef.current\n    }, action));\n  }, [propsRef]);\n  var action = actionRef.current;\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (action && prevStateRef.current && prevStateRef.current !== state) {\n      callOnChangeProps(action, getState(prevStateRef.current, action.props), state);\n    }\n    prevStateRef.current = state;\n  }, [state, props, action]);\n  return [state, dispatchWithProps];\n}\n\n/**\n * Wraps the useEnhancedReducer and applies the controlled prop values before\n * returning the new state.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useControlledReducer$1(reducer, initialState, props) {\n  var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props),\n    state = _useEnhancedReducer[0],\n    dispatch = _useEnhancedReducer[1];\n  return [getState(state, props), dispatch];\n}\nvar defaultProps$3 = {\n  itemToString: itemToString,\n  stateReducer: stateReducer,\n  getA11ySelectionMessage: getA11ySelectionMessage,\n  scrollIntoView: scrollIntoView,\n  environment: /* istanbul ignore next (ssr) */\n  typeof window === 'undefined' ? {} : window\n};\nfunction getDefaultValue$1(props, propKey, defaultStateValues) {\n  if (defaultStateValues === void 0) {\n    defaultStateValues = dropdownDefaultStateValues;\n  }\n  var defaultValue = props[\"default\" + capitalizeString(propKey)];\n  if (defaultValue !== undefined) {\n    return defaultValue;\n  }\n  return defaultStateValues[propKey];\n}\nfunction getInitialValue$1(props, propKey, defaultStateValues) {\n  if (defaultStateValues === void 0) {\n    defaultStateValues = dropdownDefaultStateValues;\n  }\n  var value = props[propKey];\n  if (value !== undefined) {\n    return value;\n  }\n  var initialValue = props[\"initial\" + capitalizeString(propKey)];\n  if (initialValue !== undefined) {\n    return initialValue;\n  }\n  return getDefaultValue$1(props, propKey, defaultStateValues);\n}\nfunction getInitialState$2(props) {\n  var selectedItem = getInitialValue$1(props, 'selectedItem');\n  var isOpen = getInitialValue$1(props, 'isOpen');\n  var highlightedIndex = getInitialValue$1(props, 'highlightedIndex');\n  var inputValue = getInitialValue$1(props, 'inputValue');\n  return {\n    highlightedIndex: highlightedIndex < 0 && selectedItem && isOpen ? props.items.indexOf(selectedItem) : highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\nfunction getHighlightedIndexOnOpen(props, state, offset) {\n  var items = props.items,\n    initialHighlightedIndex = props.initialHighlightedIndex,\n    defaultHighlightedIndex = props.defaultHighlightedIndex;\n  var selectedItem = state.selectedItem,\n    highlightedIndex = state.highlightedIndex;\n  if (items.length === 0) {\n    return -1;\n  }\n\n  // initialHighlightedIndex will give value to highlightedIndex on initial state only.\n  if (initialHighlightedIndex !== undefined && highlightedIndex === initialHighlightedIndex) {\n    return initialHighlightedIndex;\n  }\n  if (defaultHighlightedIndex !== undefined) {\n    return defaultHighlightedIndex;\n  }\n  if (selectedItem) {\n    return items.indexOf(selectedItem);\n  }\n  if (offset === 0) {\n    return -1;\n  }\n  return offset < 0 ? items.length - 1 : 0;\n}\n\n/**\n * Reuse the movement tracking of mouse and touch events.\n *\n * @param {boolean} isOpen Whether the dropdown is open or not.\n * @param {Array<Object>} downshiftElementRefs Downshift element refs to track movement (toggleButton, menu etc.)\n * @param {Object} environment Environment where component/hook exists.\n * @param {Function} handleBlur Handler on blur from mouse or touch.\n * @returns {Object} Ref containing whether mouseDown or touchMove event is happening\n */\nfunction useMouseAndTouchTracker(isOpen, downshiftElementRefs, environment, handleBlur) {\n  var mouseAndTouchTrackersRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({\n    isMouseDown: false,\n    isTouchMove: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if ((environment == null ? void 0 : environment.addEventListener) == null) {\n      return;\n    }\n\n    // The same strategy for checking if a click occurred inside or outside downshift\n    // as in downshift.js.\n    var onMouseDown = function onMouseDown() {\n      mouseAndTouchTrackersRef.current.isMouseDown = true;\n    };\n    var onMouseUp = function onMouseUp(event) {\n      mouseAndTouchTrackersRef.current.isMouseDown = false;\n      if (isOpen && !targetWithinDownshift(event.target, downshiftElementRefs.map(function (ref) {\n        return ref.current;\n      }), environment)) {\n        handleBlur();\n      }\n    };\n    var onTouchStart = function onTouchStart() {\n      mouseAndTouchTrackersRef.current.isTouchMove = false;\n    };\n    var onTouchMove = function onTouchMove() {\n      mouseAndTouchTrackersRef.current.isTouchMove = true;\n    };\n    var onTouchEnd = function onTouchEnd(event) {\n      if (isOpen && !mouseAndTouchTrackersRef.current.isTouchMove && !targetWithinDownshift(event.target, downshiftElementRefs.map(function (ref) {\n        return ref.current;\n      }), environment, false)) {\n        handleBlur();\n      }\n    };\n    environment.addEventListener('mousedown', onMouseDown);\n    environment.addEventListener('mouseup', onMouseUp);\n    environment.addEventListener('touchstart', onTouchStart);\n    environment.addEventListener('touchmove', onTouchMove);\n    environment.addEventListener('touchend', onTouchEnd);\n\n    // eslint-disable-next-line consistent-return\n    return function cleanup() {\n      environment.removeEventListener('mousedown', onMouseDown);\n      environment.removeEventListener('mouseup', onMouseUp);\n      environment.removeEventListener('touchstart', onTouchStart);\n      environment.removeEventListener('touchmove', onTouchMove);\n      environment.removeEventListener('touchend', onTouchEnd);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isOpen, environment]);\n  return mouseAndTouchTrackersRef;\n}\n\n/* istanbul ignore next */\n// eslint-disable-next-line import/no-mutable-exports\nvar useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {\n  return noop;\n};\n/**\n * Custom hook that checks if getter props are called correctly.\n *\n * @param  {...any} propKeys Getter prop names to be handled.\n * @returns {Function} Setter function called inside getter props to set call information.\n */\n/* istanbul ignore next */\nif (true) {\n  useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {\n    var isInitialMountRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(true);\n    for (var _len = arguments.length, propKeys = new Array(_len), _key = 0; _key < _len; _key++) {\n      propKeys[_key] = arguments[_key];\n    }\n    var getterPropsCalledRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(propKeys.reduce(function (acc, propKey) {\n      acc[propKey] = {};\n      return acc;\n    }, {}));\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n      Object.keys(getterPropsCalledRef.current).forEach(function (propKey) {\n        var propCallInfo = getterPropsCalledRef.current[propKey];\n        if (isInitialMountRef.current) {\n          if (!Object.keys(propCallInfo).length) {\n            // eslint-disable-next-line no-console\n            console.error(\"downshift: You forgot to call the \" + propKey + \" getter function on your component / element.\");\n            return;\n          }\n        }\n        var suppressRefError = propCallInfo.suppressRefError,\n          refKey = propCallInfo.refKey,\n          elementRef = propCallInfo.elementRef;\n        if ((!elementRef || !elementRef.current) && !suppressRefError) {\n          // eslint-disable-next-line no-console\n          console.error(\"downshift: The ref prop \\\"\" + refKey + \"\\\" from \" + propKey + \" was not applied correctly on your element.\");\n        }\n      });\n      isInitialMountRef.current = false;\n    });\n    var setGetterPropCallInfo = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (propKey, suppressRefError, refKey, elementRef) {\n      getterPropsCalledRef.current[propKey] = {\n        suppressRefError: suppressRefError,\n        refKey: refKey,\n        elementRef: elementRef\n      };\n    }, []);\n    return setGetterPropCallInfo;\n  };\n}\nfunction useA11yMessageSetter(getA11yMessage, dependencyArray, _ref2) {\n  var isInitialMount = _ref2.isInitialMount,\n    highlightedIndex = _ref2.highlightedIndex,\n    items = _ref2.items,\n    environment = _ref2.environment,\n    rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref2, _excluded$3);\n  // Sets a11y status message on changes in state.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (isInitialMount || false) {\n      return;\n    }\n    updateA11yStatus(function () {\n      return getA11yMessage((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        highlightedIndex: highlightedIndex,\n        highlightedItem: items[highlightedIndex],\n        resultCount: items.length\n      }, rest));\n    }, environment.document);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencyArray);\n}\nfunction useScrollIntoView(_ref3) {\n  var highlightedIndex = _ref3.highlightedIndex,\n    isOpen = _ref3.isOpen,\n    itemRefs = _ref3.itemRefs,\n    getItemNodeFromIndex = _ref3.getItemNodeFromIndex,\n    menuElement = _ref3.menuElement,\n    scrollIntoViewProp = _ref3.scrollIntoView;\n  // used not to scroll on highlight by mouse.\n  var shouldScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(true);\n  // Scroll on highlighted item if change comes from keyboard.\n  useIsomorphicLayoutEffect(function () {\n    if (highlightedIndex < 0 || !isOpen || !Object.keys(itemRefs.current).length) {\n      return;\n    }\n    if (shouldScrollRef.current === false) {\n      shouldScrollRef.current = true;\n    } else {\n      scrollIntoViewProp(getItemNodeFromIndex(highlightedIndex), menuElement);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [highlightedIndex]);\n  return shouldScrollRef;\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar useControlPropsValidator = noop;\n/* istanbul ignore next */\nif (true) {\n  useControlPropsValidator = function useControlPropsValidator(_ref4) {\n    var isInitialMount = _ref4.isInitialMount,\n      props = _ref4.props,\n      state = _ref4.state;\n    // used for checking when props are moving from controlled to uncontrolled.\n    var prevPropsRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(props);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n      if (isInitialMount) {\n        return;\n      }\n      validateControlledUnchanged(state, prevPropsRef.current, props);\n      prevPropsRef.current = props;\n    }, [state, props, isInitialMount]);\n  };\n}\n\n/**\n * Handles selection on Enter / Alt + ArrowUp. Closes the menu and resets the highlighted index, unless there is a highlighted.\n * In that case, selects the item and resets to defaults for open state and highlighted idex.\n * @param {Object} props The useCombobox props.\n * @param {number} highlightedIndex The index from the state.\n * @param {boolean} inputValue Also return the input value for state.\n * @returns The changes for the state.\n */\nfunction getChangesOnSelection(props, highlightedIndex, inputValue) {\n  var _props$items;\n  if (inputValue === void 0) {\n    inputValue = true;\n  }\n  var shouldSelect = ((_props$items = props.items) == null ? void 0 : _props$items.length) && highlightedIndex >= 0;\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    isOpen: false,\n    highlightedIndex: -1\n  }, shouldSelect && (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    selectedItem: props.items[highlightedIndex],\n    isOpen: getDefaultValue$1(props, 'isOpen'),\n    highlightedIndex: getDefaultValue$1(props, 'highlightedIndex')\n  }, inputValue && {\n    inputValue: props.itemToString(props.items[highlightedIndex])\n  }));\n}\n\nfunction downshiftCommonReducer(state, action, stateChangeTypes) {\n  var type = action.type,\n    props = action.props;\n  var changes;\n  switch (type) {\n    case stateChangeTypes.ItemMouseMove:\n      changes = {\n        highlightedIndex: action.disabled ? -1 : action.index\n      };\n      break;\n    case stateChangeTypes.MenuMouseLeave:\n      changes = {\n        highlightedIndex: -1\n      };\n      break;\n    case stateChangeTypes.ToggleButtonClick:\n    case stateChangeTypes.FunctionToggleMenu:\n      changes = {\n        isOpen: !state.isOpen,\n        highlightedIndex: state.isOpen ? -1 : getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case stateChangeTypes.FunctionOpenMenu:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case stateChangeTypes.FunctionCloseMenu:\n      changes = {\n        isOpen: false\n      };\n      break;\n    case stateChangeTypes.FunctionSetHighlightedIndex:\n      changes = {\n        highlightedIndex: action.highlightedIndex\n      };\n      break;\n    case stateChangeTypes.FunctionSetInputValue:\n      changes = {\n        inputValue: action.inputValue\n      };\n      break;\n    case stateChangeTypes.FunctionReset:\n      changes = {\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        selectedItem: getDefaultValue$1(props, 'selectedItem'),\n        inputValue: getDefaultValue$1(props, 'inputValue')\n      };\n      break;\n    default:\n      throw new Error('Reducer called without proper action type.');\n  }\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, state, changes);\n}\n/* eslint-enable complexity */\n\nfunction getItemIndexByCharacterKey(_a) {\n    var keysSoFar = _a.keysSoFar, highlightedIndex = _a.highlightedIndex, items = _a.items, itemToString = _a.itemToString, getItemNodeFromIndex = _a.getItemNodeFromIndex;\n    var lowerCasedKeysSoFar = keysSoFar.toLowerCase();\n    for (var index = 0; index < items.length; index++) {\n        // if we already have a search query in progress, we also consider the current highlighted item.\n        var offsetIndex = (index + highlightedIndex + (keysSoFar.length < 2 ? 1 : 0)) % items.length;\n        var item = items[offsetIndex];\n        if (item !== undefined &&\n            itemToString(item).toLowerCase().startsWith(lowerCasedKeysSoFar)) {\n            var element = getItemNodeFromIndex(offsetIndex);\n            if (!(element === null || element === void 0 ? void 0 : element.hasAttribute('disabled'))) {\n                return offsetIndex;\n            }\n        }\n    }\n    return highlightedIndex;\n}\nvar propTypes$2 = {\n    items: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().array).isRequired,\n    itemToString: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    getA11yStatusMessage: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    getA11ySelectionMessage: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    highlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n    defaultHighlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n    initialHighlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n    isOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n    defaultIsOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n    initialIsOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n    selectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n    initialSelectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n    defaultSelectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n    id: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n    labelId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n    menuId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n    getItemId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    toggleButtonId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n    stateReducer: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    onSelectedItemChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    onHighlightedIndexChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    onStateChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    onIsOpenChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    environment: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n        addEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n        removeEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n        document: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n            getElementById: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n            activeElement: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n            body: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any)\n        })\n    })\n};\n/**\n * Default implementation for status message. Only added when menu is open.\n * Will specift if there are results in the list, and if so, how many,\n * and what keys are relevant.\n *\n * @param {Object} param the downshift state and other relevant properties\n * @return {String} the a11y status message\n */\nfunction getA11yStatusMessage(_a) {\n    var isOpen = _a.isOpen, resultCount = _a.resultCount, previousResultCount = _a.previousResultCount;\n    if (!isOpen) {\n        return '';\n    }\n    if (!resultCount) {\n        return 'No results are available.';\n    }\n    if (resultCount !== previousResultCount) {\n        return \"\".concat(resultCount, \" result\").concat(resultCount === 1 ? ' is' : 's are', \" available, use up and down arrow keys to navigate. Press Enter or Space Bar keys to select.\");\n    }\n    return '';\n}\nvar defaultProps$2 = (0,tslib__WEBPACK_IMPORTED_MODULE_8__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_8__.__assign)({}, defaultProps$3), { getA11yStatusMessage: getA11yStatusMessage });\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes$2 = noop;\n/* istanbul ignore next */\nif (true) {\n    validatePropTypes$2 = function (options, caller) {\n        prop_types__WEBPACK_IMPORTED_MODULE_7___default().checkPropTypes(propTypes$2, options, 'prop', caller.name);\n    };\n}\n\nvar ToggleButtonClick$1 =  true ? '__togglebutton_click__' : 0;\nvar ToggleButtonKeyDownArrowDown =  true ? '__togglebutton_keydown_arrow_down__' : 0;\nvar ToggleButtonKeyDownArrowUp =  true ? '__togglebutton_keydown_arrow_up__' : 0;\nvar ToggleButtonKeyDownCharacter =  true ? '__togglebutton_keydown_character__' : 0;\nvar ToggleButtonKeyDownEscape =  true ? '__togglebutton_keydown_escape__' : 0;\nvar ToggleButtonKeyDownHome =  true ? '__togglebutton_keydown_home__' : 0;\nvar ToggleButtonKeyDownEnd =  true ? '__togglebutton_keydown_end__' : 0;\nvar ToggleButtonKeyDownEnter =  true ? '__togglebutton_keydown_enter__' : 0;\nvar ToggleButtonKeyDownSpaceButton =  true ? '__togglebutton_keydown_space_button__' : 0;\nvar ToggleButtonKeyDownPageUp =  true ? '__togglebutton_keydown_page_up__' : 0;\nvar ToggleButtonKeyDownPageDown =  true ? '__togglebutton_keydown_page_down__' : 0;\nvar ToggleButtonBlur =  true ? '__togglebutton_blur__' : 0;\nvar MenuMouseLeave$1 =  true ? '__menu_mouse_leave__' : 0;\nvar ItemMouseMove$1 =  true ? '__item_mouse_move__' : 0;\nvar ItemClick$1 =  true ? '__item_click__' : 0;\nvar FunctionToggleMenu$1 =  true ? '__function_toggle_menu__' : 0;\nvar FunctionOpenMenu$1 =  true ? '__function_open_menu__' : 0;\nvar FunctionCloseMenu$1 =  true ? '__function_close_menu__' : 0;\nvar FunctionSetHighlightedIndex$1 =  true ? '__function_set_highlighted_index__' : 0;\nvar FunctionSelectItem$1 =  true ? '__function_select_item__' : 0;\nvar FunctionSetInputValue$1 =  true ? '__function_set_input_value__' : 0;\nvar FunctionReset$2 =  true ? '__function_reset__' : 0;\n\nvar stateChangeTypes$2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ToggleButtonClick: ToggleButtonClick$1,\n  ToggleButtonKeyDownArrowDown: ToggleButtonKeyDownArrowDown,\n  ToggleButtonKeyDownArrowUp: ToggleButtonKeyDownArrowUp,\n  ToggleButtonKeyDownCharacter: ToggleButtonKeyDownCharacter,\n  ToggleButtonKeyDownEscape: ToggleButtonKeyDownEscape,\n  ToggleButtonKeyDownHome: ToggleButtonKeyDownHome,\n  ToggleButtonKeyDownEnd: ToggleButtonKeyDownEnd,\n  ToggleButtonKeyDownEnter: ToggleButtonKeyDownEnter,\n  ToggleButtonKeyDownSpaceButton: ToggleButtonKeyDownSpaceButton,\n  ToggleButtonKeyDownPageUp: ToggleButtonKeyDownPageUp,\n  ToggleButtonKeyDownPageDown: ToggleButtonKeyDownPageDown,\n  ToggleButtonBlur: ToggleButtonBlur,\n  MenuMouseLeave: MenuMouseLeave$1,\n  ItemMouseMove: ItemMouseMove$1,\n  ItemClick: ItemClick$1,\n  FunctionToggleMenu: FunctionToggleMenu$1,\n  FunctionOpenMenu: FunctionOpenMenu$1,\n  FunctionCloseMenu: FunctionCloseMenu$1,\n  FunctionSetHighlightedIndex: FunctionSetHighlightedIndex$1,\n  FunctionSelectItem: FunctionSelectItem$1,\n  FunctionSetInputValue: FunctionSetInputValue$1,\n  FunctionReset: FunctionReset$2\n});\n\n/* eslint-disable complexity */\nfunction downshiftSelectReducer(state, action) {\n  var _props$items;\n  var type = action.type,\n    props = action.props,\n    altKey = action.altKey;\n  var changes;\n  switch (type) {\n    case ItemClick$1:\n      changes = {\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        selectedItem: props.items[action.index]\n      };\n      break;\n    case ToggleButtonKeyDownCharacter:\n      {\n        var lowercasedKey = action.key;\n        var inputValue = \"\" + state.inputValue + lowercasedKey;\n        var prevHighlightedIndex = !state.isOpen && state.selectedItem ? props.items.indexOf(state.selectedItem) : state.highlightedIndex;\n        var highlightedIndex = getItemIndexByCharacterKey({\n          keysSoFar: inputValue,\n          highlightedIndex: prevHighlightedIndex,\n          items: props.items,\n          itemToString: props.itemToString,\n          getItemNodeFromIndex: action.getItemNodeFromIndex\n        });\n        changes = {\n          inputValue: inputValue,\n          highlightedIndex: highlightedIndex,\n          isOpen: true\n        };\n      }\n      break;\n    case ToggleButtonKeyDownArrowDown:\n      {\n        var _highlightedIndex = state.isOpen ? getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1);\n        changes = {\n          highlightedIndex: _highlightedIndex,\n          isOpen: true\n        };\n      }\n      break;\n    case ToggleButtonKeyDownArrowUp:\n      if (state.isOpen && altKey) {\n        changes = getChangesOnSelection(props, state.highlightedIndex, false);\n      } else {\n        var _highlightedIndex2 = state.isOpen ? getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : getHighlightedIndexOnOpen(props, state, -1);\n        changes = {\n          highlightedIndex: _highlightedIndex2,\n          isOpen: true\n        };\n      }\n      break;\n    // only triggered when menu is open.\n    case ToggleButtonKeyDownEnter:\n    case ToggleButtonKeyDownSpaceButton:\n      changes = getChangesOnSelection(props, state.highlightedIndex, false);\n      break;\n    case ToggleButtonKeyDownHome:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false),\n        isOpen: true\n      };\n      break;\n    case ToggleButtonKeyDownEnd:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false),\n        isOpen: true\n      };\n      break;\n    case ToggleButtonKeyDownPageUp:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case ToggleButtonKeyDownPageDown:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case ToggleButtonKeyDownEscape:\n      changes = {\n        isOpen: false,\n        highlightedIndex: -1\n      };\n      break;\n    case ToggleButtonBlur:\n      changes = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        isOpen: false,\n        highlightedIndex: -1\n      }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && {\n        selectedItem: props.items[state.highlightedIndex]\n      });\n      break;\n    case FunctionSelectItem$1:\n      changes = {\n        selectedItem: action.selectedItem\n      };\n      break;\n    default:\n      return downshiftCommonReducer(state, action, stateChangeTypes$2);\n  }\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, state, changes);\n}\n/* eslint-enable complexity */\n\nvar _excluded$2 = [\"onMouseLeave\", \"refKey\", \"onKeyDown\", \"onBlur\", \"ref\"],\n  _excluded2$2 = [\"onBlur\", \"onClick\", \"onPress\", \"onKeyDown\", \"refKey\", \"ref\"],\n  _excluded3$1 = [\"item\", \"index\", \"onMouseMove\", \"onClick\", \"onPress\", \"refKey\", \"ref\", \"disabled\"];\nuseSelect.stateChangeTypes = stateChangeTypes$2;\nfunction useSelect(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes$2(userProps, useSelect);\n  // Props defaults and destructuring.\n  var props = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, defaultProps$2, userProps);\n  var items = props.items,\n    scrollIntoView = props.scrollIntoView,\n    environment = props.environment,\n    itemToString = props.itemToString,\n    getA11ySelectionMessage = props.getA11ySelectionMessage,\n    getA11yStatusMessage = props.getA11yStatusMessage;\n  // Initial state depending on controlled props.\n  var initialState = getInitialState$2(props);\n  var _useControlledReducer = useControlledReducer$1(downshiftSelectReducer, initialState, props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var isOpen = state.isOpen,\n    highlightedIndex = state.highlightedIndex,\n    selectedItem = state.selectedItem,\n    inputValue = state.inputValue;\n\n  // Element efs.\n  var toggleButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  var menuRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  var itemRefs = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({});\n  // used to keep the inputValue clearTimeout object between renders.\n  var clearTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  // prevent id re-generation between renders.\n  var elementIds = useElementIds(props);\n  // used to keep track of how many items we had on previous cycle.\n  var previousResultCountRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  var isInitialMountRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(true);\n  // utility callback to get item element.\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n\n  // Some utils.\n  var getItemNodeFromIndex = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (index) {\n    return itemRefs.current[elementIds.getItemId(index)];\n  }, [elementIds]);\n\n  // Effects.\n  // Sets a11y status message on changes in state.\n  useA11yMessageSetter(getA11yStatusMessage, [isOpen, highlightedIndex, inputValue, items], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Sets a11y status message on changes in selectedItem.\n  useA11yMessageSetter(getA11ySelectionMessage, [selectedItem], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Scroll on highlighted item if change comes from keyboard.\n  var shouldScrollRef = useScrollIntoView({\n    menuElement: menuRef.current,\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    itemRefs: itemRefs,\n    scrollIntoView: scrollIntoView,\n    getItemNodeFromIndex: getItemNodeFromIndex\n  });\n\n  // Sets cleanup for the keysSoFar callback, debounded after 500ms.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    // init the clean function here as we need access to dispatch.\n    clearTimeoutRef.current = debounce(function (outerDispatch) {\n      outerDispatch({\n        type: FunctionSetInputValue$1,\n        inputValue: ''\n      });\n    }, 500);\n\n    // Cancel any pending debounced calls on mount\n    return function () {\n      clearTimeoutRef.current.cancel();\n    };\n  }, []);\n\n  // Invokes the keysSoFar callback set up above.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (!inputValue) {\n      return;\n    }\n    clearTimeoutRef.current(dispatch);\n  }, [dispatch, inputValue]);\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    previousResultCountRef.current = items.length;\n  });\n  // Add mouse/touch events to document.\n  var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [menuRef, toggleButtonRef], environment, function () {\n    dispatch({\n      type: ToggleButtonBlur\n    });\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getMenuProps', 'getToggleButtonProps');\n  // Make initial ref false.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n  // Reset itemRefs on close.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (!isOpen) {\n      itemRefs.current = {};\n    }\n  }, [isOpen]);\n\n  // Event handler functions.\n  var toggleButtonKeyDownHandlers = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    return {\n      ArrowDown: function ArrowDown(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownArrowDown,\n          getItemNodeFromIndex: getItemNodeFromIndex,\n          altKey: event.altKey\n        });\n      },\n      ArrowUp: function ArrowUp(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownArrowUp,\n          getItemNodeFromIndex: getItemNodeFromIndex,\n          altKey: event.altKey\n        });\n      },\n      Home: function Home(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownHome,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      End: function End(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownEnd,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Escape: function Escape() {\n        if (latest.current.state.isOpen) {\n          dispatch({\n            type: ToggleButtonKeyDownEscape\n          });\n        }\n      },\n      Enter: function Enter(event) {\n        event.preventDefault();\n        dispatch({\n          type: latest.current.state.isOpen ? ToggleButtonKeyDownEnter : ToggleButtonClick$1\n        });\n      },\n      PageUp: function PageUp(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: ToggleButtonKeyDownPageUp,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      PageDown: function PageDown(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: ToggleButtonKeyDownPageDown,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      ' ': function _(event) {\n        event.preventDefault();\n        var currentState = latest.current.state;\n        if (!currentState.isOpen) {\n          dispatch({\n            type: ToggleButtonClick$1\n          });\n          return;\n        }\n        if (currentState.inputValue) {\n          dispatch({\n            type: ToggleButtonKeyDownCharacter,\n            key: ' ',\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        } else {\n          dispatch({\n            type: ToggleButtonKeyDownSpaceButton\n          });\n        }\n      }\n    };\n  }, [dispatch, getItemNodeFromIndex, latest]);\n\n  // Action functions.\n  var toggleMenu = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionToggleMenu$1\n    });\n  }, [dispatch]);\n  var closeMenu = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionCloseMenu$1\n    });\n  }, [dispatch]);\n  var openMenu = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionOpenMenu$1\n    });\n  }, [dispatch]);\n  var setHighlightedIndex = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newHighlightedIndex) {\n    dispatch({\n      type: FunctionSetHighlightedIndex$1,\n      highlightedIndex: newHighlightedIndex\n    });\n  }, [dispatch]);\n  var selectItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newSelectedItem) {\n    dispatch({\n      type: FunctionSelectItem$1,\n      selectedItem: newSelectedItem\n    });\n  }, [dispatch]);\n  var reset = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionReset$2\n    });\n  }, [dispatch]);\n  var setInputValue = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newInputValue) {\n    dispatch({\n      type: FunctionSetInputValue$1,\n      inputValue: newInputValue\n    });\n  }, [dispatch]);\n  // Getter functions.\n  var getLabelProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (labelProps) {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      id: elementIds.labelId,\n      htmlFor: elementIds.toggleButtonId\n    }, labelProps);\n  }, [elementIds]);\n  var getMenuProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp, _temp2) {\n    var _extends2;\n    var _ref = _temp === void 0 ? {} : _temp,\n      onMouseLeave = _ref.onMouseLeave,\n      _ref$refKey = _ref.refKey,\n      refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey;\n      _ref.onKeyDown;\n      _ref.onBlur;\n      var ref = _ref.ref,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded$2);\n    var _ref2 = _temp2 === void 0 ? {} : _temp2,\n      _ref2$suppressRefErro = _ref2.suppressRefError,\n      suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n    var menuHandleMouseLeave = function menuHandleMouseLeave() {\n      dispatch({\n        type: MenuMouseLeave$1\n      });\n    };\n    setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (menuNode) {\n      menuRef.current = menuNode;\n    }), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, menuHandleMouseLeave), _extends2), rest);\n  }, [dispatch, setGetterPropCallInfo, elementIds]);\n  var getToggleButtonProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp3, _temp4) {\n    var _extends3;\n    var _ref3 = _temp3 === void 0 ? {} : _temp3,\n      onBlur = _ref3.onBlur,\n      onClick = _ref3.onClick;\n      _ref3.onPress;\n      var onKeyDown = _ref3.onKeyDown,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, _excluded2$2);\n    var _ref4 = _temp4 === void 0 ? {} : _temp4,\n      _ref4$suppressRefErro = _ref4.suppressRefError,\n      suppressRefError = _ref4$suppressRefErro === void 0 ? false : _ref4$suppressRefErro;\n    var latestState = latest.current.state;\n    var toggleButtonHandleClick = function toggleButtonHandleClick() {\n      dispatch({\n        type: ToggleButtonClick$1\n      });\n    };\n    var toggleButtonHandleBlur = function toggleButtonHandleBlur() {\n      if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {\n        dispatch({\n          type: ToggleButtonBlur\n        });\n      }\n    };\n    var toggleButtonHandleKeyDown = function toggleButtonHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && toggleButtonKeyDownHandlers[key]) {\n        toggleButtonKeyDownHandlers[key](event);\n      } else if (isAcceptedCharacterKey(key)) {\n        dispatch({\n          type: ToggleButtonKeyDownCharacter,\n          key: key,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      }\n    };\n    var toggleProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (toggleButtonNode) {\n      toggleButtonRef.current = toggleButtonNode;\n    }), _extends3['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends3['aria-controls'] = elementIds.menuId, _extends3['aria-expanded'] = latest.current.state.isOpen, _extends3['aria-haspopup'] = 'listbox', _extends3['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends3.id = elementIds.toggleButtonId, _extends3.role = 'combobox', _extends3.tabIndex = 0, _extends3.onBlur = callAllEventHandlers(onBlur, toggleButtonHandleBlur), _extends3), rest);\n    if (!rest.disabled) {\n      /* istanbul ignore if (react-native) */\n      {\n        toggleProps.onClick = callAllEventHandlers(onClick, toggleButtonHandleClick);\n        toggleProps.onKeyDown = callAllEventHandlers(onKeyDown, toggleButtonHandleKeyDown);\n      }\n    }\n    setGetterPropCallInfo('getToggleButtonProps', suppressRefError, refKey, toggleButtonRef);\n    return toggleProps;\n  }, [latest, elementIds, setGetterPropCallInfo, dispatch, mouseAndTouchTrackersRef, toggleButtonKeyDownHandlers, getItemNodeFromIndex]);\n  var getItemProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp5) {\n    var _extends4;\n    var _ref5 = _temp5 === void 0 ? {} : _temp5,\n      itemProp = _ref5.item,\n      indexProp = _ref5.index,\n      onMouseMove = _ref5.onMouseMove,\n      onClick = _ref5.onClick;\n      _ref5.onPress;\n      var _ref5$refKey = _ref5.refKey,\n      refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n      ref = _ref5.ref,\n      disabled = _ref5.disabled,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref5, _excluded3$1);\n    var _latest$current = latest.current,\n      latestState = _latest$current.state,\n      latestProps = _latest$current.props;\n    var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'),\n      item = _getItemAndIndex[0],\n      index = _getItemAndIndex[1];\n    var itemHandleMouseMove = function itemHandleMouseMove() {\n      if (index === latestState.highlightedIndex) {\n        return;\n      }\n      shouldScrollRef.current = false;\n      dispatch({\n        type: ItemMouseMove$1,\n        index: index,\n        disabled: disabled\n      });\n    };\n    var itemHandleClick = function itemHandleClick() {\n      dispatch({\n        type: ItemClick$1,\n        index: index\n      });\n    };\n    var itemProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends4 = {\n      disabled: disabled,\n      role: 'option',\n      'aria-selected': \"\" + (item === selectedItem),\n      id: elementIds.getItemId(index)\n    }, _extends4[refKey] = handleRefs(ref, function (itemNode) {\n      if (itemNode) {\n        itemRefs.current[elementIds.getItemId(index)] = itemNode;\n      }\n    }), _extends4), rest);\n    if (!disabled) {\n      /* istanbul ignore next (react-native) */\n      {\n        itemProps.onClick = callAllEventHandlers(onClick, itemHandleClick);\n      }\n    }\n    itemProps.onMouseMove = callAllEventHandlers(onMouseMove, itemHandleMouseMove);\n    return itemProps;\n  }, [latest, selectedItem, elementIds, shouldScrollRef, dispatch]);\n  return {\n    // prop getters.\n    getToggleButtonProps: getToggleButtonProps,\n    getLabelProps: getLabelProps,\n    getMenuProps: getMenuProps,\n    getItemProps: getItemProps,\n    // actions.\n    toggleMenu: toggleMenu,\n    openMenu: openMenu,\n    closeMenu: closeMenu,\n    setHighlightedIndex: setHighlightedIndex,\n    selectItem: selectItem,\n    reset: reset,\n    setInputValue: setInputValue,\n    // state.\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\n\nvar InputKeyDownArrowDown =  true ? '__input_keydown_arrow_down__' : 0;\nvar InputKeyDownArrowUp =  true ? '__input_keydown_arrow_up__' : 0;\nvar InputKeyDownEscape =  true ? '__input_keydown_escape__' : 0;\nvar InputKeyDownHome =  true ? '__input_keydown_home__' : 0;\nvar InputKeyDownEnd =  true ? '__input_keydown_end__' : 0;\nvar InputKeyDownPageUp =  true ? '__input_keydown_page_up__' : 0;\nvar InputKeyDownPageDown =  true ? '__input_keydown_page_down__' : 0;\nvar InputKeyDownEnter =  true ? '__input_keydown_enter__' : 0;\nvar InputChange =  true ? '__input_change__' : 0;\nvar InputBlur =  true ? '__input_blur__' : 0;\nvar InputFocus =  true ? '__input_focus__' : 0;\nvar MenuMouseLeave =  true ? '__menu_mouse_leave__' : 0;\nvar ItemMouseMove =  true ? '__item_mouse_move__' : 0;\nvar ItemClick =  true ? '__item_click__' : 0;\nvar ToggleButtonClick =  true ? '__togglebutton_click__' : 0;\nvar FunctionToggleMenu =  true ? '__function_toggle_menu__' : 0;\nvar FunctionOpenMenu =  true ? '__function_open_menu__' : 0;\nvar FunctionCloseMenu =  true ? '__function_close_menu__' : 0;\nvar FunctionSetHighlightedIndex =  true ? '__function_set_highlighted_index__' : 0;\nvar FunctionSelectItem =  true ? '__function_select_item__' : 0;\nvar FunctionSetInputValue =  true ? '__function_set_input_value__' : 0;\nvar FunctionReset$1 =  true ? '__function_reset__' : 0;\nvar ControlledPropUpdatedSelectedItem =  true ? '__controlled_prop_updated_selected_item__' : 0;\n\nvar stateChangeTypes$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  InputKeyDownArrowDown: InputKeyDownArrowDown,\n  InputKeyDownArrowUp: InputKeyDownArrowUp,\n  InputKeyDownEscape: InputKeyDownEscape,\n  InputKeyDownHome: InputKeyDownHome,\n  InputKeyDownEnd: InputKeyDownEnd,\n  InputKeyDownPageUp: InputKeyDownPageUp,\n  InputKeyDownPageDown: InputKeyDownPageDown,\n  InputKeyDownEnter: InputKeyDownEnter,\n  InputChange: InputChange,\n  InputBlur: InputBlur,\n  InputFocus: InputFocus,\n  MenuMouseLeave: MenuMouseLeave,\n  ItemMouseMove: ItemMouseMove,\n  ItemClick: ItemClick,\n  ToggleButtonClick: ToggleButtonClick,\n  FunctionToggleMenu: FunctionToggleMenu,\n  FunctionOpenMenu: FunctionOpenMenu,\n  FunctionCloseMenu: FunctionCloseMenu,\n  FunctionSetHighlightedIndex: FunctionSetHighlightedIndex,\n  FunctionSelectItem: FunctionSelectItem,\n  FunctionSetInputValue: FunctionSetInputValue,\n  FunctionReset: FunctionReset$1,\n  ControlledPropUpdatedSelectedItem: ControlledPropUpdatedSelectedItem\n});\n\nfunction getInitialState$1(props) {\n  var initialState = getInitialState$2(props);\n  var selectedItem = initialState.selectedItem;\n  var inputValue = initialState.inputValue;\n  if (inputValue === '' && selectedItem && props.defaultInputValue === undefined && props.initialInputValue === undefined && props.inputValue === undefined) {\n    inputValue = props.itemToString(selectedItem);\n  }\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, initialState, {\n    inputValue: inputValue\n  });\n}\nvar propTypes$1 = {\n  items: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().array).isRequired,\n  itemToString: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  selectedItemChanged: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  getA11yStatusMessage: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  getA11ySelectionMessage: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  highlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  defaultHighlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  initialHighlightedIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  isOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  defaultIsOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  initialIsOpen: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),\n  selectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n  initialSelectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n  defaultSelectedItem: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n  inputValue: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  defaultInputValue: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  initialInputValue: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  id: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  labelId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  menuId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  getItemId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  inputId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  toggleButtonId: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  stateReducer: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onSelectedItemChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onHighlightedIndexChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onStateChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onIsOpenChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onInputValueChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  environment: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n    addEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    removeEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    document: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n      getElementById: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n      activeElement: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n      body: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any)\n    })\n  })\n};\n\n/**\n * The useCombobox version of useControlledReducer, which also\n * checks if the controlled prop selectedItem changed between\n * renders. If so, it will also update inputValue with its\n * string equivalent. It uses the common useEnhancedReducer to\n * compute the rest of the state.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useControlledReducer(reducer, initialState, props) {\n  var previousSelectedItemRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props),\n    state = _useEnhancedReducer[0],\n    dispatch = _useEnhancedReducer[1];\n\n  // ToDo: if needed, make same approach as selectedItemChanged from Downshift.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (!isControlledProp(props, 'selectedItem')) {\n      return;\n    }\n    if (props.selectedItemChanged(previousSelectedItemRef.current, props.selectedItem)) {\n      dispatch({\n        type: ControlledPropUpdatedSelectedItem,\n        inputValue: props.itemToString(props.selectedItem)\n      });\n    }\n    previousSelectedItemRef.current = state.selectedItem === previousSelectedItemRef.current ? props.selectedItem : state.selectedItem;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [state.selectedItem, props.selectedItem]);\n  return [getState(state, props), dispatch];\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes$1 = noop;\n/* istanbul ignore next */\nif (true) {\n  validatePropTypes$1 = function validatePropTypes(options, caller) {\n    prop_types__WEBPACK_IMPORTED_MODULE_7___default().checkPropTypes(propTypes$1, options, 'prop', caller.name);\n  };\n}\nvar defaultProps$1 = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, defaultProps$3, {\n  selectedItemChanged: function selectedItemChanged(prevItem, item) {\n    return prevItem !== item;\n  },\n  getA11yStatusMessage: getA11yStatusMessage$1\n});\n\n/* eslint-disable complexity */\nfunction downshiftUseComboboxReducer(state, action) {\n  var _props$items;\n  var type = action.type,\n    props = action.props,\n    altKey = action.altKey;\n  var changes;\n  switch (type) {\n    case ItemClick:\n      changes = {\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        selectedItem: props.items[action.index],\n        inputValue: props.itemToString(props.items[action.index])\n      };\n      break;\n    case InputKeyDownArrowDown:\n      if (state.isOpen) {\n        changes = {\n          highlightedIndex: getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)\n        };\n      } else {\n        changes = {\n          highlightedIndex: altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1, action.getItemNodeFromIndex),\n          isOpen: props.items.length >= 0\n        };\n      }\n      break;\n    case InputKeyDownArrowUp:\n      if (state.isOpen) {\n        if (altKey) {\n          changes = getChangesOnSelection(props, state.highlightedIndex);\n        } else {\n          changes = {\n            highlightedIndex: getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)\n          };\n        }\n      } else {\n        changes = {\n          highlightedIndex: getHighlightedIndexOnOpen(props, state, -1, action.getItemNodeFromIndex),\n          isOpen: props.items.length >= 0\n        };\n      }\n      break;\n    case InputKeyDownEnter:\n      changes = getChangesOnSelection(props, state.highlightedIndex);\n      break;\n    case InputKeyDownEscape:\n      changes = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        isOpen: false,\n        highlightedIndex: -1\n      }, !state.isOpen && {\n        selectedItem: null,\n        inputValue: ''\n      });\n      break;\n    case InputKeyDownPageUp:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownPageDown:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownHome:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownEnd:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputBlur:\n      changes = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        isOpen: false,\n        highlightedIndex: -1\n      }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && action.selectItem && {\n        selectedItem: props.items[state.highlightedIndex],\n        inputValue: props.itemToString(props.items[state.highlightedIndex])\n      });\n      break;\n    case InputChange:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        inputValue: action.inputValue\n      };\n      break;\n    case InputFocus:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case FunctionSelectItem:\n      changes = {\n        selectedItem: action.selectedItem,\n        inputValue: props.itemToString(action.selectedItem)\n      };\n      break;\n    case ControlledPropUpdatedSelectedItem:\n      changes = {\n        inputValue: action.inputValue\n      };\n      break;\n    default:\n      return downshiftCommonReducer(state, action, stateChangeTypes$1);\n  }\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, state, changes);\n}\n/* eslint-enable complexity */\n\nvar _excluded$1 = [\"onMouseLeave\", \"refKey\", \"ref\"],\n  _excluded2$1 = [\"item\", \"index\", \"refKey\", \"ref\", \"onMouseMove\", \"onMouseDown\", \"onClick\", \"onPress\", \"disabled\"],\n  _excluded3 = [\"onClick\", \"onPress\", \"refKey\", \"ref\"],\n  _excluded4 = [\"onKeyDown\", \"onChange\", \"onInput\", \"onFocus\", \"onBlur\", \"onChangeText\", \"refKey\", \"ref\"];\nuseCombobox.stateChangeTypes = stateChangeTypes$1;\nfunction useCombobox(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes$1(userProps, useCombobox);\n  // Props defaults and destructuring.\n  var props = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, defaultProps$1, userProps);\n  var initialIsOpen = props.initialIsOpen,\n    defaultIsOpen = props.defaultIsOpen,\n    items = props.items,\n    scrollIntoView = props.scrollIntoView,\n    environment = props.environment,\n    getA11yStatusMessage = props.getA11yStatusMessage,\n    getA11ySelectionMessage = props.getA11ySelectionMessage,\n    itemToString = props.itemToString;\n  // Initial state depending on controlled props.\n  var initialState = getInitialState$1(props);\n  var _useControlledReducer = useControlledReducer(downshiftUseComboboxReducer, initialState, props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var isOpen = state.isOpen,\n    highlightedIndex = state.highlightedIndex,\n    selectedItem = state.selectedItem,\n    inputValue = state.inputValue;\n\n  // Element refs.\n  var menuRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  var itemRefs = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)({});\n  var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  var toggleButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  var isInitialMountRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(true);\n  // prevent id re-generation between renders.\n  var elementIds = useElementIds(props);\n  // used to keep track of how many items we had on previous cycle.\n  var previousResultCountRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  // utility callback to get item element.\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n  var getItemNodeFromIndex = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (index) {\n    return itemRefs.current[elementIds.getItemId(index)];\n  }, [elementIds]);\n\n  // Effects.\n  // Sets a11y status message on changes in state.\n  useA11yMessageSetter(getA11yStatusMessage, [isOpen, highlightedIndex, inputValue, items], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Sets a11y status message on changes in selectedItem.\n  useA11yMessageSetter(getA11ySelectionMessage, [selectedItem], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Scroll on highlighted item if change comes from keyboard.\n  var shouldScrollRef = useScrollIntoView({\n    menuElement: menuRef.current,\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    itemRefs: itemRefs,\n    scrollIntoView: scrollIntoView,\n    getItemNodeFromIndex: getItemNodeFromIndex\n  });\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  // Focus the input on first render if required.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    var focusOnOpen = initialIsOpen || defaultIsOpen || isOpen;\n    if (focusOnOpen && inputRef.current) {\n      inputRef.current.focus();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    previousResultCountRef.current = items.length;\n  });\n  // Add mouse/touch events to document.\n  var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [inputRef, menuRef, toggleButtonRef], environment, function () {\n    dispatch({\n      type: InputBlur,\n      selectItem: false\n    });\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getInputProps', 'getMenuProps');\n  // Make initial ref false.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n  // Reset itemRefs on close.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    var _environment$document;\n    if (!isOpen) {\n      itemRefs.current = {};\n    } else if (((_environment$document = environment.document) == null ? void 0 : _environment$document.activeElement) !== inputRef.current) {\n      var _inputRef$current;\n      inputRef == null || (_inputRef$current = inputRef.current) == null ? void 0 : _inputRef$current.focus();\n    }\n  }, [isOpen, environment]);\n\n  /* Event handler functions */\n  var inputKeyDownHandlers = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    return {\n      ArrowDown: function ArrowDown(event) {\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownArrowDown,\n          altKey: event.altKey,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      ArrowUp: function ArrowUp(event) {\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownArrowUp,\n          altKey: event.altKey,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Home: function Home(event) {\n        if (!latest.current.state.isOpen) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownHome,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      End: function End(event) {\n        if (!latest.current.state.isOpen) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownEnd,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Escape: function Escape(event) {\n        var latestState = latest.current.state;\n        if (latestState.isOpen || latestState.inputValue || latestState.selectedItem || latestState.highlightedIndex > -1) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownEscape\n          });\n        }\n      },\n      Enter: function Enter(event) {\n        var latestState = latest.current.state;\n        // if closed or no highlighted index, do nothing.\n        if (!latestState.isOpen || event.which === 229 // if IME composing, wait for next Enter keydown event.\n        ) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownEnter,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      PageUp: function PageUp(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownPageUp,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      PageDown: function PageDown(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownPageDown,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      }\n    };\n  }, [dispatch, latest, getItemNodeFromIndex]);\n\n  // Getter props.\n  var getLabelProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (labelProps) {\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      id: elementIds.labelId,\n      htmlFor: elementIds.inputId\n    }, labelProps);\n  }, [elementIds]);\n  var getMenuProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp, _temp2) {\n    var _extends2;\n    var _ref = _temp === void 0 ? {} : _temp,\n      onMouseLeave = _ref.onMouseLeave,\n      _ref$refKey = _ref.refKey,\n      refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey,\n      ref = _ref.ref,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded$1);\n    var _ref2 = _temp2 === void 0 ? {} : _temp2,\n      _ref2$suppressRefErro = _ref2.suppressRefError,\n      suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n    setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (menuNode) {\n      menuRef.current = menuNode;\n    }), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, function () {\n      dispatch({\n        type: MenuMouseLeave\n      });\n    }), _extends2), rest);\n  }, [dispatch, setGetterPropCallInfo, elementIds]);\n  var getItemProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp3) {\n    var _extends3, _ref4;\n    var _ref3 = _temp3 === void 0 ? {} : _temp3,\n      itemProp = _ref3.item,\n      indexProp = _ref3.index,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      onMouseMove = _ref3.onMouseMove,\n      onMouseDown = _ref3.onMouseDown,\n      onClick = _ref3.onClick;\n      _ref3.onPress;\n      var disabled = _ref3.disabled,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, _excluded2$1);\n    var _latest$current = latest.current,\n      latestProps = _latest$current.props,\n      latestState = _latest$current.state;\n    var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'),\n      index = _getItemAndIndex[1];\n    var onSelectKey = 'onClick';\n    var customClickHandler = onClick;\n    var itemHandleMouseMove = function itemHandleMouseMove() {\n      if (index === latestState.highlightedIndex) {\n        return;\n      }\n      shouldScrollRef.current = false;\n      dispatch({\n        type: ItemMouseMove,\n        index: index,\n        disabled: disabled\n      });\n    };\n    var itemHandleClick = function itemHandleClick() {\n      dispatch({\n        type: ItemClick,\n        index: index\n      });\n    };\n    var itemHandleMouseDown = function itemHandleMouseDown(e) {\n      return e.preventDefault();\n    };\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (itemNode) {\n      if (itemNode) {\n        itemRefs.current[elementIds.getItemId(index)] = itemNode;\n      }\n    }), _extends3.disabled = disabled, _extends3.role = 'option', _extends3['aria-selected'] = \"\" + (index === latestState.highlightedIndex), _extends3.id = elementIds.getItemId(index), _extends3), !disabled && (_ref4 = {}, _ref4[onSelectKey] = callAllEventHandlers(customClickHandler, itemHandleClick), _ref4), {\n      onMouseMove: callAllEventHandlers(onMouseMove, itemHandleMouseMove),\n      onMouseDown: callAllEventHandlers(onMouseDown, itemHandleMouseDown)\n    }, rest);\n  }, [dispatch, latest, shouldScrollRef, elementIds]);\n  var getToggleButtonProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp4) {\n    var _extends4;\n    var _ref5 = _temp4 === void 0 ? {} : _temp4,\n      onClick = _ref5.onClick;\n      _ref5.onPress;\n      var _ref5$refKey = _ref5.refKey,\n      refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n      ref = _ref5.ref,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref5, _excluded3);\n    var latestState = latest.current.state;\n    var toggleButtonHandleClick = function toggleButtonHandleClick() {\n      dispatch({\n        type: ToggleButtonClick\n      });\n    };\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends4 = {}, _extends4[refKey] = handleRefs(ref, function (toggleButtonNode) {\n      toggleButtonRef.current = toggleButtonNode;\n    }), _extends4['aria-controls'] = elementIds.menuId, _extends4['aria-expanded'] = latestState.isOpen, _extends4.id = elementIds.toggleButtonId, _extends4.tabIndex = -1, _extends4), !rest.disabled && (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, {\n      onClick: callAllEventHandlers(onClick, toggleButtonHandleClick)\n    }), rest);\n  }, [dispatch, latest, elementIds]);\n  var getInputProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp5, _temp6) {\n    var _extends5;\n    var _ref6 = _temp5 === void 0 ? {} : _temp5,\n      onKeyDown = _ref6.onKeyDown,\n      onChange = _ref6.onChange,\n      onInput = _ref6.onInput,\n      onFocus = _ref6.onFocus,\n      onBlur = _ref6.onBlur;\n      _ref6.onChangeText;\n      var _ref6$refKey = _ref6.refKey,\n      refKey = _ref6$refKey === void 0 ? 'ref' : _ref6$refKey,\n      ref = _ref6.ref,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref6, _excluded4);\n    var _ref7 = _temp6 === void 0 ? {} : _temp6,\n      _ref7$suppressRefErro = _ref7.suppressRefError,\n      suppressRefError = _ref7$suppressRefErro === void 0 ? false : _ref7$suppressRefErro;\n    setGetterPropCallInfo('getInputProps', suppressRefError, refKey, inputRef);\n    var latestState = latest.current.state;\n    var inputHandleKeyDown = function inputHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && inputKeyDownHandlers[key]) {\n        inputKeyDownHandlers[key](event);\n      }\n    };\n    var inputHandleChange = function inputHandleChange(event) {\n      dispatch({\n        type: InputChange,\n        inputValue: event.target.value\n      });\n    };\n    var inputHandleBlur = function inputHandleBlur(event) {\n      /* istanbul ignore else */\n      if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {\n        var isBlurByTabChange = event.relatedTarget === null && environment.document.activeElement !== environment.document.body;\n        dispatch({\n          type: InputBlur,\n          selectItem: !isBlurByTabChange\n        });\n      }\n    };\n    var inputHandleFocus = function inputHandleFocus() {\n      if (!latestState.isOpen) {\n        dispatch({\n          type: InputFocus\n        });\n      }\n    };\n\n    /* istanbul ignore next (preact) */\n    var onChangeKey = 'onChange';\n    var eventHandlers = {};\n    if (!rest.disabled) {\n      var _eventHandlers;\n      eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, inputHandleBlur), _eventHandlers.onFocus = callAllEventHandlers(onFocus, inputHandleFocus), _eventHandlers);\n    }\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends5 = {}, _extends5[refKey] = handleRefs(ref, function (inputNode) {\n      inputRef.current = inputNode;\n    }), _extends5['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends5['aria-autocomplete'] = 'list', _extends5['aria-controls'] = elementIds.menuId, _extends5['aria-expanded'] = latestState.isOpen, _extends5['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends5.autoComplete = 'off', _extends5.id = elementIds.inputId, _extends5.role = 'combobox', _extends5.value = latestState.inputValue, _extends5), eventHandlers, rest);\n  }, [setGetterPropCallInfo, latest, elementIds, inputKeyDownHandlers, dispatch, mouseAndTouchTrackersRef, environment]);\n\n  // returns\n  var toggleMenu = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionToggleMenu\n    });\n  }, [dispatch]);\n  var closeMenu = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionCloseMenu\n    });\n  }, [dispatch]);\n  var openMenu = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionOpenMenu\n    });\n  }, [dispatch]);\n  var setHighlightedIndex = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newHighlightedIndex) {\n    dispatch({\n      type: FunctionSetHighlightedIndex,\n      highlightedIndex: newHighlightedIndex\n    });\n  }, [dispatch]);\n  var selectItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newSelectedItem) {\n    dispatch({\n      type: FunctionSelectItem,\n      selectedItem: newSelectedItem\n    });\n  }, [dispatch]);\n  var setInputValue = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newInputValue) {\n    dispatch({\n      type: FunctionSetInputValue,\n      inputValue: newInputValue\n    });\n  }, [dispatch]);\n  var reset = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionReset$1\n    });\n  }, [dispatch]);\n  return {\n    // prop getters.\n    getItemProps: getItemProps,\n    getLabelProps: getLabelProps,\n    getMenuProps: getMenuProps,\n    getInputProps: getInputProps,\n    getToggleButtonProps: getToggleButtonProps,\n    // actions.\n    toggleMenu: toggleMenu,\n    openMenu: openMenu,\n    closeMenu: closeMenu,\n    setHighlightedIndex: setHighlightedIndex,\n    setInputValue: setInputValue,\n    selectItem: selectItem,\n    reset: reset,\n    // state.\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\n\nvar defaultStateValues = {\n  activeIndex: -1,\n  selectedItems: []\n};\n\n/**\n * Returns the initial value for a state key in the following order:\n * 1. controlled prop, 2. initial prop, 3. default prop, 4. default\n * value from Downshift.\n *\n * @param {Object} props Props passed to the hook.\n * @param {string} propKey Props key to generate the value for.\n * @returns {any} The initial value for that prop.\n */\nfunction getInitialValue(props, propKey) {\n  return getInitialValue$1(props, propKey, defaultStateValues);\n}\n\n/**\n * Returns the default value for a state key in the following order:\n * 1. controlled prop, 2. default prop, 3. default value from Downshift.\n *\n * @param {Object} props Props passed to the hook.\n * @param {string} propKey Props key to generate the value for.\n * @returns {any} The initial value for that prop.\n */\nfunction getDefaultValue(props, propKey) {\n  return getDefaultValue$1(props, propKey, defaultStateValues);\n}\n\n/**\n * Gets the initial state based on the provided props. It uses initial, default\n * and controlled props related to state in order to compute the initial value.\n *\n * @param {Object} props Props passed to the hook.\n * @returns {Object} The initial state.\n */\nfunction getInitialState(props) {\n  var activeIndex = getInitialValue(props, 'activeIndex');\n  var selectedItems = getInitialValue(props, 'selectedItems');\n  return {\n    activeIndex: activeIndex,\n    selectedItems: selectedItems\n  };\n}\n\n/**\n * Returns true if dropdown keydown operation is permitted. Should not be\n * allowed on keydown with modifier keys (ctrl, alt, shift, meta), on\n * input element with text content that is either highlighted or selection\n * cursor is not at the starting position.\n *\n * @param {KeyboardEvent} event The event from keydown.\n * @returns {boolean} Whether the operation is allowed.\n */\nfunction isKeyDownOperationPermitted(event) {\n  if (event.shiftKey || event.metaKey || event.ctrlKey || event.altKey) {\n    return false;\n  }\n  var element = event.target;\n  if (element instanceof HTMLInputElement &&\n  // if element is a text input\n  element.value !== '' && (\n  // and we have text in it\n  // and cursor is either not at the start or is currently highlighting text.\n  element.selectionStart !== 0 || element.selectionEnd !== 0)) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * Returns a message to be added to aria-live region when item is removed.\n *\n * @param {Object} selectionParameters Parameters required to build the message.\n * @returns {string} The a11y message.\n */\nfunction getA11yRemovalMessage(selectionParameters) {\n  var removedSelectedItem = selectionParameters.removedSelectedItem,\n    itemToStringLocal = selectionParameters.itemToString;\n  return itemToStringLocal(removedSelectedItem) + \" has been removed.\";\n}\nvar propTypes = {\n  selectedItems: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().array),\n  initialSelectedItems: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().array),\n  defaultSelectedItems: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().array),\n  itemToString: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  getA11yRemovalMessage: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  stateReducer: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  activeIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  initialActiveIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  defaultActiveIndex: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),\n  onActiveIndexChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  onSelectedItemsChange: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n  keyNavigationNext: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  keyNavigationPrevious: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),\n  environment: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n    addEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    removeEventListener: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n    document: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({\n      getElementById: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),\n      activeElement: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any),\n      body: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().any)\n    })\n  })\n};\nvar defaultProps = {\n  itemToString: defaultProps$3.itemToString,\n  stateReducer: defaultProps$3.stateReducer,\n  environment: defaultProps$3.environment,\n  getA11yRemovalMessage: getA11yRemovalMessage,\n  keyNavigationNext: 'ArrowRight',\n  keyNavigationPrevious: 'ArrowLeft'\n};\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes = noop;\n/* istanbul ignore next */\nif (true) {\n  validatePropTypes = function validatePropTypes(options, caller) {\n    prop_types__WEBPACK_IMPORTED_MODULE_7___default().checkPropTypes(propTypes, options, 'prop', caller.name);\n  };\n}\n\nvar SelectedItemClick =  true ? '__selected_item_click__' : 0;\nvar SelectedItemKeyDownDelete =  true ? '__selected_item_keydown_delete__' : 0;\nvar SelectedItemKeyDownBackspace =  true ? '__selected_item_keydown_backspace__' : 0;\nvar SelectedItemKeyDownNavigationNext =  true ? '__selected_item_keydown_navigation_next__' : 0;\nvar SelectedItemKeyDownNavigationPrevious =  true ? '__selected_item_keydown_navigation_previous__' : 0;\nvar DropdownKeyDownNavigationPrevious =  true ? '__dropdown_keydown_navigation_previous__' : 0;\nvar DropdownKeyDownBackspace =  true ? '__dropdown_keydown_backspace__' : 0;\nvar DropdownClick =  true ? '__dropdown_click__' : 0;\nvar FunctionAddSelectedItem =  true ? '__function_add_selected_item__' : 0;\nvar FunctionRemoveSelectedItem =  true ? '__function_remove_selected_item__' : 0;\nvar FunctionSetSelectedItems =  true ? '__function_set_selected_items__' : 0;\nvar FunctionSetActiveIndex =  true ? '__function_set_active_index__' : 0;\nvar FunctionReset =  true ? '__function_reset__' : 0;\n\nvar stateChangeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  SelectedItemClick: SelectedItemClick,\n  SelectedItemKeyDownDelete: SelectedItemKeyDownDelete,\n  SelectedItemKeyDownBackspace: SelectedItemKeyDownBackspace,\n  SelectedItemKeyDownNavigationNext: SelectedItemKeyDownNavigationNext,\n  SelectedItemKeyDownNavigationPrevious: SelectedItemKeyDownNavigationPrevious,\n  DropdownKeyDownNavigationPrevious: DropdownKeyDownNavigationPrevious,\n  DropdownKeyDownBackspace: DropdownKeyDownBackspace,\n  DropdownClick: DropdownClick,\n  FunctionAddSelectedItem: FunctionAddSelectedItem,\n  FunctionRemoveSelectedItem: FunctionRemoveSelectedItem,\n  FunctionSetSelectedItems: FunctionSetSelectedItems,\n  FunctionSetActiveIndex: FunctionSetActiveIndex,\n  FunctionReset: FunctionReset\n});\n\n/* eslint-disable complexity */\nfunction downshiftMultipleSelectionReducer(state, action) {\n  var type = action.type,\n    index = action.index,\n    props = action.props,\n    selectedItem = action.selectedItem;\n  var activeIndex = state.activeIndex,\n    selectedItems = state.selectedItems;\n  var changes;\n  switch (type) {\n    case SelectedItemClick:\n      changes = {\n        activeIndex: index\n      };\n      break;\n    case SelectedItemKeyDownNavigationPrevious:\n      changes = {\n        activeIndex: activeIndex - 1 < 0 ? 0 : activeIndex - 1\n      };\n      break;\n    case SelectedItemKeyDownNavigationNext:\n      changes = {\n        activeIndex: activeIndex + 1 >= selectedItems.length ? -1 : activeIndex + 1\n      };\n      break;\n    case SelectedItemKeyDownBackspace:\n    case SelectedItemKeyDownDelete:\n      {\n        if (activeIndex < 0) {\n          break;\n        }\n        var newActiveIndex = activeIndex;\n        if (selectedItems.length === 1) {\n          newActiveIndex = -1;\n        } else if (activeIndex === selectedItems.length - 1) {\n          newActiveIndex = selectedItems.length - 2;\n        }\n        changes = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          selectedItems: [].concat(selectedItems.slice(0, activeIndex), selectedItems.slice(activeIndex + 1))\n        }, {\n          activeIndex: newActiveIndex\n        });\n        break;\n      }\n    case DropdownKeyDownNavigationPrevious:\n      changes = {\n        activeIndex: selectedItems.length - 1\n      };\n      break;\n    case DropdownKeyDownBackspace:\n      changes = {\n        selectedItems: selectedItems.slice(0, selectedItems.length - 1)\n      };\n      break;\n    case FunctionAddSelectedItem:\n      changes = {\n        selectedItems: [].concat(selectedItems, [selectedItem])\n      };\n      break;\n    case DropdownClick:\n      changes = {\n        activeIndex: -1\n      };\n      break;\n    case FunctionRemoveSelectedItem:\n      {\n        var _newActiveIndex = activeIndex;\n        var selectedItemIndex = selectedItems.indexOf(selectedItem);\n        if (selectedItemIndex < 0) {\n          break;\n        }\n        if (selectedItems.length === 1) {\n          _newActiveIndex = -1;\n        } else if (selectedItemIndex === selectedItems.length - 1) {\n          _newActiveIndex = selectedItems.length - 2;\n        }\n        changes = {\n          selectedItems: [].concat(selectedItems.slice(0, selectedItemIndex), selectedItems.slice(selectedItemIndex + 1)),\n          activeIndex: _newActiveIndex\n        };\n        break;\n      }\n    case FunctionSetSelectedItems:\n      {\n        var newSelectedItems = action.selectedItems;\n        changes = {\n          selectedItems: newSelectedItems\n        };\n        break;\n      }\n    case FunctionSetActiveIndex:\n      {\n        var _newActiveIndex2 = action.activeIndex;\n        changes = {\n          activeIndex: _newActiveIndex2\n        };\n        break;\n      }\n    case FunctionReset:\n      changes = {\n        activeIndex: getDefaultValue(props, 'activeIndex'),\n        selectedItems: getDefaultValue(props, 'selectedItems')\n      };\n      break;\n    default:\n      throw new Error('Reducer called without proper action type.');\n  }\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, state, changes);\n}\n\nvar _excluded = [\"refKey\", \"ref\", \"onClick\", \"onKeyDown\", \"selectedItem\", \"index\"],\n  _excluded2 = [\"refKey\", \"ref\", \"onKeyDown\", \"onClick\", \"preventKeyAction\"];\nuseMultipleSelection.stateChangeTypes = stateChangeTypes;\nfunction useMultipleSelection(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes(userProps, useMultipleSelection);\n  // Props defaults and destructuring.\n  var props = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, defaultProps, userProps);\n  var getA11yRemovalMessage = props.getA11yRemovalMessage,\n    itemToString = props.itemToString,\n    environment = props.environment,\n    keyNavigationNext = props.keyNavigationNext,\n    keyNavigationPrevious = props.keyNavigationPrevious;\n\n  // Reducer init.\n  var _useControlledReducer = useControlledReducer$1(downshiftMultipleSelectionReducer, getInitialState(props), props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var activeIndex = state.activeIndex,\n    selectedItems = state.selectedItems;\n\n  // Refs.\n  var isInitialMountRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(true);\n  var dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n  var previousSelectedItemsRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(selectedItems);\n  var selectedItemRefs = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)();\n  selectedItemRefs.current = [];\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n\n  // Effects.\n  /* Sets a11y status message on changes in selectedItem. */\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (isInitialMountRef.current || false) {\n      return;\n    }\n    if (selectedItems.length < previousSelectedItemsRef.current.length) {\n      var removedSelectedItem = previousSelectedItemsRef.current.find(function (item) {\n        return selectedItems.indexOf(item) < 0;\n      });\n      setStatus(getA11yRemovalMessage({\n        itemToString: itemToString,\n        resultCount: selectedItems.length,\n        removedSelectedItem: removedSelectedItem,\n        activeIndex: activeIndex,\n        activeSelectedItem: selectedItems[activeIndex]\n      }), environment.document);\n    }\n    previousSelectedItemsRef.current = selectedItems;\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedItems.length]);\n  // Sets focus on active item.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    if (activeIndex === -1 && dropdownRef.current) {\n      dropdownRef.current.focus();\n    } else if (selectedItemRefs.current[activeIndex]) {\n      selectedItemRefs.current[activeIndex].focus();\n    }\n  }, [activeIndex]);\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getDropdownProps');\n  // Make initial ref false.\n  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n\n  // Event handler functions.\n  var selectedItemKeyDownHandlers = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    var _ref;\n    return _ref = {}, _ref[keyNavigationPrevious] = function () {\n      dispatch({\n        type: SelectedItemKeyDownNavigationPrevious\n      });\n    }, _ref[keyNavigationNext] = function () {\n      dispatch({\n        type: SelectedItemKeyDownNavigationNext\n      });\n    }, _ref.Delete = function Delete() {\n      dispatch({\n        type: SelectedItemKeyDownDelete\n      });\n    }, _ref.Backspace = function Backspace() {\n      dispatch({\n        type: SelectedItemKeyDownBackspace\n      });\n    }, _ref;\n  }, [dispatch, keyNavigationNext, keyNavigationPrevious]);\n  var dropdownKeyDownHandlers = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    var _ref2;\n    return _ref2 = {}, _ref2[keyNavigationPrevious] = function (event) {\n      if (isKeyDownOperationPermitted(event)) {\n        dispatch({\n          type: DropdownKeyDownNavigationPrevious\n        });\n      }\n    }, _ref2.Backspace = function Backspace(event) {\n      if (isKeyDownOperationPermitted(event)) {\n        dispatch({\n          type: DropdownKeyDownBackspace\n        });\n      }\n    }, _ref2;\n  }, [dispatch, keyNavigationPrevious]);\n\n  // Getter props.\n  var getSelectedItemProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp) {\n    var _extends2;\n    var _ref3 = _temp === void 0 ? {} : _temp,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      onClick = _ref3.onClick,\n      onKeyDown = _ref3.onKeyDown,\n      selectedItemProp = _ref3.selectedItem,\n      indexProp = _ref3.index,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, _excluded);\n    var latestState = latest.current.state;\n    var _getItemAndIndex = getItemAndIndex(selectedItemProp, indexProp, latestState.selectedItems, 'Pass either item or index to getSelectedItemProps!'),\n      index = _getItemAndIndex[1];\n    var isFocusable = index > -1 && index === latestState.activeIndex;\n    var selectedItemHandleClick = function selectedItemHandleClick() {\n      dispatch({\n        type: SelectedItemClick,\n        index: index\n      });\n    };\n    var selectedItemHandleKeyDown = function selectedItemHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && selectedItemKeyDownHandlers[key]) {\n        selectedItemKeyDownHandlers[key](event);\n      }\n    };\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (selectedItemNode) {\n      if (selectedItemNode) {\n        selectedItemRefs.current.push(selectedItemNode);\n      }\n    }), _extends2.tabIndex = isFocusable ? 0 : -1, _extends2.onClick = callAllEventHandlers(onClick, selectedItemHandleClick), _extends2.onKeyDown = callAllEventHandlers(onKeyDown, selectedItemHandleKeyDown), _extends2), rest);\n  }, [dispatch, latest, selectedItemKeyDownHandlers]);\n  var getDropdownProps = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (_temp2, _temp3) {\n    var _extends3;\n    var _ref4 = _temp2 === void 0 ? {} : _temp2,\n      _ref4$refKey = _ref4.refKey,\n      refKey = _ref4$refKey === void 0 ? 'ref' : _ref4$refKey,\n      ref = _ref4.ref,\n      onKeyDown = _ref4.onKeyDown,\n      onClick = _ref4.onClick,\n      _ref4$preventKeyActio = _ref4.preventKeyAction,\n      preventKeyAction = _ref4$preventKeyActio === void 0 ? false : _ref4$preventKeyActio,\n      rest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref4, _excluded2);\n    var _ref5 = _temp3 === void 0 ? {} : _temp3,\n      _ref5$suppressRefErro = _ref5.suppressRefError,\n      suppressRefError = _ref5$suppressRefErro === void 0 ? false : _ref5$suppressRefErro;\n    setGetterPropCallInfo('getDropdownProps', suppressRefError, refKey, dropdownRef);\n    var dropdownHandleKeyDown = function dropdownHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && dropdownKeyDownHandlers[key]) {\n        dropdownKeyDownHandlers[key](event);\n      }\n    };\n    var dropdownHandleClick = function dropdownHandleClick() {\n      dispatch({\n        type: DropdownClick\n      });\n    };\n    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (dropdownNode) {\n      if (dropdownNode) {\n        dropdownRef.current = dropdownNode;\n      }\n    }), _extends3), !preventKeyAction && {\n      onKeyDown: callAllEventHandlers(onKeyDown, dropdownHandleKeyDown),\n      onClick: callAllEventHandlers(onClick, dropdownHandleClick)\n    }, rest);\n  }, [dispatch, dropdownKeyDownHandlers, setGetterPropCallInfo]);\n\n  // returns\n  var addSelectedItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (selectedItem) {\n    dispatch({\n      type: FunctionAddSelectedItem,\n      selectedItem: selectedItem\n    });\n  }, [dispatch]);\n  var removeSelectedItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (selectedItem) {\n    dispatch({\n      type: FunctionRemoveSelectedItem,\n      selectedItem: selectedItem\n    });\n  }, [dispatch]);\n  var setSelectedItems = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newSelectedItems) {\n    dispatch({\n      type: FunctionSetSelectedItems,\n      selectedItems: newSelectedItems\n    });\n  }, [dispatch]);\n  var setActiveIndex = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (newActiveIndex) {\n    dispatch({\n      type: FunctionSetActiveIndex,\n      activeIndex: newActiveIndex\n    });\n  }, [dispatch]);\n  var reset = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {\n    dispatch({\n      type: FunctionReset\n    });\n  }, [dispatch]);\n  return {\n    getSelectedItemProps: getSelectedItemProps,\n    getDropdownProps: getDropdownProps,\n    addSelectedItem: addSelectedItem,\n    removeSelectedItem: removeSelectedItem,\n    setSelectedItems: setSelectedItems,\n    setActiveIndex: setActiveIndex,\n    reset: reset,\n    selectedItems: selectedItems,\n    activeIndex: activeIndex\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/downshift/dist/downshift.esm.js\n");

/***/ })

};
;