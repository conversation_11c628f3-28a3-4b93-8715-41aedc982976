"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/character-entities-legacy";
exports.ids = ["vendor-chunks/character-entities-legacy"];
exports.modules = {

/***/ "(ssr)/./node_modules/character-entities-legacy/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/character-entities-legacy/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesLegacy: () => (/* binding */ characterEntitiesLegacy)\n/* harmony export */ });\n/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nconst characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  'Agrave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'Ccedil',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY2hhcmFjdGVyLWVudGl0aWVzLWxlZ2FjeS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXGNoYXJhY3Rlci1lbnRpdGllcy1sZWdhY3lcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTGlzdCBvZiBsZWdhY3kgSFRNTCBuYW1lZCBjaGFyYWN0ZXIgcmVmZXJlbmNlcyB0aGF0IGRvbuKAmXQgbmVlZCBhIHRyYWlsaW5nIHNlbWljb2xvbi5cbiAqXG4gKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IGNoYXJhY3RlckVudGl0aWVzTGVnYWN5ID0gW1xuICAnQUVsaWcnLFxuICAnQU1QJyxcbiAgJ0FhY3V0ZScsXG4gICdBY2lyYycsXG4gICdBZ3JhdmUnLFxuICAnQXJpbmcnLFxuICAnQXRpbGRlJyxcbiAgJ0F1bWwnLFxuICAnQ09QWScsXG4gICdDY2VkaWwnLFxuICAnRVRIJyxcbiAgJ0VhY3V0ZScsXG4gICdFY2lyYycsXG4gICdFZ3JhdmUnLFxuICAnRXVtbCcsXG4gICdHVCcsXG4gICdJYWN1dGUnLFxuICAnSWNpcmMnLFxuICAnSWdyYXZlJyxcbiAgJ0l1bWwnLFxuICAnTFQnLFxuICAnTnRpbGRlJyxcbiAgJ09hY3V0ZScsXG4gICdPY2lyYycsXG4gICdPZ3JhdmUnLFxuICAnT3NsYXNoJyxcbiAgJ090aWxkZScsXG4gICdPdW1sJyxcbiAgJ1FVT1QnLFxuICAnUkVHJyxcbiAgJ1RIT1JOJyxcbiAgJ1VhY3V0ZScsXG4gICdVY2lyYycsXG4gICdVZ3JhdmUnLFxuICAnVXVtbCcsXG4gICdZYWN1dGUnLFxuICAnYWFjdXRlJyxcbiAgJ2FjaXJjJyxcbiAgJ2FjdXRlJyxcbiAgJ2FlbGlnJyxcbiAgJ2FncmF2ZScsXG4gICdhbXAnLFxuICAnYXJpbmcnLFxuICAnYXRpbGRlJyxcbiAgJ2F1bWwnLFxuICAnYnJ2YmFyJyxcbiAgJ2NjZWRpbCcsXG4gICdjZWRpbCcsXG4gICdjZW50JyxcbiAgJ2NvcHknLFxuICAnY3VycmVuJyxcbiAgJ2RlZycsXG4gICdkaXZpZGUnLFxuICAnZWFjdXRlJyxcbiAgJ2VjaXJjJyxcbiAgJ2VncmF2ZScsXG4gICdldGgnLFxuICAnZXVtbCcsXG4gICdmcmFjMTInLFxuICAnZnJhYzE0JyxcbiAgJ2ZyYWMzNCcsXG4gICdndCcsXG4gICdpYWN1dGUnLFxuICAnaWNpcmMnLFxuICAnaWV4Y2wnLFxuICAnaWdyYXZlJyxcbiAgJ2lxdWVzdCcsXG4gICdpdW1sJyxcbiAgJ2xhcXVvJyxcbiAgJ2x0JyxcbiAgJ21hY3InLFxuICAnbWljcm8nLFxuICAnbWlkZG90JyxcbiAgJ25ic3AnLFxuICAnbm90JyxcbiAgJ250aWxkZScsXG4gICdvYWN1dGUnLFxuICAnb2NpcmMnLFxuICAnb2dyYXZlJyxcbiAgJ29yZGYnLFxuICAnb3JkbScsXG4gICdvc2xhc2gnLFxuICAnb3RpbGRlJyxcbiAgJ291bWwnLFxuICAncGFyYScsXG4gICdwbHVzbW4nLFxuICAncG91bmQnLFxuICAncXVvdCcsXG4gICdyYXF1bycsXG4gICdyZWcnLFxuICAnc2VjdCcsXG4gICdzaHknLFxuICAnc3VwMScsXG4gICdzdXAyJyxcbiAgJ3N1cDMnLFxuICAnc3psaWcnLFxuICAndGhvcm4nLFxuICAndGltZXMnLFxuICAndWFjdXRlJyxcbiAgJ3VjaXJjJyxcbiAgJ3VncmF2ZScsXG4gICd1bWwnLFxuICAndXVtbCcsXG4gICd5YWN1dGUnLFxuICAneWVuJyxcbiAgJ3l1bWwnXG5dXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/character-entities-legacy/index.js\n");

/***/ })

};
;