"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-events-to-acorn";
exports.ids = ["vendor-chunks/micromark-util-events-to-acorn"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-events-to-acorn/dev/lib/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-util-events-to-acorn/dev/lib/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventsToAcorn: () => (/* binding */ eventsToAcorn)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var estree_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! estree-util-visit */ \"(ssr)/./node_modules/estree-util-visit/lib/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {Comment, Node as AcornNode, Token} from 'acorn'\n * @import {Node as EstreeNode, Program} from 'estree'\n * @import {Chunk, Event, Point as MicromarkPoint, TokenType} from 'micromark-util-types'\n * @import {Point as UnistPoint} from 'unist'\n *\n * @import {AcornOptions, Options} from 'micromark-util-events-to-acorn'\n * @import {AcornError, Collection, Result, Stop} from './types.js'\n */\n\n\n\n\n\n\n/**\n * Parse a list of micromark events with acorn.\n *\n * @param {Array<Event>} events\n *   Events.\n * @param {Options} options\n *   Configuration (required).\n * @returns {Result}\n *   Result.\n */\n// eslint-disable-next-line complexity\nfunction eventsToAcorn(events, options) {\n  const prefix = options.prefix || ''\n  const suffix = options.suffix || ''\n  const acornOptions = Object.assign({}, options.acornOptions)\n  /** @type {Array<Comment>} */\n  const comments = []\n  /** @type {Array<Token>} */\n  const tokens = []\n  const onComment = acornOptions.onComment\n  const onToken = acornOptions.onToken\n  let swallow = false\n  /** @type {AcornNode | undefined} */\n  let estree\n  /** @type {AcornError | undefined} */\n  let exception\n  /** @type {AcornOptions} */\n  const acornConfig = Object.assign({}, acornOptions, {\n    onComment: comments,\n    preserveParens: true\n  })\n\n  if (onToken) {\n    acornConfig.onToken = tokens\n  }\n\n  const collection = collect(events, options.tokenTypes)\n\n  const source = collection.value\n\n  const value = prefix + source + suffix\n  const isEmptyExpression = options.expression && empty(source)\n\n  if (isEmptyExpression && !options.allowEmpty) {\n    throw new vfile_message__WEBPACK_IMPORTED_MODULE_0__.VFileMessage('Unexpected empty expression', {\n      place: parseOffsetToUnistPoint(0),\n      ruleId: 'unexpected-empty-expression',\n      source: 'micromark-extension-mdx-expression'\n    })\n  }\n\n  try {\n    estree =\n      options.expression && !isEmptyExpression\n        ? options.acorn.parseExpressionAt(value, 0, acornConfig)\n        : options.acorn.parse(value, acornConfig)\n  } catch (error_) {\n    const error = /** @type {AcornError} */ (error_)\n    const point = parseOffsetToUnistPoint(error.pos)\n    error.message = String(error.message).replace(/ \\(\\d+:\\d+\\)$/, '')\n    // Always defined in our unist points that come from micromark.\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(point.offset !== undefined, 'expected `offset`')\n    error.pos = point.offset\n    error.loc = {line: point.line, column: point.column - 1}\n    exception = error\n    swallow =\n      error.raisedAt >= prefix.length + source.length ||\n      // Broken comments are raised at their start, not their end.\n      error.message === 'Unterminated comment'\n  }\n\n  if (estree && options.expression && !isEmptyExpression) {\n    if (empty(value.slice(estree.end, value.length - suffix.length))) {\n      estree = {\n        type: 'Program',\n        start: 0,\n        end: prefix.length + source.length,\n        // @ts-expect-error: It’s good.\n        body: [\n          {\n            type: 'ExpressionStatement',\n            expression: estree,\n            start: 0,\n            end: prefix.length + source.length\n          }\n        ],\n        sourceType: 'module',\n        comments: []\n      }\n    } else {\n      const point = parseOffsetToUnistPoint(estree.end)\n      const error = /** @type {AcornError} */ (\n        new Error('Unexpected content after expression')\n      )\n      // Always defined in our unist points that come from micromark.\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(point.offset !== undefined, 'expected `offset`')\n      error.pos = point.offset\n      error.loc = {line: point.line, column: point.column - 1}\n      exception = error\n      estree = undefined\n    }\n  }\n\n  if (estree) {\n    // @ts-expect-error: acorn *does* allow comments\n    estree.comments = comments\n\n    // @ts-expect-error: acorn looks enough like estree.\n    ;(0,estree_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(estree, function (esnode, field, index, parents) {\n      let context = /** @type {AcornNode | Array<AcornNode>} */ (\n        parents[parents.length - 1]\n      )\n      /** @type {number | string | undefined} */\n      let property = field\n\n      // Remove non-standard `ParenthesizedExpression`.\n      // @ts-expect-error: included in acorn.\n      if (esnode.type === 'ParenthesizedExpression' && context && property) {\n        /* c8 ignore next 5 */\n        if (typeof index === 'number') {\n          // @ts-expect-error: indexable.\n          context = context[property]\n          property = index\n        }\n\n        // @ts-expect-error: indexable.\n        context[property] = esnode.expression\n      }\n\n      fixPosition(esnode)\n    })\n\n    // Comment positions are fixed by `visit` because they’re in the tree.\n    if (Array.isArray(onComment)) {\n      onComment.push(...comments)\n    } else if (typeof onComment === 'function') {\n      for (const comment of comments) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(comment.loc, 'expected `loc` on comment')\n        onComment(\n          comment.type === 'Block',\n          comment.value,\n          comment.start,\n          comment.end,\n          comment.loc.start,\n          comment.loc.end\n        )\n      }\n    }\n\n    for (const token of tokens) {\n      // Ignore tokens that ends in prefix or start in suffix:\n      if (\n        token.end <= prefix.length ||\n        token.start - prefix.length >= source.length\n      ) {\n        continue\n      }\n\n      fixPosition(token)\n\n      if (Array.isArray(onToken)) {\n        onToken.push(token)\n      } else {\n        // `tokens` are not added if `onToken` is not defined, so it must be a\n        // function.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof onToken === 'function', 'expected function')\n        onToken(token)\n      }\n    }\n  }\n\n  // @ts-expect-error: It’s a program now.\n  return {estree, error: exception, swallow}\n\n  /**\n   * Update the position of a node.\n   *\n   * @param {AcornNode | EstreeNode | Token} nodeOrToken\n   * @returns {undefined}\n   */\n  function fixPosition(nodeOrToken) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n      'start' in nodeOrToken,\n      'expected `start` in node or token from acorn'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)('end' in nodeOrToken, 'expected `end` in node or token from acorn')\n    const pointStart = parseOffsetToUnistPoint(nodeOrToken.start)\n    const pointEnd = parseOffsetToUnistPoint(nodeOrToken.end)\n    // Always defined in our unist points that come from micromark.\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(pointStart.offset !== undefined, 'expected `offset`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(pointEnd.offset !== undefined, 'expected `offset`')\n    nodeOrToken.start = pointStart.offset\n    nodeOrToken.end = pointEnd.offset\n    nodeOrToken.loc = {\n      start: {\n        line: pointStart.line,\n        column: pointStart.column - 1,\n        // @ts-expect-error: not allowed by acorn types.\n        offset: pointStart.offset\n      },\n      end: {\n        line: pointEnd.line,\n        column: pointEnd.column - 1,\n        // @ts-expect-error: not allowed by acorn types.\n        offset: pointEnd.offset\n      }\n    }\n    nodeOrToken.range = [nodeOrToken.start, nodeOrToken.end]\n  }\n\n  /**\n   * Turn an arbitrary offset into the parsed value, into a point in the source\n   * value.\n   *\n   * @param {number} acornOffset\n   * @returns {UnistPoint}\n   */\n  function parseOffsetToUnistPoint(acornOffset) {\n    let sourceOffset = acornOffset - prefix.length\n\n    if (sourceOffset < 0) {\n      sourceOffset = 0\n    } else if (sourceOffset > source.length) {\n      sourceOffset = source.length\n    }\n\n    let point = relativeToPoint(collection.stops, sourceOffset)\n\n    if (!point) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(\n        options.start,\n        'empty expressions are need `options.start` being passed'\n      )\n      point = {\n        line: options.start.line,\n        column: options.start.column,\n        offset: options.start.offset\n      }\n    }\n\n    return point\n  }\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return /^\\s*$/.test(\n    value\n      // Multiline comments.\n      .replace(/\\/\\*[\\s\\S]*?\\*\\//g, '')\n      // Line comments.\n      // EOF instead of EOL is specifically not allowed, because that would\n      // mean the closing brace is on the commented-out line\n      .replace(/\\/\\/[^\\r\\n]*(\\r\\n|\\n|\\r)/g, '')\n  )\n}\n\n// Port from <https://github.com/wooorm/markdown-rs/blob/e692ab0/src/util/mdx_collect.rs#L15>.\n/**\n * @param {Array<Event>} events\n * @param {Array<TokenType>} tokenTypes\n * @returns {Collection}\n */\nfunction collect(events, tokenTypes) {\n  /** @type {Collection} */\n  const result = {value: '', stops: []}\n  let index = -1\n\n  while (++index < events.length) {\n    const event = events[index]\n\n    // Assume void.\n    if (event[0] === 'enter') {\n      const type = event[1].type\n\n      if (type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding || tokenTypes.includes(type)) {\n        const chunks = event[2].sliceStream(event[1])\n\n        // Drop virtual spaces.\n        while (chunks.length > 0 && chunks[0] === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.virtualSpace) {\n          chunks.shift()\n        }\n\n        const value = serializeChunks(chunks)\n        result.stops.push([result.value.length, event[1].start])\n        result.value += value\n        result.stops.push([result.value.length, event[1].end])\n      }\n    }\n  }\n\n  return result\n}\n\n// Port from <https://github.com/wooorm/markdown-rs/blob/e692ab0/src/util/location.rs#L91>.\n/**\n * Turn a relative offset into an absolute offset.\n *\n * @param {Array<Stop>} stops\n * @param {number} relative\n * @returns {UnistPoint | undefined}\n */\nfunction relativeToPoint(stops, relative) {\n  let index = 0\n\n  while (index < stops.length && stops[index][0] <= relative) {\n    index += 1\n  }\n\n  // There are no points: that only occurs if there was an empty string.\n  if (index === 0) {\n    return undefined\n  }\n\n  const [stopRelative, stopAbsolute] = stops[index - 1]\n  const rest = relative - stopRelative\n  return {\n    line: stopAbsolute.line,\n    column: stopAbsolute.column + rest,\n    offset: stopAbsolute.offset + rest\n  }\n}\n\n// Copy from <https://github.com/micromark/micromark/blob/ce3593a/packages/micromark/dev/lib/create-tokenizer.js#L595>\n// To do: expose that?\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {Array<Chunk>} chunks\n * @returns {string}\n */\nfunction serializeChunks(chunks) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.carriageReturn: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.values.cr\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.lineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.carriageReturnLineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.horizontalTab: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.values.ht\n\n          break\n        }\n\n        /* c8 ignore next 6 */\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.virtualSpace: {\n          if (atTab) continue\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.values.space\n\n          break\n        }\n\n        default: {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          // eslint-disable-next-line unicorn/prefer-code-point\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-events-to-acorn/dev/lib/index.js\n");

/***/ })

};
;