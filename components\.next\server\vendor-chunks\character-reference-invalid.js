"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/character-reference-invalid";
exports.ids = ["vendor-chunks/character-reference-invalid"];
exports.modules = {

/***/ "(ssr)/./node_modules/character-reference-invalid/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/character-reference-invalid/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterReferenceInvalid: () => (/* binding */ characterReferenceInvalid)\n/* harmony export */ });\n/**\n * Map of invalid numeric character references to their replacements, according to HTML.\n *\n * @type {Record<number, string>}\n */\nconst characterReferenceInvalid = {\n  0: '�',\n  128: '€',\n  130: '‚',\n  131: 'ƒ',\n  132: '„',\n  133: '…',\n  134: '†',\n  135: '‡',\n  136: 'ˆ',\n  137: '‰',\n  138: 'Š',\n  139: '‹',\n  140: 'Œ',\n  142: 'Ž',\n  145: '‘',\n  146: '’',\n  147: '“',\n  148: '”',\n  149: '•',\n  150: '–',\n  151: '—',\n  152: '˜',\n  153: '™',\n  154: 'š',\n  155: '›',\n  156: 'œ',\n  158: 'ž',\n  159: 'Ÿ'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY2hhcmFjdGVyLXJlZmVyZW5jZS1pbnZhbGlkL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcY2hhcmFjdGVyLXJlZmVyZW5jZS1pbnZhbGlkXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE1hcCBvZiBpbnZhbGlkIG51bWVyaWMgY2hhcmFjdGVyIHJlZmVyZW5jZXMgdG8gdGhlaXIgcmVwbGFjZW1lbnRzLCBhY2NvcmRpbmcgdG8gSFRNTC5cbiAqXG4gKiBAdHlwZSB7UmVjb3JkPG51bWJlciwgc3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IGNoYXJhY3RlclJlZmVyZW5jZUludmFsaWQgPSB7XG4gIDA6ICfvv70nLFxuICAxMjg6ICfigqwnLFxuICAxMzA6ICfigJonLFxuICAxMzE6ICfGkicsXG4gIDEzMjogJ+KAnicsXG4gIDEzMzogJ+KApicsXG4gIDEzNDogJ+KAoCcsXG4gIDEzNTogJ+KAoScsXG4gIDEzNjogJ8uGJyxcbiAgMTM3OiAn4oCwJyxcbiAgMTM4OiAnxaAnLFxuICAxMzk6ICfigLknLFxuICAxNDA6ICfFkicsXG4gIDE0MjogJ8W9JyxcbiAgMTQ1OiAn4oCYJyxcbiAgMTQ2OiAn4oCZJyxcbiAgMTQ3OiAn4oCcJyxcbiAgMTQ4OiAn4oCdJyxcbiAgMTQ5OiAn4oCiJyxcbiAgMTUwOiAn4oCTJyxcbiAgMTUxOiAn4oCUJyxcbiAgMTUyOiAny5wnLFxuICAxNTM6ICfihKInLFxuICAxNTQ6ICfFoScsXG4gIDE1NTogJ+KAuicsXG4gIDE1NjogJ8WTJyxcbiAgMTU4OiAnxb4nLFxuICAxNTk6ICfFuCdcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/character-reference-invalid/index.js\n");

/***/ })

};
;