"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm";
exports.ids = ["vendor-chunks/mdast-util-gfm"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/mdast-util-gfm/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFromMarkdown: () => (/* binding */ gfmFromMarkdown),\n/* harmony export */   gfmToMarkdown: () => (/* binding */ gfmToMarkdown)\n/* harmony export */ });\n/* harmony import */ var mdast_util_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-gfm-autolink-literal */ \"(ssr)/./node_modules/mdast-util-gfm-autolink-literal/lib/index.js\");\n/* harmony import */ var mdast_util_gfm_footnote__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-gfm-footnote */ \"(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js\");\n/* harmony import */ var mdast_util_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-gfm-strikethrough */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-strikethrough/lib/index.js\");\n/* harmony import */ var mdast_util_gfm_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mdast-util-gfm-table */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-table/lib/index.js\");\n/* harmony import */ var mdast_util_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mdast-util-gfm-task-list-item */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-task-list-item/lib/index.js\");\n/**\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n */\n\n/**\n * @typedef {import('mdast-util-gfm-table').Options} Options\n *   Configuration.\n */\n\n\n\n\n\n\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM (autolink\n * literals, footnotes, strikethrough, tables, tasklists).\n *\n * @returns {Array<FromMarkdownExtension>}\n *   Extension for `mdast-util-from-markdown` to enable GFM (autolink literals,\n *   footnotes, strikethrough, tables, tasklists).\n */\nfunction gfmFromMarkdown() {\n  return [\n    mdast_util_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_0__.gfmAutolinkLiteralFromMarkdown,\n    (0,mdast_util_gfm_footnote__WEBPACK_IMPORTED_MODULE_1__.gfmFootnoteFromMarkdown)(),\n    mdast_util_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_2__.gfmStrikethroughFromMarkdown,\n    mdast_util_gfm_table__WEBPACK_IMPORTED_MODULE_3__.gfmTableFromMarkdown,\n    mdast_util_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_4__.gfmTaskListItemFromMarkdown\n  ]\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM (autolink\n * literals, footnotes, strikethrough, tables, tasklists).\n *\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM (autolink literals,\n *   footnotes, strikethrough, tables, tasklists).\n */\nfunction gfmToMarkdown(options) {\n  return {\n    extensions: [\n      mdast_util_gfm_autolink_literal__WEBPACK_IMPORTED_MODULE_0__.gfmAutolinkLiteralToMarkdown,\n      (0,mdast_util_gfm_footnote__WEBPACK_IMPORTED_MODULE_1__.gfmFootnoteToMarkdown)(),\n      mdast_util_gfm_strikethrough__WEBPACK_IMPORTED_MODULE_2__.gfmStrikethroughToMarkdown,\n      (0,mdast_util_gfm_table__WEBPACK_IMPORTED_MODULE_3__.gfmTableToMarkdown)(options),\n      mdast_util_gfm_task_list_item__WEBPACK_IMPORTED_MODULE_4__.gfmTaskListItemToMarkdown\n    ]\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-strikethrough/lib/index.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-strikethrough/lib/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethroughFromMarkdown: () => (/* binding */ gfmStrikethroughFromMarkdown),\n/* harmony export */   gfmStrikethroughToMarkdown: () => (/* binding */ gfmStrikethroughToMarkdown)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown_lib_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/container-phrasing.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/track.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('mdast').Delete} Delete\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').ConstructName} ConstructName\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */\n\n\n\n\n// To do: next major: expose functions.\n// To do: next major: use `state`, state utilities.\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain strikethrough.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * Note: keep in sync with: <https://github.com/syntax-tree/mdast-util-to-markdown/blob/8ce8dbf/lib/unsafe.js#L14>\n *\n * @type {Array<ConstructName>}\n */\nconst constructsWithoutStrikethrough = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\nhandleDelete.peek = peekDelete\n\n/**\n * Extension for `mdast-util-from-markdown` to enable GFM strikethrough.\n *\n * @type {FromMarkdownExtension}\n */\nconst gfmStrikethroughFromMarkdown = {\n  canContainEols: ['delete'],\n  enter: {strikethrough: enterStrikethrough},\n  exit: {strikethrough: exitStrikethrough}\n}\n\n/**\n * Extension for `mdast-util-to-markdown` to enable GFM strikethrough.\n *\n * @type {ToMarkdownExtension}\n */\nconst gfmStrikethroughToMarkdown = {\n  unsafe: [\n    {\n      character: '~',\n      inConstruct: 'phrasing',\n      notInConstruct: constructsWithoutStrikethrough\n    }\n  ],\n  handlers: {delete: handleDelete}\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterStrikethrough(token) {\n  this.enter({type: 'delete', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitStrikethrough(token) {\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {Delete} node\n */\nfunction handleDelete(node, _, context, safeOptions) {\n  const tracker = (0,mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__.track)(safeOptions)\n  const exit = context.enter('strikethrough')\n  let value = tracker.move('~~')\n  value += (0,mdast_util_to_markdown_lib_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_1__.containerPhrasing)(node, context, {\n    ...tracker.current(),\n    before: value,\n    after: '~'\n  })\n  value += tracker.move('~~')\n  exit()\n  return value\n}\n\n/** @type {ToMarkdownHandle} */\nfunction peekDelete() {\n  return '~'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-strikethrough/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-table/lib/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-table/lib/index.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableFromMarkdown: () => (/* binding */ gfmTableFromMarkdown),\n/* harmony export */   gfmTableToMarkdown: () => (/* binding */ gfmTableToMarkdown)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown_lib_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/container-phrasing.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_handle_inline_code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-markdown/lib/handle/inline-code.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var markdown_table__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! markdown-table */ \"(ssr)/./node_modules/markdown-table/index.js\");\n/**\n * @typedef {import('mdast').Table} Table\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('mdast').InlineCode} InlineCode\n *\n * @typedef {import('markdown-table').MarkdownTableOptions} MarkdownTableOptions\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Context} ToMarkdownContext\n * @typedef {import('mdast-util-to-markdown').SafeOptions} SafeOptions\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [tableCellPadding=true]\n *   Whether to add a space of padding between delimiters and cells.\n * @property {boolean | null | undefined} [tablePipeAlign=true]\n *   Whether to align the delimiters.\n * @property {MarkdownTableOptions['stringLength'] | null | undefined} [stringLength]\n *   Function to detect the length of table cell content, used when aligning\n *   the delimiters between cells\n */\n\n\n\n\n\n// To do: next major: use `state` and `state` utilities from `mdast-util-to-markdown`.\n// To do: next major: use `defaultHandlers.inlineCode`.\n// To do: next major: expose functions.\n\n/**\n * Extension for `mdast-util-from-markdown` to enable GFM tables.\n *\n * @type {FromMarkdownExtension}\n */\nconst gfmTableFromMarkdown = {\n  enter: {\n    table: enterTable,\n    tableData: enterCell,\n    tableHeader: enterCell,\n    tableRow: enterRow\n  },\n  exit: {\n    codeText: exitCodeText,\n    table: exitTable,\n    tableData: exit,\n    tableHeader: exit,\n    tableRow: exit\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterTable(token) {\n  /** @type {Array<'left' | 'right' | 'center' | 'none'>} */\n  // @ts-expect-error: `align` is custom.\n  const align = token._align\n  this.enter(\n    {\n      type: 'table',\n      align: align.map((d) => (d === 'none' ? null : d)),\n      children: []\n    },\n    token\n  )\n  this.setData('inTable', true)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitTable(token) {\n  this.exit(token)\n  this.setData('inTable')\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterRow(token) {\n  this.enter({type: 'tableRow', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exit(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterCell(token) {\n  this.enter({type: 'tableCell', children: []}, token)\n}\n\n// Overwrite the default code text data handler to unescape escaped pipes when\n// they are in tables.\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCodeText(token) {\n  let value = this.resume()\n\n  if (this.getData('inTable')) {\n    value = value.replace(/\\\\([\\\\|])/g, replace)\n  }\n\n  const node = /** @type {InlineCode} */ (this.stack[this.stack.length - 1])\n  node.value = value\n  this.exit(token)\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM tables in\n * markdown.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM tables.\n */\nfunction gfmTableToMarkdown(options) {\n  const settings = options || {}\n  const padding = settings.tableCellPadding\n  const alignDelimiters = settings.tablePipeAlign\n  const stringLength = settings.stringLength\n  const around = padding ? ' ' : '|'\n\n  return {\n    unsafe: [\n      {character: '\\r', inConstruct: 'tableCell'},\n      {character: '\\n', inConstruct: 'tableCell'},\n      // A pipe, when followed by a tab or space (padding), or a dash or colon\n      // (unpadded delimiter row), could result in a table.\n      {atBreak: true, character: '|', after: '[\\t :-]'},\n      // A pipe in a cell must be encoded.\n      {character: '|', inConstruct: 'tableCell'},\n      // A colon must be followed by a dash, in which case it could start a\n      // delimiter row.\n      {atBreak: true, character: ':', after: '-'},\n      // A delimiter row can also start with a dash, when followed by more\n      // dashes, a colon, or a pipe.\n      // This is a stricter version than the built in check for lists, thematic\n      // breaks, and setex heading underlines though:\n      // <https://github.com/syntax-tree/mdast-util-to-markdown/blob/51a2038/lib/unsafe.js#L57>\n      {atBreak: true, character: '-', after: '[:|-]'}\n    ],\n    handlers: {\n      table: handleTable,\n      tableRow: handleTableRow,\n      tableCell: handleTableCell,\n      inlineCode: inlineCodeWithTable\n    }\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {Table} node\n   */\n  function handleTable(node, _, context, safeOptions) {\n    return serializeData(\n      handleTableAsData(node, context, safeOptions),\n      node.align\n    )\n  }\n\n  /**\n   * This function isn’t really used normally, because we handle rows at the\n   * table level.\n   * But, if someone passes in a table row, this ensures we make somewhat sense.\n   *\n   * @type {ToMarkdownHandle}\n   * @param {TableRow} node\n   */\n  function handleTableRow(node, _, context, safeOptions) {\n    const row = handleTableRowAsData(node, context, safeOptions)\n    const value = serializeData([row])\n    // `markdown-table` will always add an align row\n    return value.slice(0, value.indexOf('\\n'))\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {TableCell} node\n   */\n  function handleTableCell(node, _, context, safeOptions) {\n    const exit = context.enter('tableCell')\n    const subexit = context.enter('phrasing')\n    const value = (0,mdast_util_to_markdown_lib_util_container_phrasing_js__WEBPACK_IMPORTED_MODULE_0__.containerPhrasing)(node, context, {\n      ...safeOptions,\n      before: around,\n      after: around\n    })\n    subexit()\n    exit()\n    return value\n  }\n\n  /**\n   * @param {Array<Array<string>>} matrix\n   * @param {Array<string | null | undefined> | null | undefined} [align]\n   */\n  function serializeData(matrix, align) {\n    return (0,markdown_table__WEBPACK_IMPORTED_MODULE_1__.markdownTable)(matrix, {\n      align,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      alignDelimiters,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      padding,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      stringLength\n    })\n  }\n\n  /**\n   * @param {Table} node\n   * @param {ToMarkdownContext} context\n   * @param {SafeOptions} safeOptions\n   */\n  function handleTableAsData(node, context, safeOptions) {\n    const children = node.children\n    let index = -1\n    /** @type {Array<Array<string>>} */\n    const result = []\n    const subexit = context.enter('table')\n\n    while (++index < children.length) {\n      result[index] = handleTableRowAsData(\n        children[index],\n        context,\n        safeOptions\n      )\n    }\n\n    subexit()\n\n    return result\n  }\n\n  /**\n   * @param {TableRow} node\n   * @param {ToMarkdownContext} context\n   * @param {SafeOptions} safeOptions\n   */\n  function handleTableRowAsData(node, context, safeOptions) {\n    const children = node.children\n    let index = -1\n    /** @type {Array<string>} */\n    const result = []\n    const subexit = context.enter('tableRow')\n\n    while (++index < children.length) {\n      // Note: the positional info as used here is incorrect.\n      // Making it correct would be impossible due to aligning cells?\n      // And it would need copy/pasting `markdown-table` into this project.\n      result[index] = handleTableCell(\n        children[index],\n        node,\n        context,\n        safeOptions\n      )\n    }\n\n    subexit()\n\n    return result\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {InlineCode} node\n   */\n  function inlineCodeWithTable(node, parent, context) {\n    let value = (0,mdast_util_to_markdown_lib_handle_inline_code_js__WEBPACK_IMPORTED_MODULE_2__.inlineCode)(node, parent, context)\n\n    if (context.stack.includes('tableCell')) {\n      value = value.replace(/\\|/g, '\\\\$&')\n    }\n\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-table/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-task-list-item/lib/index.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-task-list-item/lib/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemFromMarkdown: () => (/* binding */ gfmTaskListItemFromMarkdown),\n/* harmony export */   gfmTaskListItemToMarkdown: () => (/* binding */ gfmTaskListItemToMarkdown)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown_lib_handle_list_item_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown/lib/handle/list-item.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/track.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast').Parent} Parent\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */\n\n/**\n * @typedef {Extract<Root | Content, Parent>} Parents\n */\n\n\n\n\n// To do: next major: rename `context` -> `state`, `safeOptions` -> `info`, use\n// `track` from `state`.\n// To do: next major: replace exports with functions.\n// To do: next major: use `defaulthandlers.listItem`.\n\n/**\n * Extension for `mdast-util-from-markdown` to enable GFM task list items.\n *\n * @type {FromMarkdownExtension}\n */\nconst gfmTaskListItemFromMarkdown = {\n  exit: {\n    taskListCheckValueChecked: exitCheck,\n    taskListCheckValueUnchecked: exitCheck,\n    paragraph: exitParagraphWithTaskListItem\n  }\n}\n\n/**\n * Extension for `mdast-util-to-markdown` to enable GFM task list items.\n *\n * @type {ToMarkdownExtension}\n */\nconst gfmTaskListItemToMarkdown = {\n  unsafe: [{atBreak: true, character: '-', after: '[:|-]'}],\n  handlers: {listItem: listItemWithTaskListItem}\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCheck(token) {\n  const node = /** @type {ListItem} */ (this.stack[this.stack.length - 2])\n  // We’re always in a paragraph, in a list item.\n  node.checked = token.type === 'taskListCheckValueChecked'\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitParagraphWithTaskListItem(token) {\n  const parent = /** @type {Parents} */ (this.stack[this.stack.length - 2])\n\n  if (\n    parent &&\n    parent.type === 'listItem' &&\n    typeof parent.checked === 'boolean'\n  ) {\n    const node = /** @type {Paragraph} */ (this.stack[this.stack.length - 1])\n    const head = node.children[0]\n\n    if (head && head.type === 'text') {\n      const siblings = parent.children\n      let index = -1\n      /** @type {Paragraph | undefined} */\n      let firstParaghraph\n\n      while (++index < siblings.length) {\n        const sibling = siblings[index]\n        if (sibling.type === 'paragraph') {\n          firstParaghraph = sibling\n          break\n        }\n      }\n\n      if (firstParaghraph === node) {\n        // Must start with a space or a tab.\n        head.value = head.value.slice(1)\n\n        if (head.value.length === 0) {\n          node.children.shift()\n        } else if (\n          node.position &&\n          head.position &&\n          typeof head.position.start.offset === 'number'\n        ) {\n          head.position.start.column++\n          head.position.start.offset++\n          node.position.start = Object.assign({}, head.position.start)\n        }\n      }\n    }\n  }\n\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {ListItem} node\n */\nfunction listItemWithTaskListItem(node, parent, context, safeOptions) {\n  const head = node.children[0]\n  const checkable =\n    typeof node.checked === 'boolean' && head && head.type === 'paragraph'\n  const checkbox = '[' + (node.checked ? 'x' : ' ') + '] '\n  const tracker = (0,mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_0__.track)(safeOptions)\n\n  if (checkable) {\n    tracker.move(checkbox)\n  }\n\n  let value = (0,mdast_util_to_markdown_lib_handle_list_item_js__WEBPACK_IMPORTED_MODULE_1__.listItem)(node, parent, context, {\n    ...safeOptions,\n    ...tracker.current()\n  })\n\n  if (checkable) {\n    value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check)\n  }\n\n  return value\n\n  /**\n   * @param {string} $0\n   * @returns {string}\n   */\n  function check($0) {\n    return $0 + checkbox\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-gfm-task-list-item/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_compile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-compile.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\");\n/**\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n */\n\n\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parent | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = (0,_util_pattern_compile_js__WEBPACK_IMPORTED_MODULE_0__.patternCompile)(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('../types.js').Map} Map\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Info} Info\n */\n\n\n\n\n/**\n * @param {ListItem} node\n * @param {Parent | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nfunction listItem(node, parent, state, info) {\n  const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state)\n  let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nfunction checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsK0JBQStCO0FBQzVDOztBQUVBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLWdmbVxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWJ1bGxldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3R5cGVzLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldChzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLmJ1bGxldCB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnKycgJiYgbWFya2VyICE9PSAnLScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldGAsIGV4cGVjdGVkIGAqYCwgYCtgLCBvciBgLWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').State} State\n * @typedef {import('../types.js').Options} Options\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nfunction checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'tab'\n\n  // To do: remove in a major.\n  // @ts-expect-error: deprecated.\n  if (style === 1 || style === '1') {\n    return 'one'\n  }\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stbGlzdC1pdGVtLWluZGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQyxhQUFhLCtCQUErQjtBQUM1Qzs7QUFFQTtBQUNBLFdBQVcsT0FBTztBQUNsQixhQUFhO0FBQ2I7QUFDTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLWdmbVxcbm9kZV9tb2R1bGVzXFxtZGFzdC11dGlsLXRvLW1hcmtkb3duXFxsaWJcXHV0aWxcXGNoZWNrLWxpc3QtaXRlbS1pbmRlbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vdHlwZXMuanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snbGlzdEl0ZW1JbmRlbnQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0xpc3RJdGVtSW5kZW50KHN0YXRlKSB7XG4gIGNvbnN0IHN0eWxlID0gc3RhdGUub3B0aW9ucy5saXN0SXRlbUluZGVudCB8fCAndGFiJ1xuXG4gIC8vIFRvIGRvOiByZW1vdmUgaW4gYSBtYWpvci5cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogZGVwcmVjYXRlZC5cbiAgaWYgKHN0eWxlID09PSAxIHx8IHN0eWxlID09PSAnMScpIHtcbiAgICByZXR1cm4gJ29uZSdcbiAgfVxuXG4gIGlmIChzdHlsZSAhPT0gJ3RhYicgJiYgc3R5bGUgIT09ICdvbmUnICYmIHN0eWxlICE9PSAnbWl4ZWQnKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIHN0eWxlICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmxpc3RJdGVtSW5kZW50YCwgZXhwZWN0ZWQgYHRhYmAsIGBvbmVgLCBvciBgbWl4ZWRgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBzdHlsZVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   containerPhrasing: () => (/* binding */ containerPhrasing)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Handle} Handle\n * @typedef {import('../types.js').Info} Info\n * @typedef {import('../types.js').Parent} Parent\n * @typedef {import('../types.js').PhrasingContent} PhrasingContent\n * @typedef {import('../types.js').State} State\n */\n\n/**\n * Serialize the children of a parent that contains phrasing children.\n *\n * These children will be joined flush together.\n *\n * @param {Parent & {children: Array<PhrasingContent>}} parent\n *   Parent of flow nodes.\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Info} info\n *   Info on where we are in the document we are generating.\n * @returns {string}\n *   Serialized children, joined together.\n */\nfunction containerPhrasing(parent, state, info) {\n  const indexStack = state.indexStack\n  const children = parent.children || []\n  /** @type {Array<string>} */\n  const results = []\n  let index = -1\n  let before = info.before\n\n  indexStack.push(-1)\n  let tracker = state.createTracker(info)\n\n  while (++index < children.length) {\n    const child = children[index]\n    /** @type {string} */\n    let after\n\n    indexStack[indexStack.length - 1] = index\n\n    if (index + 1 < children.length) {\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      let handle = state.handle.handlers[children[index + 1].type]\n      /** @type {Handle} */\n      // @ts-expect-error: hush, it’s actually a `zwitch`.\n      if (handle && handle.peek) handle = handle.peek\n      after = handle\n        ? handle(children[index + 1], parent, state, {\n            before: '',\n            after: '',\n            ...tracker.current()\n          }).charAt(0)\n        : ''\n    } else {\n      after = info.after\n    }\n\n    // In some cases, html (text) can be found in phrasing right after an eol.\n    // When we’d serialize that, in most cases that would be seen as html\n    // (flow).\n    // As we can’t escape or so to prevent it from happening, we take a somewhat\n    // reasonable approach: replace that eol with a space.\n    // See: <https://github.com/syntax-tree/mdast-util-to-markdown/issues/15>\n    if (\n      results.length > 0 &&\n      (before === '\\r' || before === '\\n') &&\n      child.type === 'html'\n    ) {\n      results[results.length - 1] = results[results.length - 1].replace(\n        /(\\r?\\n|\\r)$/,\n        ' '\n      )\n      before = ' '\n\n      // To do: does this work to reset tracker?\n      tracker = state.createTracker(info)\n      tracker.move(results.join(''))\n    }\n\n    results.push(\n      tracker.move(\n        state.handle(child, parent, state, {\n          ...tracker.current(),\n          before,\n          after\n        })\n      )\n    )\n\n    before = results[results.length - 1].slice(-1)\n  }\n\n  indexStack.pop()\n\n  return results.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/container-phrasing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternCompile: () => (/* binding */ patternCompile)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').Unsafe} Unsafe\n */\n\n/**\n * @param {Unsafe} pattern\n * @returns {RegExp}\n */\nfunction patternCompile(pattern) {\n  if (!pattern._compiled) {\n    const before =\n      (pattern.atBreak ? '[\\\\r\\\\n][\\\\t ]*' : '') +\n      (pattern.before ? '(?:' + pattern.before + ')' : '')\n\n    pattern._compiled = new RegExp(\n      (before ? '(' + before + ')' : '') +\n        (/[|\\\\{}()[\\]^$+*?.-]/.test(pattern.character) ? '\\\\' : '') +\n        pattern.character +\n        (pattern.after ? '(?:' + pattern.after + ')' : ''),\n      'g'\n    )\n  }\n\n  return pattern._compiled\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvcGF0dGVybi1jb21waWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsOEJBQThCO0FBQzNDOztBQUVBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC1nZm1cXG5vZGVfbW9kdWxlc1xcbWRhc3QtdXRpbC10by1tYXJrZG93blxcbGliXFx1dGlsXFxwYXR0ZXJuLWNvbXBpbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi90eXBlcy5qcycpLlVuc2FmZX0gVW5zYWZlXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1Vuc2FmZX0gcGF0dGVyblxuICogQHJldHVybnMge1JlZ0V4cH1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhdHRlcm5Db21waWxlKHBhdHRlcm4pIHtcbiAgaWYgKCFwYXR0ZXJuLl9jb21waWxlZCkge1xuICAgIGNvbnN0IGJlZm9yZSA9XG4gICAgICAocGF0dGVybi5hdEJyZWFrID8gJ1tcXFxcclxcXFxuXVtcXFxcdCBdKicgOiAnJykgK1xuICAgICAgKHBhdHRlcm4uYmVmb3JlID8gJyg/OicgKyBwYXR0ZXJuLmJlZm9yZSArICcpJyA6ICcnKVxuXG4gICAgcGF0dGVybi5fY29tcGlsZWQgPSBuZXcgUmVnRXhwKFxuICAgICAgKGJlZm9yZSA/ICcoJyArIGJlZm9yZSArICcpJyA6ICcnKSArXG4gICAgICAgICgvW3xcXFxce30oKVtcXF1eJCsqPy4tXS8udGVzdChwYXR0ZXJuLmNoYXJhY3RlcikgPyAnXFxcXCcgOiAnJykgK1xuICAgICAgICBwYXR0ZXJuLmNoYXJhY3RlciArXG4gICAgICAgIChwYXR0ZXJuLmFmdGVyID8gJyg/OicgKyBwYXR0ZXJuLmFmdGVyICsgJyknIDogJycpLFxuICAgICAgJ2cnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHBhdHRlcm4uX2NvbXBpbGVkXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/pattern-compile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/track.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/track.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/**\n * @typedef {import('../types.js').CreateTracker} CreateTracker\n * @typedef {import('../types.js').TrackCurrent} TrackCurrent\n * @typedef {import('../types.js').TrackMove} TrackMove\n * @typedef {import('../types.js').TrackShift} TrackShift\n */\n\n/**\n * Track positional info in the output.\n *\n * @type {CreateTracker}\n */\nfunction track(config) {\n  // Defaults are used to prevent crashes when older utilities somehow activate\n  // this code.\n  /* c8 ignore next 5 */\n  const options = config || {}\n  const now = options.now || {}\n  let lineShift = options.lineShift || 0\n  let line = now.line || 1\n  let column = now.column || 1\n\n  return {move, current, shift}\n\n  /**\n   * Get the current tracked info.\n   *\n   * @type {TrackCurrent}\n   */\n  function current() {\n    return {now: {line, column}, lineShift}\n  }\n\n  /**\n   * Define an increased line shift (the typical indent for lines).\n   *\n   * @type {TrackShift}\n   */\n  function shift(value) {\n    lineShift += value\n  }\n\n  /**\n   * Move past some generated markdown.\n   *\n   * @type {TrackMove}\n   */\n  function move(input) {\n    // eslint-disable-next-line unicorn/prefer-default-parameters\n    const value = input || ''\n    const chunks = value.split(/\\r?\\n|\\r/g)\n    const tail = chunks[chunks.length - 1]\n    line += chunks.length - 1\n    column =\n      chunks.length === 1 ? column + tail.length : 1 + tail.length + lineShift\n    return value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm/node_modules/mdast-util-to-markdown/lib/util/track.js\n");

/***/ })

};
;