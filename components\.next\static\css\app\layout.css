/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_e8ce0c {font-family: 'Inter', 'Inter Fallback';font-style: normal
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  * {
  border-color: hsl(var(--border));
}
  body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.-right-2 {
  right: -0.5rem;
}
.-right-4 {
  right: -1rem;
}
.-top-0 {
  top: -0px;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-2 {
  bottom: 0.5rem;
}
.left-2 {
  left: 0.5rem;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-\[30px\] {
  right: 30px;
}
.top-0 {
  top: 0px;
}
.top-\[14px\] {
  top: 14px;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-50 {
  z-index: 50;
}
.m-4 {
  margin: 1rem;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-20 {
  margin-left: 5rem;
  margin-right: 5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-auto {
  margin-right: auto;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-24 {
  margin-top: 6rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-auto {
  margin-top: auto;
}
.block {
  display: block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}
.size-8 {
  width: 2rem;
  height: 2rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[100px\] {
  height: 100px;
}
.h-\[calc\(100vh-73px\)\] {
  height: calc(100vh - 73px);
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[200px\] {
  max-height: 200px;
}
.max-h-\[400px\] {
  max-height: 400px;
}
.max-h-\[96\%\] {
  max-height: 96%;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-20 {
  width: 5rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/5 {
  width: 60%;
}
.w-4 {
  width: 1rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-60 {
  width: 15rem;
}
.w-64 {
  width: 16rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-\[100px\] {
  width: 100px;
}
.w-\[400px\] {
  width: 400px;
}
.w-\[70px\] {
  width: 70px;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.max-w-60 {
  max-width: 15rem;
}
.max-w-72 {
  max-width: 18rem;
}
.max-w-\[200px\] {
  max-width: 200px;
}
.max-w-\[80\%\] {
  max-width: 80%;
}
.max-w-full {
  max-width: 100%;
}
.max-w-none {
  max-width: none;
}
.flex-1 {
  flex: 1 1 0%;
}
.shrink-0 {
  flex-shrink: 0;
}
.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.animate-\[slide-up_0\.5s_ease-out\] {
  animation: slide-up 0.5s ease-out;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-decimal {
  list-style-type: decimal;
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-center {
  align-items: center;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.self-stretch {
  align-self: stretch;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-line {
  white-space: pre-line;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.whitespace-break-spaces {
  white-space: break-spaces;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-t-\[10px\] {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-none {
  border-style: none;
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-border {
  border-color: hsl(var(--border));
}
.border-current {
  border-color: currentColor;
}
.border-indigo-400 {
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.border-input {
  border-color: hsl(var(--input));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-\[\#fafafa\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-sky-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(56 189 248 / var(--tw-bg-opacity, 1));
}
.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-4 {
  padding-top: 1rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.opacity-0 {
  opacity: 0;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-75 {
  opacity: 0.75;
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\[animation-delay\:100ms\] {
  animation-delay: 100ms;
}
.\[animation-delay\:300ms\] {
  animation-delay: 300ms;
}
.\[animation-fill-mode\:forwards\] {
  animation-fill-mode: forwards;
}
.\[citation\:id\] {
  citation: id;
}
.\[citation\:node_id\] {
  citation: node id;
}

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

/* 引用样式 */
.citation-number {
  display: inline-flex;
  height: 1.25rem;
  width: 1.25rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.citation-number:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.citation-number {
  margin: 0 2px;
  vertical-align: baseline;
}

.citation-number:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.citation-tooltip {
  position: absolute;
  z-index: 50;
  max-width: 24rem;
  border-radius: var(--radius);
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  max-height: 200px;
  overflow-y: auto;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.citation-tooltip::-webkit-scrollbar {
  width: 4px;
}

.citation-tooltip::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.citation-tooltip::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.citation-tooltip::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色模式支持 */
.dark .citation-number {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.dark .citation-number:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.dark .citation-tooltip {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.dark .citation-tooltip::-webkit-scrollbar-track {
  background: #374151;
}

.dark .citation-tooltip::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .citation-tooltip::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .citation-tooltip {
    max-width: calc(100vw - 2rem);
    left: 1rem !important;
    right: 1rem !important;
    transform: none !important;
  }
}

/* 聊天消息样式增强 */
.chat-message {
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-message:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}

.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:brightness-75:hover {
  --tw-brightness: brightness(.75);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:block {
  display: block;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}

.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 640px) {

  .sm\:w-auto {
    width: auto;
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:text-left {
    text-align: left;
  }
}

@media (min-width: 768px) {

  .md\:w-3\/5 {
    width: 60%;
  }
}

@media (prefers-color-scheme: dark) {

  .dark\:border-red-800 {
    --tw-border-opacity: 1;
    border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
  }

  .dark\:bg-red-900\/20 {
    background-color: rgb(127 29 29 / 0.2);
  }

  .dark\:text-neutral-400 {
    --tw-text-opacity: 1;
    color: rgb(163 163 163 / var(--tw-text-opacity, 1));
  }

  .dark\:text-red-200 {
    --tw-text-opacity: 1;
    color: rgb(254 202 202 / var(--tw-text-opacity, 1));
  }
}

.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@llamaindex/chat-ui/dist/styles/markdown.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.0.7 | MIT License | https://tailwindcss.com */
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}
code.hljs {
  padding: 3px 5px;
}
.hljs {
  color: #383a42;
  background: #fafafa;
}
.hljs-comment, .hljs-quote {
  color: #a0a1a7;
  font-style: italic;
}
.hljs-doctag, .hljs-keyword, .hljs-formula {
  color: #a626a4;
}
.hljs-section, .hljs-name, .hljs-selector-tag, .hljs-deletion, .hljs-subst {
  color: #e45649;
}
.hljs-literal {
  color: #0184bb;
}
.hljs-string, .hljs-regexp, .hljs-addition, .hljs-attribute, .hljs-meta .hljs-string {
  color: #50a14f;
}
.hljs-attr, .hljs-variable, .hljs-template-variable, .hljs-type, .hljs-selector-class, .hljs-selector-attr, .hljs-selector-pseudo, .hljs-number {
  color: #986801;
}
.hljs-symbol, .hljs-bullet, .hljs-link, .hljs-meta, .hljs-selector-id, .hljs-title {
  color: #4078f2;
}
.hljs-built_in, .hljs-title.class_, .hljs-class .hljs-title {
  color: #c18401;
}
.hljs-emphasis {
  font-style: italic;
}
.hljs-strong {
  font-weight: bold;
}
.hljs-link {
  text-decoration: underline;
}
@font-face {
  font-family: KaTeX_AMS;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_AMS-Regular.a79f1c31.woff2) format("woff2"),url(/_next/static/media/KaTeX_AMS-Regular.1608a09b.woff) format("woff"),url(/_next/static/media/KaTeX_AMS-Regular.4aafdb68.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Caligraphic;
  font-style: normal;
  font-weight: 700;
  src: url(/_next/static/media/KaTeX_Caligraphic-Bold.ec17d132.woff2) format("woff2"),url(/_next/static/media/KaTeX_Caligraphic-Bold.b6770918.woff) format("woff"),url(/_next/static/media/KaTeX_Caligraphic-Bold.cce5b8ec.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Caligraphic;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Caligraphic-Regular.55fac258.woff2) format("woff2"),url(/_next/static/media/KaTeX_Caligraphic-Regular.dad44a7f.woff) format("woff"),url(/_next/static/media/KaTeX_Caligraphic-Regular.07ef19e7.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Fraktur;
  font-style: normal;
  font-weight: 700;
  src: url(/_next/static/media/KaTeX_Fraktur-Bold.d42a5579.woff2) format("woff2"),url(/_next/static/media/KaTeX_Fraktur-Bold.9f256b85.woff) format("woff"),url(/_next/static/media/KaTeX_Fraktur-Bold.b18f59e1.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Fraktur;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Fraktur-Regular.d3c882a6.woff2) format("woff2"),url(/_next/static/media/KaTeX_Fraktur-Regular.7c187121.woff) format("woff"),url(/_next/static/media/KaTeX_Fraktur-Regular.ed38e79f.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Main;
  font-style: normal;
  font-weight: 700;
  src: url(/_next/static/media/KaTeX_Main-Bold.c3fb5ac2.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Bold.d181c465.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Bold.b74a1a8b.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Main;
  font-style: italic;
  font-weight: 700;
  src: url(/_next/static/media/KaTeX_Main-BoldItalic.6f2bb1df.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-BoldItalic.e3f82f9d.woff) format("woff"),url(/_next/static/media/KaTeX_Main-BoldItalic.70d8b0a5.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Main;
  font-style: italic;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Main-Italic.8916142b.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Italic.9024d815.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Italic.47373d1e.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Main;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Main-Regular.0462f03b.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Regular.7f51fe03.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Regular.b7f8fe9b.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Math;
  font-style: italic;
  font-weight: 700;
  src: url(/_next/static/media/KaTeX_Math-BoldItalic.572d331f.woff2) format("woff2"),url(/_next/static/media/KaTeX_Math-BoldItalic.f1035d8d.woff) format("woff"),url(/_next/static/media/KaTeX_Math-BoldItalic.a879cf83.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Math;
  font-style: italic;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Math-Italic.f28c23ac.woff2) format("woff2"),url(/_next/static/media/KaTeX_Math-Italic.5295ba48.woff) format("woff"),url(/_next/static/media/KaTeX_Math-Italic.939bc644.ttf) format("truetype");
}
@font-face {
  font-family: "KaTeX_SansSerif";
  font-style: normal;
  font-weight: 700;
  src: url(/_next/static/media/KaTeX_SansSerif-Bold.8c5b5494.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Bold.bf59d231.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Bold.94e1e8dc.ttf) format("truetype");
}
@font-face {
  font-family: "KaTeX_SansSerif";
  font-style: italic;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_SansSerif-Italic.3b1e59b3.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Italic.7c9bc82b.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Italic.b4c20c84.ttf) format("truetype");
}
@font-face {
  font-family: "KaTeX_SansSerif";
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_SansSerif-Regular.ba21ed5f.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Regular.74048478.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Regular.d4d7ba48.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Script;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Script-Regular.03e9641d.woff2) format("woff2"),url(/_next/static/media/KaTeX_Script-Regular.07505710.woff) format("woff"),url(/_next/static/media/KaTeX_Script-Regular.fe9cbbe1.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Size1;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Size1-Regular.eae34984.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size1-Regular.e1e279cb.woff) format("woff"),url(/_next/static/media/KaTeX_Size1-Regular.fabc004a.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Size2;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Size2-Regular.5916a24f.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size2-Regular.57727022.woff) format("woff"),url(/_next/static/media/KaTeX_Size2-Regular.d6b476ec.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Size3;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Size3-Regular.b4230e7e.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size3-Regular.9acaf01c.woff) format("woff"),url(/_next/static/media/KaTeX_Size3-Regular.a144ef58.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Size4;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Size4-Regular.10d95fd3.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size4-Regular.7a996c9d.woff) format("woff"),url(/_next/static/media/KaTeX_Size4-Regular.fbccdabe.ttf) format("truetype");
}
@font-face {
  font-family: KaTeX_Typewriter;
  font-style: normal;
  font-weight: 400;
  src: url(/_next/static/media/KaTeX_Typewriter-Regular.a8709e36.woff2) format("woff2"),url(/_next/static/media/KaTeX_Typewriter-Regular.6258592b.woff) format("woff"),url(/_next/static/media/KaTeX_Typewriter-Regular.d97aaf4a.ttf) format("truetype");
}
.katex {
  font: normal 1.21em KaTeX_Main,Times New Roman,serif;
  line-height: 1.2;
  text-indent: 0;
  text-rendering: auto;
}
.katex * {
  -ms-high-contrast-adjust: none !important;
  border-color: currentColor;
}
.katex .katex-version:after {
  content: "0.16.22";
}
.katex .katex-mathml {
  clip: rect(1px,1px,1px,1px);
  border: 0;
  height: 1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
.katex .katex-html>.newline {
  display: block;
}
.katex .base {
  position: relative;
  white-space: nowrap;
  width: -moz-min-content;
  width: min-content;
}
.katex .base,.katex .strut {
  display: inline-block;
}
.katex .textbf {
  font-weight: 700;
}
.katex .textit {
  font-style: italic;
}
.katex .textrm {
  font-family: KaTeX_Main;
}
.katex .textsf {
  font-family: KaTeX_SansSerif;
}
.katex .texttt {
  font-family: KaTeX_Typewriter;
}
.katex .mathnormal {
  font-family: KaTeX_Math;
  font-style: italic;
}
.katex .mathit {
  font-family: KaTeX_Main;
  font-style: italic;
}
.katex .mathrm {
  font-style: normal;
}
.katex .mathbf {
  font-family: KaTeX_Main;
  font-weight: 700;
}
.katex .boldsymbol {
  font-family: KaTeX_Math;
  font-style: italic;
  font-weight: 700;
}
.katex .amsrm,.katex .mathbb,.katex .textbb {
  font-family: KaTeX_AMS;
}
.katex .mathcal {
  font-family: KaTeX_Caligraphic;
}
.katex .mathfrak,.katex .textfrak {
  font-family: KaTeX_Fraktur;
}
.katex .mathboldfrak,.katex .textboldfrak {
  font-family: KaTeX_Fraktur;
  font-weight: 700;
}
.katex .mathtt {
  font-family: KaTeX_Typewriter;
}
.katex .mathscr,.katex .textscr {
  font-family: KaTeX_Script;
}
.katex .mathsf,.katex .textsf {
  font-family: KaTeX_SansSerif;
}
.katex .mathboldsf,.katex .textboldsf {
  font-family: KaTeX_SansSerif;
  font-weight: 700;
}
.katex .mathitsf,.katex .mathsfit,.katex .textitsf {
  font-family: KaTeX_SansSerif;
  font-style: italic;
}
.katex .mainrm {
  font-family: KaTeX_Main;
  font-style: normal;
}
.katex .vlist-t {
  border-collapse: collapse;
  display: inline-table;
  table-layout: fixed;
}
.katex .vlist-r {
  display: table-row;
}
.katex .vlist {
  display: table-cell;
  position: relative;
  vertical-align: bottom;
}
.katex .vlist>span {
  display: block;
  height: 0;
  position: relative;
}
.katex .vlist>span>span {
  display: inline-block;
}
.katex .vlist>span>.pstrut {
  overflow: hidden;
  width: 0;
}
.katex .vlist-t2 {
  margin-right: -2px;
}
.katex .vlist-s {
  display: table-cell;
  font-size: 1px;
  min-width: 2px;
  vertical-align: bottom;
  width: 2px;
}
.katex .vbox {
  align-items: baseline;
  display: inline-flex;
  flex-direction: column;
}
.katex .hbox {
  width: 100%;
}
.katex .hbox,.katex .thinbox {
  display: inline-flex;
  flex-direction: row;
}
.katex .thinbox {
  max-width: 0;
  width: 0;
}
.katex .msupsub {
  text-align: left;
}
.katex .mfrac>span>span {
  text-align: center;
}
.katex .mfrac .frac-line {
  border-bottom-style: solid;
  display: inline-block;
  width: 100%;
}
.katex .hdashline,.katex .hline,.katex .mfrac .frac-line,.katex .overline .overline-line,.katex .rule,.katex .underline .underline-line {
  min-height: 1px;
}
.katex .mspace {
  display: inline-block;
}
.katex .clap,.katex .llap,.katex .rlap {
  position: relative;
  width: 0;
}
.katex .clap>.inner,.katex .llap>.inner,.katex .rlap>.inner {
  position: absolute;
}
.katex .clap>.fix,.katex .llap>.fix,.katex .rlap>.fix {
  display: inline-block;
}
.katex .llap>.inner {
  right: 0;
}
.katex .clap>.inner,.katex .rlap>.inner {
  left: 0;
}
.katex .clap>.inner>span {
  margin-left: -50%;
  margin-right: 50%;
}
.katex .rule {
  border: 0 solid;
  display: inline-block;
  position: relative;
}
.katex .hline,.katex .overline .overline-line,.katex .underline .underline-line {
  border-bottom-style: solid;
  display: inline-block;
  width: 100%;
}
.katex .hdashline {
  border-bottom-style: dashed;
  display: inline-block;
  width: 100%;
}
.katex .sqrt>.root {
  margin-left: .2777777778em;
  margin-right: -.5555555556em;
}
.katex .fontsize-ensurer.reset-size1.size1,.katex .sizing.reset-size1.size1 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size1.size2,.katex .sizing.reset-size1.size2 {
  font-size: 1.2em;
}
.katex .fontsize-ensurer.reset-size1.size3,.katex .sizing.reset-size1.size3 {
  font-size: 1.4em;
}
.katex .fontsize-ensurer.reset-size1.size4,.katex .sizing.reset-size1.size4 {
  font-size: 1.6em;
}
.katex .fontsize-ensurer.reset-size1.size5,.katex .sizing.reset-size1.size5 {
  font-size: 1.8em;
}
.katex .fontsize-ensurer.reset-size1.size6,.katex .sizing.reset-size1.size6 {
  font-size: 2em;
}
.katex .fontsize-ensurer.reset-size1.size7,.katex .sizing.reset-size1.size7 {
  font-size: 2.4em;
}
.katex .fontsize-ensurer.reset-size1.size8,.katex .sizing.reset-size1.size8 {
  font-size: 2.88em;
}
.katex .fontsize-ensurer.reset-size1.size9,.katex .sizing.reset-size1.size9 {
  font-size: 3.456em;
}
.katex .fontsize-ensurer.reset-size1.size10,.katex .sizing.reset-size1.size10 {
  font-size: 4.148em;
}
.katex .fontsize-ensurer.reset-size1.size11,.katex .sizing.reset-size1.size11 {
  font-size: 4.976em;
}
.katex .fontsize-ensurer.reset-size2.size1,.katex .sizing.reset-size2.size1 {
  font-size: .8333333333em;
}
.katex .fontsize-ensurer.reset-size2.size2,.katex .sizing.reset-size2.size2 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size2.size3,.katex .sizing.reset-size2.size3 {
  font-size: 1.1666666667em;
}
.katex .fontsize-ensurer.reset-size2.size4,.katex .sizing.reset-size2.size4 {
  font-size: 1.3333333333em;
}
.katex .fontsize-ensurer.reset-size2.size5,.katex .sizing.reset-size2.size5 {
  font-size: 1.5em;
}
.katex .fontsize-ensurer.reset-size2.size6,.katex .sizing.reset-size2.size6 {
  font-size: 1.6666666667em;
}
.katex .fontsize-ensurer.reset-size2.size7,.katex .sizing.reset-size2.size7 {
  font-size: 2em;
}
.katex .fontsize-ensurer.reset-size2.size8,.katex .sizing.reset-size2.size8 {
  font-size: 2.4em;
}
.katex .fontsize-ensurer.reset-size2.size9,.katex .sizing.reset-size2.size9 {
  font-size: 2.88em;
}
.katex .fontsize-ensurer.reset-size2.size10,.katex .sizing.reset-size2.size10 {
  font-size: 3.4566666667em;
}
.katex .fontsize-ensurer.reset-size2.size11,.katex .sizing.reset-size2.size11 {
  font-size: 4.1466666667em;
}
.katex .fontsize-ensurer.reset-size3.size1,.katex .sizing.reset-size3.size1 {
  font-size: .7142857143em;
}
.katex .fontsize-ensurer.reset-size3.size2,.katex .sizing.reset-size3.size2 {
  font-size: .8571428571em;
}
.katex .fontsize-ensurer.reset-size3.size3,.katex .sizing.reset-size3.size3 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size3.size4,.katex .sizing.reset-size3.size4 {
  font-size: 1.1428571429em;
}
.katex .fontsize-ensurer.reset-size3.size5,.katex .sizing.reset-size3.size5 {
  font-size: 1.2857142857em;
}
.katex .fontsize-ensurer.reset-size3.size6,.katex .sizing.reset-size3.size6 {
  font-size: 1.4285714286em;
}
.katex .fontsize-ensurer.reset-size3.size7,.katex .sizing.reset-size3.size7 {
  font-size: 1.7142857143em;
}
.katex .fontsize-ensurer.reset-size3.size8,.katex .sizing.reset-size3.size8 {
  font-size: 2.0571428571em;
}
.katex .fontsize-ensurer.reset-size3.size9,.katex .sizing.reset-size3.size9 {
  font-size: 2.4685714286em;
}
.katex .fontsize-ensurer.reset-size3.size10,.katex .sizing.reset-size3.size10 {
  font-size: 2.9628571429em;
}
.katex .fontsize-ensurer.reset-size3.size11,.katex .sizing.reset-size3.size11 {
  font-size: 3.5542857143em;
}
.katex .fontsize-ensurer.reset-size4.size1,.katex .sizing.reset-size4.size1 {
  font-size: .625em;
}
.katex .fontsize-ensurer.reset-size4.size2,.katex .sizing.reset-size4.size2 {
  font-size: .75em;
}
.katex .fontsize-ensurer.reset-size4.size3,.katex .sizing.reset-size4.size3 {
  font-size: .875em;
}
.katex .fontsize-ensurer.reset-size4.size4,.katex .sizing.reset-size4.size4 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size4.size5,.katex .sizing.reset-size4.size5 {
  font-size: 1.125em;
}
.katex .fontsize-ensurer.reset-size4.size6,.katex .sizing.reset-size4.size6 {
  font-size: 1.25em;
}
.katex .fontsize-ensurer.reset-size4.size7,.katex .sizing.reset-size4.size7 {
  font-size: 1.5em;
}
.katex .fontsize-ensurer.reset-size4.size8,.katex .sizing.reset-size4.size8 {
  font-size: 1.8em;
}
.katex .fontsize-ensurer.reset-size4.size9,.katex .sizing.reset-size4.size9 {
  font-size: 2.16em;
}
.katex .fontsize-ensurer.reset-size4.size10,.katex .sizing.reset-size4.size10 {
  font-size: 2.5925em;
}
.katex .fontsize-ensurer.reset-size4.size11,.katex .sizing.reset-size4.size11 {
  font-size: 3.11em;
}
.katex .fontsize-ensurer.reset-size5.size1,.katex .sizing.reset-size5.size1 {
  font-size: .5555555556em;
}
.katex .fontsize-ensurer.reset-size5.size2,.katex .sizing.reset-size5.size2 {
  font-size: .6666666667em;
}
.katex .fontsize-ensurer.reset-size5.size3,.katex .sizing.reset-size5.size3 {
  font-size: .7777777778em;
}
.katex .fontsize-ensurer.reset-size5.size4,.katex .sizing.reset-size5.size4 {
  font-size: .8888888889em;
}
.katex .fontsize-ensurer.reset-size5.size5,.katex .sizing.reset-size5.size5 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size5.size6,.katex .sizing.reset-size5.size6 {
  font-size: 1.1111111111em;
}
.katex .fontsize-ensurer.reset-size5.size7,.katex .sizing.reset-size5.size7 {
  font-size: 1.3333333333em;
}
.katex .fontsize-ensurer.reset-size5.size8,.katex .sizing.reset-size5.size8 {
  font-size: 1.6em;
}
.katex .fontsize-ensurer.reset-size5.size9,.katex .sizing.reset-size5.size9 {
  font-size: 1.92em;
}
.katex .fontsize-ensurer.reset-size5.size10,.katex .sizing.reset-size5.size10 {
  font-size: 2.3044444444em;
}
.katex .fontsize-ensurer.reset-size5.size11,.katex .sizing.reset-size5.size11 {
  font-size: 2.7644444444em;
}
.katex .fontsize-ensurer.reset-size6.size1,.katex .sizing.reset-size6.size1 {
  font-size: .5em;
}
.katex .fontsize-ensurer.reset-size6.size2,.katex .sizing.reset-size6.size2 {
  font-size: .6em;
}
.katex .fontsize-ensurer.reset-size6.size3,.katex .sizing.reset-size6.size3 {
  font-size: .7em;
}
.katex .fontsize-ensurer.reset-size6.size4,.katex .sizing.reset-size6.size4 {
  font-size: .8em;
}
.katex .fontsize-ensurer.reset-size6.size5,.katex .sizing.reset-size6.size5 {
  font-size: .9em;
}
.katex .fontsize-ensurer.reset-size6.size6,.katex .sizing.reset-size6.size6 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size6.size7,.katex .sizing.reset-size6.size7 {
  font-size: 1.2em;
}
.katex .fontsize-ensurer.reset-size6.size8,.katex .sizing.reset-size6.size8 {
  font-size: 1.44em;
}
.katex .fontsize-ensurer.reset-size6.size9,.katex .sizing.reset-size6.size9 {
  font-size: 1.728em;
}
.katex .fontsize-ensurer.reset-size6.size10,.katex .sizing.reset-size6.size10 {
  font-size: 2.074em;
}
.katex .fontsize-ensurer.reset-size6.size11,.katex .sizing.reset-size6.size11 {
  font-size: 2.488em;
}
.katex .fontsize-ensurer.reset-size7.size1,.katex .sizing.reset-size7.size1 {
  font-size: .4166666667em;
}
.katex .fontsize-ensurer.reset-size7.size2,.katex .sizing.reset-size7.size2 {
  font-size: .5em;
}
.katex .fontsize-ensurer.reset-size7.size3,.katex .sizing.reset-size7.size3 {
  font-size: .5833333333em;
}
.katex .fontsize-ensurer.reset-size7.size4,.katex .sizing.reset-size7.size4 {
  font-size: .6666666667em;
}
.katex .fontsize-ensurer.reset-size7.size5,.katex .sizing.reset-size7.size5 {
  font-size: .75em;
}
.katex .fontsize-ensurer.reset-size7.size6,.katex .sizing.reset-size7.size6 {
  font-size: .8333333333em;
}
.katex .fontsize-ensurer.reset-size7.size7,.katex .sizing.reset-size7.size7 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size7.size8,.katex .sizing.reset-size7.size8 {
  font-size: 1.2em;
}
.katex .fontsize-ensurer.reset-size7.size9,.katex .sizing.reset-size7.size9 {
  font-size: 1.44em;
}
.katex .fontsize-ensurer.reset-size7.size10,.katex .sizing.reset-size7.size10 {
  font-size: 1.7283333333em;
}
.katex .fontsize-ensurer.reset-size7.size11,.katex .sizing.reset-size7.size11 {
  font-size: 2.0733333333em;
}
.katex .fontsize-ensurer.reset-size8.size1,.katex .sizing.reset-size8.size1 {
  font-size: .3472222222em;
}
.katex .fontsize-ensurer.reset-size8.size2,.katex .sizing.reset-size8.size2 {
  font-size: .4166666667em;
}
.katex .fontsize-ensurer.reset-size8.size3,.katex .sizing.reset-size8.size3 {
  font-size: .4861111111em;
}
.katex .fontsize-ensurer.reset-size8.size4,.katex .sizing.reset-size8.size4 {
  font-size: .5555555556em;
}
.katex .fontsize-ensurer.reset-size8.size5,.katex .sizing.reset-size8.size5 {
  font-size: .625em;
}
.katex .fontsize-ensurer.reset-size8.size6,.katex .sizing.reset-size8.size6 {
  font-size: .6944444444em;
}
.katex .fontsize-ensurer.reset-size8.size7,.katex .sizing.reset-size8.size7 {
  font-size: .8333333333em;
}
.katex .fontsize-ensurer.reset-size8.size8,.katex .sizing.reset-size8.size8 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size8.size9,.katex .sizing.reset-size8.size9 {
  font-size: 1.2em;
}
.katex .fontsize-ensurer.reset-size8.size10,.katex .sizing.reset-size8.size10 {
  font-size: 1.4402777778em;
}
.katex .fontsize-ensurer.reset-size8.size11,.katex .sizing.reset-size8.size11 {
  font-size: 1.7277777778em;
}
.katex .fontsize-ensurer.reset-size9.size1,.katex .sizing.reset-size9.size1 {
  font-size: .2893518519em;
}
.katex .fontsize-ensurer.reset-size9.size2,.katex .sizing.reset-size9.size2 {
  font-size: .3472222222em;
}
.katex .fontsize-ensurer.reset-size9.size3,.katex .sizing.reset-size9.size3 {
  font-size: .4050925926em;
}
.katex .fontsize-ensurer.reset-size9.size4,.katex .sizing.reset-size9.size4 {
  font-size: .462962963em;
}
.katex .fontsize-ensurer.reset-size9.size5,.katex .sizing.reset-size9.size5 {
  font-size: .5208333333em;
}
.katex .fontsize-ensurer.reset-size9.size6,.katex .sizing.reset-size9.size6 {
  font-size: .5787037037em;
}
.katex .fontsize-ensurer.reset-size9.size7,.katex .sizing.reset-size9.size7 {
  font-size: .6944444444em;
}
.katex .fontsize-ensurer.reset-size9.size8,.katex .sizing.reset-size9.size8 {
  font-size: .8333333333em;
}
.katex .fontsize-ensurer.reset-size9.size9,.katex .sizing.reset-size9.size9 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size9.size10,.katex .sizing.reset-size9.size10 {
  font-size: 1.2002314815em;
}
.katex .fontsize-ensurer.reset-size9.size11,.katex .sizing.reset-size9.size11 {
  font-size: 1.4398148148em;
}
.katex .fontsize-ensurer.reset-size10.size1,.katex .sizing.reset-size10.size1 {
  font-size: .2410800386em;
}
.katex .fontsize-ensurer.reset-size10.size2,.katex .sizing.reset-size10.size2 {
  font-size: .2892960463em;
}
.katex .fontsize-ensurer.reset-size10.size3,.katex .sizing.reset-size10.size3 {
  font-size: .337512054em;
}
.katex .fontsize-ensurer.reset-size10.size4,.katex .sizing.reset-size10.size4 {
  font-size: .3857280617em;
}
.katex .fontsize-ensurer.reset-size10.size5,.katex .sizing.reset-size10.size5 {
  font-size: .4339440694em;
}
.katex .fontsize-ensurer.reset-size10.size6,.katex .sizing.reset-size10.size6 {
  font-size: .4821600771em;
}
.katex .fontsize-ensurer.reset-size10.size7,.katex .sizing.reset-size10.size7 {
  font-size: .5785920926em;
}
.katex .fontsize-ensurer.reset-size10.size8,.katex .sizing.reset-size10.size8 {
  font-size: .6943105111em;
}
.katex .fontsize-ensurer.reset-size10.size9,.katex .sizing.reset-size10.size9 {
  font-size: .8331726133em;
}
.katex .fontsize-ensurer.reset-size10.size10,.katex .sizing.reset-size10.size10 {
  font-size: 1em;
}
.katex .fontsize-ensurer.reset-size10.size11,.katex .sizing.reset-size10.size11 {
  font-size: 1.1996142719em;
}
.katex .fontsize-ensurer.reset-size11.size1,.katex .sizing.reset-size11.size1 {
  font-size: .2009646302em;
}
.katex .fontsize-ensurer.reset-size11.size2,.katex .sizing.reset-size11.size2 {
  font-size: .2411575563em;
}
.katex .fontsize-ensurer.reset-size11.size3,.katex .sizing.reset-size11.size3 {
  font-size: .2813504823em;
}
.katex .fontsize-ensurer.reset-size11.size4,.katex .sizing.reset-size11.size4 {
  font-size: .3215434084em;
}
.katex .fontsize-ensurer.reset-size11.size5,.katex .sizing.reset-size11.size5 {
  font-size: .3617363344em;
}
.katex .fontsize-ensurer.reset-size11.size6,.katex .sizing.reset-size11.size6 {
  font-size: .4019292605em;
}
.katex .fontsize-ensurer.reset-size11.size7,.katex .sizing.reset-size11.size7 {
  font-size: .4823151125em;
}
.katex .fontsize-ensurer.reset-size11.size8,.katex .sizing.reset-size11.size8 {
  font-size: .578778135em;
}
.katex .fontsize-ensurer.reset-size11.size9,.katex .sizing.reset-size11.size9 {
  font-size: .6945337621em;
}
.katex .fontsize-ensurer.reset-size11.size10,.katex .sizing.reset-size11.size10 {
  font-size: .8336012862em;
}
.katex .fontsize-ensurer.reset-size11.size11,.katex .sizing.reset-size11.size11 {
  font-size: 1em;
}
.katex .delimsizing.size1 {
  font-family: KaTeX_Size1;
}
.katex .delimsizing.size2 {
  font-family: KaTeX_Size2;
}
.katex .delimsizing.size3 {
  font-family: KaTeX_Size3;
}
.katex .delimsizing.size4 {
  font-family: KaTeX_Size4;
}
.katex .delimsizing.mult .delim-size1>span {
  font-family: KaTeX_Size1;
}
.katex .delimsizing.mult .delim-size4>span {
  font-family: KaTeX_Size4;
}
.katex .nulldelimiter {
  display: inline-block;
  width: .12em;
}
.katex .delimcenter,.katex .op-symbol {
  position: relative;
}
.katex .op-symbol.small-op {
  font-family: KaTeX_Size1;
}
.katex .op-symbol.large-op {
  font-family: KaTeX_Size2;
}
.katex .accent>.vlist-t,.katex .op-limits>.vlist-t {
  text-align: center;
}
.katex .accent .accent-body {
  position: relative;
}
.katex .accent .accent-body:not(.accent-full) {
  width: 0;
}
.katex .overlay {
  display: block;
}
.katex .mtable .vertical-separator {
  display: inline-block;
  min-width: 1px;
}
.katex .mtable .arraycolsep {
  display: inline-block;
}
.katex .mtable .col-align-c>.vlist-t {
  text-align: center;
}
.katex .mtable .col-align-l>.vlist-t {
  text-align: left;
}
.katex .mtable .col-align-r>.vlist-t {
  text-align: right;
}
.katex .svg-align {
  text-align: left;
}
.katex svg {
  fill: currentColor;
  stroke: currentColor;
  fill-rule: nonzero;
  fill-opacity: 1;
  stroke-width: 1;
  stroke-linecap: butt;
  stroke-linejoin: miter;
  stroke-miterlimit: 4;
  stroke-dasharray: none;
  stroke-dashoffset: 0;
  stroke-opacity: 1;
  display: block;
  height: inherit;
  position: absolute;
  width: 100%;
}
.katex svg path {
  stroke: none;
}
.katex img {
  border-style: none;
  max-height: none;
  max-width: none;
  min-height: 0;
  min-width: 0;
}
.katex .stretchy {
  display: block;
  overflow: hidden;
  position: relative;
  width: 100%;
}
.katex .stretchy:after,.katex .stretchy:before {
  content: "";
}
.katex .hide-tail {
  overflow: hidden;
  position: relative;
  width: 100%;
}
.katex .halfarrow-left {
  left: 0;
  overflow: hidden;
  position: absolute;
  width: 50.2%;
}
.katex .halfarrow-right {
  overflow: hidden;
  position: absolute;
  right: 0;
  width: 50.2%;
}
.katex .brace-left {
  left: 0;
  overflow: hidden;
  position: absolute;
  width: 25.1%;
}
.katex .brace-center {
  left: 25%;
  overflow: hidden;
  position: absolute;
  width: 50%;
}
.katex .brace-right {
  overflow: hidden;
  position: absolute;
  right: 0;
  width: 25.1%;
}
.katex .x-arrow-pad {
  padding: 0 .5em;
}
.katex .cd-arrow-pad {
  padding: 0 .55556em 0 .27778em;
}
.katex .mover,.katex .munder,.katex .x-arrow {
  text-align: center;
}
.katex .boxpad {
  padding: 0 .3em;
}
.katex .fbox,.katex .fcolorbox {
  border: .04em solid;
  box-sizing: border-box;
}
.katex .cancel-pad {
  padding: 0 .2em;
}
.katex .cancel-lap {
  margin-left: -.2em;
  margin-right: -.2em;
}
.katex .sout {
  border-bottom-style: solid;
  border-bottom-width: .08em;
}
.katex .angl {
  border-right: .049em solid;
  border-top: .049em solid;
  box-sizing: border-box;
  margin-right: .03889em;
}
.katex .anglpad {
  padding: 0 .03889em;
}
.katex .eqn-num:before {
  content: "(" counter(katexEqnNo) ")";
  counter-increment: katexEqnNo;
}
.katex .mml-eqn-num:before {
  content: "(" counter(mmlEqnNo) ")";
  counter-increment: mmlEqnNo;
}
.katex .mtr-glue {
  width: 50%;
}
.katex .cd-vert-arrow {
  display: inline-block;
  position: relative;
}
.katex .cd-label-left {
  display: inline-block;
  position: absolute;
  right: calc(50% + .3em);
  text-align: left;
}
.katex .cd-label-right {
  display: inline-block;
  left: calc(50% + .3em);
  position: absolute;
  text-align: right;
}
.katex-display {
  display: block;
  margin: 1em 0;
  text-align: center;
}
.katex-display>.katex {
  display: block;
  text-align: center;
  white-space: nowrap;
}
.katex-display>.katex>.katex-html {
  display: block;
  position: relative;
}
.katex-display>.katex>.katex-html>.tag {
  position: absolute;
  right: 0;
}
.katex-display.leqno>.katex>.katex-html>.tag {
  left: 0;
  right: auto;
}
.katex-display.fleqn>.katex {
  padding-left: 2em;
  text-align: left;
}
body {
  counter-reset: katexEqnNo mmlEqnNo;
}
.custom-markdown ul:not(.custom-renderer *) {
  list-style-type: disc;
  margin-left: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.custom-markdown ol:not(.custom-renderer *) {
  list-style-type: decimal;
  margin-left: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.custom-markdown li:not(.custom-renderer *) {
  margin-bottom: 5px;
}
.custom-markdown ol ol:not(.custom-renderer *) {
  list-style: lower-alpha;
}
.custom-markdown ul ul:not(.custom-renderer *),
.custom-markdown ol ol:not(.custom-renderer *) {
  margin-left: 20px;
}
.custom-markdown img:not(.custom-renderer *) {
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  margin: 10px 0;
}
.custom-markdown a:not(.custom-renderer *) {
  text-decoration: underline;
  color: #007bff;
}
.custom-markdown h1:not(.custom-renderer *),
.custom-markdown h2:not(.custom-renderer *),
.custom-markdown h3:not(.custom-renderer *),
.custom-markdown h4:not(.custom-renderer *),
.custom-markdown h5:not(.custom-renderer *),
.custom-markdown h6:not(.custom-renderer *) {
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 20px;
}
.custom-markdown h6:not(.custom-renderer *) {
  font-size: 16px;
}
.custom-markdown h5:not(.custom-renderer *) {
  font-size: 18px;
}
.custom-markdown h4:not(.custom-renderer *) {
  font-size: 20px;
}
.custom-markdown h3:not(.custom-renderer *) {
  font-size: 22px;
}
.custom-markdown h2:not(.custom-renderer *) {
  font-size: 24px;
}
.custom-markdown h1:not(.custom-renderer *) {
  font-size: 26px;
}
.custom-markdown hr:not(.custom-renderer *) {
  border: 0;
  border-top: 1px solid #e1e4e8;
  margin: 20px 0;
}
.custom-markdown table:not(.custom-renderer *) {
  width: 100%;
  margin: 20px 0;
  overflow-x: auto;
  display: block;
  border-collapse: collapse;
  font-size: 14px;
}
.custom-markdown table caption:not(.custom-renderer *) {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
  text-align: left;
}
.custom-markdown thead:not(.custom-renderer *) {
  border-bottom: 1px solid #e1e4e8;
}
.custom-markdown th:not(.custom-renderer *) {
  height: 48px;
  padding: 16px;
  text-align: left;
  vertical-align: middle;
  font-weight: 500;
  color: #666;
}
.custom-markdown td:not(.custom-renderer *) {
  padding: 16px;
  vertical-align: middle;
  border-bottom: 1px solid #e1e4e8;
}
.custom-markdown tr:not(.custom-renderer *) {
  transition: background-color 0.2s;
}
.custom-markdown tr:hover:not(.custom-renderer *) {
  background-color: rgba(0, 0, 0, 0.05);
}
.custom-markdown tfoot:not(.custom-renderer *) {
  border-top: 1px solid #e1e4e8;
  background-color: rgba(0, 0, 0, 0.03);
  font-weight: 500;
}
.custom-renderer {
  white-space: initial;
  font-family: var(--font-sans);
}
pre:has(.custom-renderer) {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: inherit;
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@llamaindex/chat-ui/dist/styles/pdf.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.0.7 | MIT License | https://tailwindcss.com */
:root {
  --react-pdf-annotation-layer: 1;
  --annotation-unfocused-field-background: url("data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>");
  --input-focus-border-color: Highlight;
  --input-focus-outline: 1px solid Canvas;
  --input-unfocused-border-color: transparent;
  --input-disabled-border-color: transparent;
  --input-hover-border-color: black;
  --link-outline: none;
}
@media screen and (forced-colors: active) {
  :root {
    --input-focus-border-color: CanvasText;
    --input-unfocused-border-color: ActiveText;
    --input-disabled-border-color: GrayText;
    --input-hover-border-color: Highlight;
    --link-outline: 1.5px solid LinkText;
  }
  .annotationLayer .textWidgetAnnotation :is(input, textarea):required, .annotationLayer .choiceWidgetAnnotation select:required, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required {
    outline: 1.5px solid selectedItem;
  }
  .annotationLayer .linkAnnotation:hover {
    -webkit-backdrop-filter: invert(100%);
            backdrop-filter: invert(100%);
  }
}
.annotationLayer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  transform-origin: 0 0;
  z-index: 3;
}
.annotationLayer[data-main-rotation='90'] .norotate {
  transform: rotate(270deg) translateX(-100%);
}
.annotationLayer[data-main-rotation='180'] .norotate {
  transform: rotate(180deg) translate(-100%, -100%);
}
.annotationLayer[data-main-rotation='270'] .norotate {
  transform: rotate(90deg) translateY(-100%);
}
.annotationLayer canvas {
  position: absolute;
  width: 100%;
  height: 100%;
}
.annotationLayer section {
  position: absolute;
  text-align: initial;
  pointer-events: auto;
  box-sizing: border-box;
  margin: 0;
  transform-origin: 0 0;
}
.annotationLayer .linkAnnotation {
  outline: var(--link-outline);
}
.textLayer.selecting ~ .annotationLayer section {
  pointer-events: none;
}
.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a {
  position: absolute;
  font-size: 1em;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a:hover {
  opacity: 0.2;
  background: rgba(255, 255, 0, 1);
  box-shadow: 0 2px 10px rgba(255, 255, 0, 1);
}
.annotationLayer .textAnnotation img {
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.annotationLayer .textWidgetAnnotation :is(input, textarea), .annotationLayer .choiceWidgetAnnotation select, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  height: 100%;
  margin: 0;
  vertical-align: top;
  width: 100%;
}
.annotationLayer .textWidgetAnnotation :is(input, textarea):required, .annotationLayer .choiceWidgetAnnotation select:required, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required {
  outline: 1.5px solid red;
}
.annotationLayer .choiceWidgetAnnotation select option {
  padding: 0;
}
.annotationLayer .buttonWidgetAnnotation.radioButton input {
  border-radius: 50%;
}
.annotationLayer .textWidgetAnnotation textarea {
  resize: none;
}
.annotationLayer .textWidgetAnnotation :is(input, textarea)[disabled], .annotationLayer .choiceWidgetAnnotation select[disabled], .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input[disabled] {
  background: none;
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
}
.annotationLayer .textWidgetAnnotation :is(input, textarea):hover, .annotationLayer .choiceWidgetAnnotation select:hover, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:hover {
  border: 2px solid var(--input-hover-border-color);
}
.annotationLayer .textWidgetAnnotation :is(input, textarea):hover, .annotationLayer .choiceWidgetAnnotation select:hover, .annotationLayer .buttonWidgetAnnotation.checkBox input:hover {
  border-radius: 2px;
}
.annotationLayer .textWidgetAnnotation :is(input, textarea):focus, .annotationLayer .choiceWidgetAnnotation select:focus {
  background: none;
  border: 2px solid var(--input-focus-border-color);
  border-radius: 2px;
  outline: var(--input-focus-outline);
}
.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) :focus {
  background-image: none;
  background-color: transparent;
}
.annotationLayer .buttonWidgetAnnotation.checkBox :focus {
  border: 2px solid var(--input-focus-border-color);
  border-radius: 2px;
  outline: var(--input-focus-outline);
}
.annotationLayer .buttonWidgetAnnotation.radioButton :focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
}
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before, .annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after, .annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before {
  background-color: CanvasText;
  content: '';
  display: block;
  position: absolute;
}
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before, .annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after {
  height: 80%;
  left: 45%;
  width: 1px;
}
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before {
  transform: rotate(45deg);
}
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after {
  transform: rotate(-45deg);
}
.annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before {
  border-radius: 50%;
  height: 50%;
  left: 30%;
  top: 20%;
  width: 50%;
}
.annotationLayer .textWidgetAnnotation input.comb {
  font-family: monospace;
  padding-left: 2px;
  padding-right: 0;
}
.annotationLayer .textWidgetAnnotation input.comb:focus {
  width: 103%;
}
.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.annotationLayer .popupTriggerArea {
  height: 100%;
  width: 100%;
}
.annotationLayer .fileAttachmentAnnotation .popupTriggerArea {
  position: absolute;
}
.annotationLayer .popupWrapper {
  position: absolute;
  font-size: calc(9px * var(--scale-factor));
  width: 100%;
  min-width: calc(180px * var(--scale-factor));
  pointer-events: none;
}
.annotationLayer .popup {
  position: absolute;
  max-width: calc(180px * var(--scale-factor));
  background-color: rgba(255, 255, 153, 1);
  box-shadow: 0 calc(2px * var(--scale-factor)) calc(5px * var(--scale-factor)) rgba(136, 136, 136, 1);
  border-radius: calc(2px * var(--scale-factor));
  padding: calc(6px * var(--scale-factor));
  margin-left: calc(5px * var(--scale-factor));
  cursor: pointer;
  font: message-box;
  white-space: normal;
  word-wrap: break-word;
  pointer-events: auto;
}
.annotationLayer .popup > * {
  font-size: calc(9px * var(--scale-factor));
}
.annotationLayer .popup h1 {
  display: inline-block;
}
.annotationLayer .popupDate {
  display: inline-block;
  margin-left: calc(5px * var(--scale-factor));
}
.annotationLayer .popupContent {
  border-top: 1px solid rgba(51, 51, 51, 1);
  margin-top: calc(2px * var(--scale-factor));
  padding-top: calc(2px * var(--scale-factor));
}
.annotationLayer .richText > * {
  white-space: pre-wrap;
  font-size: calc(9px * var(--scale-factor));
}
.annotationLayer .highlightAnnotation, .annotationLayer .underlineAnnotation, .annotationLayer .squigglyAnnotation, .annotationLayer .strikeoutAnnotation, .annotationLayer .freeTextAnnotation, .annotationLayer .lineAnnotation svg line, .annotationLayer .squareAnnotation svg rect, .annotationLayer .circleAnnotation svg ellipse, .annotationLayer .polylineAnnotation svg polyline, .annotationLayer .polygonAnnotation svg polygon, .annotationLayer .caretAnnotation, .annotationLayer .inkAnnotation svg polyline, .annotationLayer .stampAnnotation, .annotationLayer .fileAttachmentAnnotation {
  cursor: pointer;
}
.annotationLayer section svg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.annotationLayer .annotationTextContent {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  color: transparent;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  pointer-events: none;
}
.annotationLayer .annotationTextContent span {
  width: 100%;
  display: inline-block;
}
:root {
  --react-pdf-text-layer: 1;
  --highlight-bg-color: rgba(180, 0, 170, 1);
  --highlight-selected-bg-color: rgba(0, 100, 0, 1);
}
@media screen and (forced-colors: active) {
  :root {
    --highlight-bg-color: Highlight;
    --highlight-selected-bg-color: ButtonText;
  }
}
[data-main-rotation='90'] {
  transform: rotate(90deg) translateY(-100%);
}
[data-main-rotation='180'] {
  transform: rotate(180deg) translate(-100%, -100%);
}
[data-main-rotation='270'] {
  transform: rotate(270deg) translateX(-100%);
}
.textLayer {
  position: absolute;
  text-align: initial;
  inset: 0;
  overflow: hidden;
  line-height: 1;
  -webkit-text-size-adjust: none;
     -moz-text-size-adjust: none;
          text-size-adjust: none;
  forced-color-adjust: none;
  transform-origin: 0 0;
  z-index: 2;
}
.textLayer :is(span, br) {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  margin: 0;
  transform-origin: 0 0;
}
.textLayer span.markedContent {
  top: 0;
  height: 0;
}
.textLayer .highlight {
  margin: -1px;
  padding: 1px;
  background-color: var(--highlight-bg-color);
  border-radius: 4px;
}
.textLayer .highlight.appended {
  position: initial;
}
.textLayer .highlight.begin {
  border-radius: 4px 0 0 4px;
}
.textLayer .highlight.end {
  border-radius: 0 4px 4px 0;
}
.textLayer .highlight.middle {
  border-radius: 0;
}
.textLayer .highlight.selected {
  background-color: var(--highlight-selected-bg-color);
}
.textLayer br::-moz-selection {
  background: transparent;
}
.textLayer br::selection {
  background: transparent;
}
.textLayer .endOfContent {
  display: block;
  position: absolute;
  inset: 100% 0 0;
  z-index: -1;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.textLayer.selecting .endOfContent {
  top: 0;
}
.hiddenCanvasElement {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  display: none;
}
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
  height: 100%;
}
.pdf-viewer-container .optionBar {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  align-items: center;
  border: 1px solid #e4e4e7;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
  padding: 4px;
  border-radius: 8px;
  width: 100%;
}
.pdf-viewer-container .optionBar .title {
  padding-left: 8px;
  margin-left: 8px;
  border-right: 1px solid lightslategray;
}
.pdf-viewer-container .optionBar .control {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}
.pdf-viewer-container .optionBar .control .pageControl {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-right: 1px solid lightslategray;
}
.pdf-viewer-container .optionBar .control button {
  border: 1px solid #e4e4e7;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pdf-viewer-container .optionBar .control button:hover {
  background-color: #f4f4f5;
}
.pdf-viewer-container .optionBar .control .pageControl .pageInput {
  border: 1px solid #e4e4e7;
  border-radius: 4px;
  padding: 4px;
  width: 50px;
  text-align: center;
  height: 100%;
  box-sizing: border-box;
}
.pdf-viewer-container .optionBar .control .zoomControl {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}
.pdf-viewer-container .optionBar .control .zoomControl .zoomSelect {
  border: 1px solid #e4e4e7;
  border-radius: 4px;
  padding: 4px;
  height: 100%;
  box-sizing: border-box;
}
@media (max-width: 640px) {
  .pdf-viewer-container .optionBar {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }
  .pdf-viewer-container .optionBar .title {
    border-right: none !important;
  }
}

