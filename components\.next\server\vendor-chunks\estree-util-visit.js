"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/estree-util-visit";
exports.ids = ["vendor-chunks/estree-util-visit"];
exports.modules = {

/***/ "(ssr)/./node_modules/estree-util-visit/lib/color.node.js":
/*!**********************************************************!*\
  !*** ./node_modules/estree-util-visit/lib/color.node.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* binding */ color)\n/* harmony export */ });\n/**\n * @param {string} d\n * @returns {string}\n */\nfunction color(d) {\n  return '\\u001B[33m' + d + '\\u001B[39m'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXN0cmVlLXV0aWwtdmlzaXQvbGliL2NvbG9yLm5vZGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxlc3RyZWUtdXRpbC12aXNpdFxcbGliXFxjb2xvci5ub2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IGRcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb2xvcihkKSB7XG4gIHJldHVybiAnXFx1MDAxQlszM20nICsgZCArICdcXHUwMDFCWzM5bSdcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/estree-util-visit/lib/color.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/estree-util-visit/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/estree-util-visit/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONTINUE: () => (/* binding */ CONTINUE),\n/* harmony export */   EXIT: () => (/* binding */ EXIT),\n/* harmony export */   SKIP: () => (/* binding */ SKIP),\n/* harmony export */   visit: () => (/* binding */ visit)\n/* harmony export */ });\n/* harmony import */ var estree_util_visit_do_not_use_color__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! estree-util-visit/do-not-use-color */ \"(ssr)/./node_modules/estree-util-visit/lib/color.node.js\");\n/**\n * @typedef {import('estree-jsx').Node} Node\n */\n\n/**\n * @typedef {CONTINUE | EXIT | SKIP} Action\n *   Union of the action types.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed), when moving in an array.\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n */\n\n/**\n * @callback Visitor\n *   Handle a node.\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node`, the `Visitor` should\n *   return a new `Index` to specify the sibling to traverse after `node` is\n *   traversed.\n *   Adding or removing next siblings of `node` is handled as expected without\n *   needing to return a new `Index`.\n * @param {Node} node\n *   Found node.\n * @param {string | undefined} key\n *   Field at which `node` lives in its parent (or where a list of nodes lives).\n * @param {number | undefined} index\n *   Index where `node` lives if `parent[key]` is an array.\n * @param {Array<Node>} ancestors\n *   Ancestors of `node`.\n * @returns {Action | ActionTuple | Index | null | undefined | void}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @typedef Visitors\n *   Handle nodes when entering (preorder) and leaving (postorder).\n * @property {Visitor | null | undefined} [enter]\n *   Handle nodes when entering (preorder) (optional).\n * @property {Visitor | null | undefined} [leave]\n *   Handle nodes when leaving (postorder) (optional).\n */\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Continue traversing as normal.\n */\nconst CONTINUE = Symbol('continue')\n\n/**\n * Stop traversing immediately.\n */\nconst EXIT = Symbol('exit')\n\n/**\n * Do not traverse this node’s children.\n */\nconst SKIP = Symbol('skip')\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) and/or *postorder* (**LRN**).\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor(s) when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * @param {Node} tree\n *   Tree to traverse\n * @param {Visitor | Visitors | null | undefined} [visitor]\n *   Handle each node (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction visit(tree, visitor) {\n  /** @type {Visitor | undefined} */\n  let enter\n  /** @type {Visitor | undefined} */\n  let leave\n\n  if (typeof visitor === 'function') {\n    enter = visitor\n  } else if (visitor && typeof visitor === 'object') {\n    if (visitor.enter) enter = visitor.enter\n    if (visitor.leave) leave = visitor.leave\n  }\n\n  build(tree, undefined, undefined, [])()\n\n  /**\n   * @param {Node} node\n   * @param {string | undefined} key\n   * @param {number | undefined} index\n   * @param {Array<Node>} parents\n   */\n  function build(node, key, index, parents) {\n    if (nodelike(node)) {\n      visit.displayName = 'node (' + (0,estree_util_visit_do_not_use_color__WEBPACK_IMPORTED_MODULE_0__.color)(node.type) + ')'\n    }\n\n    return visit\n\n    /**\n     * @returns {ActionTuple}\n     */\n    function visit() {\n      /** @type {ActionTuple} */\n      const result = enter ? toResult(enter(node, key, index, parents)) : []\n\n      if (result[0] === EXIT) {\n        return result\n      }\n\n      if (result[0] !== SKIP) {\n        /** @type {keyof node} */\n        let cKey\n\n        for (cKey in node) {\n          if (\n            own.call(node, cKey) &&\n            node[cKey] &&\n            typeof node[cKey] === 'object' &&\n            // @ts-expect-error: custom esast extension.\n            cKey !== 'data' &&\n            // @ts-expect-error: custom esast extension.\n            cKey !== 'position'\n          ) {\n            const grandparents = parents.concat(node)\n            /** @type {unknown} */\n            const value = node[cKey]\n\n            if (Array.isArray(value)) {\n              const nodes = /** @type {Array<unknown>} */ (value)\n              let cIndex = 0\n\n              while (cIndex > -1 && cIndex < nodes.length) {\n                const subvalue = nodes[cIndex]\n\n                if (nodelike(subvalue)) {\n                  const subresult = build(\n                    subvalue,\n                    cKey,\n                    cIndex,\n                    grandparents\n                  )()\n                  if (subresult[0] === EXIT) return subresult\n                  cIndex =\n                    typeof subresult[1] === 'number' ? subresult[1] : cIndex + 1\n                } else {\n                  cIndex++\n                }\n              }\n            } else if (nodelike(value)) {\n              const subresult = build(value, cKey, undefined, grandparents)()\n              if (subresult[0] === EXIT) return subresult\n            }\n          }\n        }\n      }\n\n      return leave ? toResult(leave(node, key, index, parents)) : result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {Action | ActionTuple | Index | null | undefined | void} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n\n/**\n * Check if something looks like a node.\n *\n * @param {unknown} value\n *   Anything.\n * @returns {value is Node}\n *   Whether `value` looks like a node.\n */\nfunction nodelike(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'type' in value &&\n      typeof value.type === 'string' &&\n      value.type.length > 0\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXN0cmVlLXV0aWwtdmlzaXQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLDJCQUEyQjtBQUN4Qzs7QUFFQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDO0FBQ0E7QUFDQSxhQUFhLG9FQUFvRTtBQUNqRjtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyxvQkFBb0I7QUFDL0I7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsYUFBYTtBQUN4QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsY0FBYyw0QkFBNEI7QUFDMUM7QUFDQSxjQUFjLDRCQUE0QjtBQUMxQztBQUNBOztBQUV3RDs7QUFFeEQsY0FBYzs7QUFFZDtBQUNBO0FBQ0E7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsV0FBVyx1Q0FBdUM7QUFDbEQ7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxxQkFBcUI7QUFDbEM7QUFDQSxhQUFhLHFCQUFxQjtBQUNsQzs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLGFBQWEsTUFBTTtBQUNuQixhQUFhLG9CQUFvQjtBQUNqQyxhQUFhLG9CQUFvQjtBQUNqQyxhQUFhLGFBQWE7QUFDMUI7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHlFQUFLO0FBQzFDOztBQUVBOztBQUVBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxpQkFBaUIsYUFBYTtBQUM5Qjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxtQkFBbUIsWUFBWTtBQUMvQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLFNBQVM7QUFDaEM7O0FBRUE7QUFDQSx1Q0FBdUMsZ0JBQWdCO0FBQ3ZEOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHdEQUF3RDtBQUNuRTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcZXN0cmVlLXV0aWwtdmlzaXRcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdlc3RyZWUtanN4JykuTm9kZX0gTm9kZVxuICovXG5cbi8qKlxuICogQHR5cGVkZWYge0NPTlRJTlVFIHwgRVhJVCB8IFNLSVB9IEFjdGlvblxuICogICBVbmlvbiBvZiB0aGUgYWN0aW9uIHR5cGVzLlxuICpcbiAqIEB0eXBlZGVmIHtbKEFjdGlvbiB8IG51bGwgfCB1bmRlZmluZWQgfCB2b2lkKT8sIChJbmRleCB8IG51bGwgfCB1bmRlZmluZWQpP119IEFjdGlvblR1cGxlXG4gKiAgIExpc3Qgd2l0aCBvbmUgb3IgdHdvIHZhbHVlcywgdGhlIGZpcnN0IGFuIGFjdGlvbiwgdGhlIHNlY29uZCBhbiBpbmRleC5cbiAqXG4gKiBAdHlwZWRlZiB7bnVtYmVyfSBJbmRleFxuICogICBNb3ZlIHRvIHRoZSBzaWJsaW5nIGF0IGBpbmRleGAgbmV4dCAoYWZ0ZXIgbm9kZSBpdHNlbGYgaXMgY29tcGxldGVseVxuICogICB0cmF2ZXJzZWQpLCB3aGVuIG1vdmluZyBpbiBhbiBhcnJheS5cbiAqXG4gKiAgIFVzZWZ1bCBpZiBtdXRhdGluZyB0aGUgdHJlZSwgc3VjaCBhcyByZW1vdmluZyB0aGUgbm9kZSB0aGUgdmlzaXRvciBpc1xuICogICBjdXJyZW50bHkgb24sIG9yIGFueSBvZiBpdHMgcHJldmlvdXMgc2libGluZ3MuXG4gKiAgIFJlc3VsdHMgbGVzcyB0aGFuIDAgb3IgZ3JlYXRlciB0aGFuIG9yIGVxdWFsIHRvIGBjaGlsZHJlbi5sZW5ndGhgIHN0b3BcbiAqICAgdHJhdmVyc2luZyB0aGUgcGFyZW50LlxuICovXG5cbi8qKlxuICogQGNhbGxiYWNrIFZpc2l0b3JcbiAqICAgSGFuZGxlIGEgbm9kZS5cbiAqXG4gKiAgIFZpc2l0b3JzIGFyZSBmcmVlIHRvIHRyYW5zZm9ybSBgbm9kZWAuXG4gKiAgIFRoZXkgY2FuIGFsc28gdHJhbnNmb3JtIHRoZSBwYXJlbnQgb2Ygbm9kZSAodGhlIGxhc3Qgb2YgYGFuY2VzdG9yc2ApLlxuICpcbiAqICAgUmVwbGFjaW5nIGBub2RlYCBpdHNlbGYsIGlmIGBTS0lQYCBpcyBub3QgcmV0dXJuZWQsIHN0aWxsIGNhdXNlcyBpdHNcbiAqICAgZGVzY2VuZGFudHMgdG8gYmUgd2Fsa2VkICh3aGljaCBpcyBhIGJ1ZykuXG4gKlxuICogICBXaGVuIGFkZGluZyBvciByZW1vdmluZyBwcmV2aW91cyBzaWJsaW5ncyBvZiBgbm9kZWAsIHRoZSBgVmlzaXRvcmAgc2hvdWxkXG4gKiAgIHJldHVybiBhIG5ldyBgSW5kZXhgIHRvIHNwZWNpZnkgdGhlIHNpYmxpbmcgdG8gdHJhdmVyc2UgYWZ0ZXIgYG5vZGVgIGlzXG4gKiAgIHRyYXZlcnNlZC5cbiAqICAgQWRkaW5nIG9yIHJlbW92aW5nIG5leHQgc2libGluZ3Mgb2YgYG5vZGVgIGlzIGhhbmRsZWQgYXMgZXhwZWN0ZWQgd2l0aG91dFxuICogICBuZWVkaW5nIHRvIHJldHVybiBhIG5ldyBgSW5kZXhgLlxuICogQHBhcmFtIHtOb2RlfSBub2RlXG4gKiAgIEZvdW5kIG5vZGUuXG4gKiBAcGFyYW0ge3N0cmluZyB8IHVuZGVmaW5lZH0ga2V5XG4gKiAgIEZpZWxkIGF0IHdoaWNoIGBub2RlYCBsaXZlcyBpbiBpdHMgcGFyZW50IChvciB3aGVyZSBhIGxpc3Qgb2Ygbm9kZXMgbGl2ZXMpLlxuICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IGluZGV4XG4gKiAgIEluZGV4IHdoZXJlIGBub2RlYCBsaXZlcyBpZiBgcGFyZW50W2tleV1gIGlzIGFuIGFycmF5LlxuICogQHBhcmFtIHtBcnJheTxOb2RlPn0gYW5jZXN0b3JzXG4gKiAgIEFuY2VzdG9ycyBvZiBgbm9kZWAuXG4gKiBAcmV0dXJucyB7QWN0aW9uIHwgQWN0aW9uVHVwbGUgfCBJbmRleCB8IG51bGwgfCB1bmRlZmluZWQgfCB2b2lkfVxuICogICBXaGF0IHRvIGRvIG5leHQuXG4gKlxuICogICBBbiBgSW5kZXhgIGlzIHRyZWF0ZWQgYXMgYSB0dXBsZSBvZiBgW0NPTlRJTlVFLCBJbmRleF1gLlxuICogICBBbiBgQWN0aW9uYCBpcyB0cmVhdGVkIGFzIGEgdHVwbGUgb2YgYFtBY3Rpb25dYC5cbiAqXG4gKiAgIFBhc3NpbmcgYSB0dXBsZSBiYWNrIG9ubHkgbWFrZXMgc2Vuc2UgaWYgdGhlIGBBY3Rpb25gIGlzIGBTS0lQYC5cbiAqICAgV2hlbiB0aGUgYEFjdGlvbmAgaXMgYEVYSVRgLCB0aGF0IGFjdGlvbiBjYW4gYmUgcmV0dXJuZWQuXG4gKiAgIFdoZW4gdGhlIGBBY3Rpb25gIGlzIGBDT05USU5VRWAsIGBJbmRleGAgY2FuIGJlIHJldHVybmVkLlxuICovXG5cbi8qKlxuICogQHR5cGVkZWYgVmlzaXRvcnNcbiAqICAgSGFuZGxlIG5vZGVzIHdoZW4gZW50ZXJpbmcgKHByZW9yZGVyKSBhbmQgbGVhdmluZyAocG9zdG9yZGVyKS5cbiAqIEBwcm9wZXJ0eSB7VmlzaXRvciB8IG51bGwgfCB1bmRlZmluZWR9IFtlbnRlcl1cbiAqICAgSGFuZGxlIG5vZGVzIHdoZW4gZW50ZXJpbmcgKHByZW9yZGVyKSAob3B0aW9uYWwpLlxuICogQHByb3BlcnR5IHtWaXNpdG9yIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2xlYXZlXVxuICogICBIYW5kbGUgbm9kZXMgd2hlbiBsZWF2aW5nIChwb3N0b3JkZXIpIChvcHRpb25hbCkuXG4gKi9cblxuaW1wb3J0IHtjb2xvcn0gZnJvbSAnZXN0cmVlLXV0aWwtdmlzaXQvZG8tbm90LXVzZS1jb2xvcidcblxuY29uc3Qgb3duID0ge30uaGFzT3duUHJvcGVydHlcblxuLyoqXG4gKiBDb250aW51ZSB0cmF2ZXJzaW5nIGFzIG5vcm1hbC5cbiAqL1xuZXhwb3J0IGNvbnN0IENPTlRJTlVFID0gU3ltYm9sKCdjb250aW51ZScpXG5cbi8qKlxuICogU3RvcCB0cmF2ZXJzaW5nIGltbWVkaWF0ZWx5LlxuICovXG5leHBvcnQgY29uc3QgRVhJVCA9IFN5bWJvbCgnZXhpdCcpXG5cbi8qKlxuICogRG8gbm90IHRyYXZlcnNlIHRoaXMgbm9kZeKAmXMgY2hpbGRyZW4uXG4gKi9cbmV4cG9ydCBjb25zdCBTS0lQID0gU3ltYm9sKCdza2lwJylcblxuLyoqXG4gKiBWaXNpdCBub2Rlcywgd2l0aCBhbmNlc3RyYWwgaW5mb3JtYXRpb24uXG4gKlxuICogVGhpcyBhbGdvcml0aG0gcGVyZm9ybXMgKmRlcHRoLWZpcnN0KiAqdHJlZSB0cmF2ZXJzYWwqIGluICpwcmVvcmRlcipcbiAqICgqKk5MUioqKSBhbmQvb3IgKnBvc3RvcmRlciogKCoqTFJOKiopLlxuICpcbiAqIFdhbGtpbmcgdGhlIHRyZWUgaXMgYW4gaW50ZW5zaXZlIHRhc2suXG4gKiBNYWtlIHVzZSBvZiB0aGUgcmV0dXJuIHZhbHVlcyBvZiB0aGUgdmlzaXRvcihzKSB3aGVuIHBvc3NpYmxlLlxuICogSW5zdGVhZCBvZiB3YWxraW5nIGEgdHJlZSBtdWx0aXBsZSB0aW1lcywgd2FsayBpdCBvbmNlLCB1c2UgYHVuaXN0LXV0aWwtaXNgXG4gKiB0byBjaGVjayBpZiBhIG5vZGUgbWF0Y2hlcywgYW5kIHRoZW4gcGVyZm9ybSBkaWZmZXJlbnQgb3BlcmF0aW9ucy5cbiAqXG4gKiBAcGFyYW0ge05vZGV9IHRyZWVcbiAqICAgVHJlZSB0byB0cmF2ZXJzZVxuICogQHBhcmFtIHtWaXNpdG9yIHwgVmlzaXRvcnMgfCBudWxsIHwgdW5kZWZpbmVkfSBbdmlzaXRvcl1cbiAqICAgSGFuZGxlIGVhY2ggbm9kZSAob3B0aW9uYWwpLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZpc2l0KHRyZWUsIHZpc2l0b3IpIHtcbiAgLyoqIEB0eXBlIHtWaXNpdG9yIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgZW50ZXJcbiAgLyoqIEB0eXBlIHtWaXNpdG9yIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgbGVhdmVcblxuICBpZiAodHlwZW9mIHZpc2l0b3IgPT09ICdmdW5jdGlvbicpIHtcbiAgICBlbnRlciA9IHZpc2l0b3JcbiAgfSBlbHNlIGlmICh2aXNpdG9yICYmIHR5cGVvZiB2aXNpdG9yID09PSAnb2JqZWN0Jykge1xuICAgIGlmICh2aXNpdG9yLmVudGVyKSBlbnRlciA9IHZpc2l0b3IuZW50ZXJcbiAgICBpZiAodmlzaXRvci5sZWF2ZSkgbGVhdmUgPSB2aXNpdG9yLmxlYXZlXG4gIH1cblxuICBidWlsZCh0cmVlLCB1bmRlZmluZWQsIHVuZGVmaW5lZCwgW10pKClcblxuICAvKipcbiAgICogQHBhcmFtIHtOb2RlfSBub2RlXG4gICAqIEBwYXJhbSB7c3RyaW5nIHwgdW5kZWZpbmVkfSBrZXlcbiAgICogQHBhcmFtIHtudW1iZXIgfCB1bmRlZmluZWR9IGluZGV4XG4gICAqIEBwYXJhbSB7QXJyYXk8Tm9kZT59IHBhcmVudHNcbiAgICovXG4gIGZ1bmN0aW9uIGJ1aWxkKG5vZGUsIGtleSwgaW5kZXgsIHBhcmVudHMpIHtcbiAgICBpZiAobm9kZWxpa2Uobm9kZSkpIHtcbiAgICAgIHZpc2l0LmRpc3BsYXlOYW1lID0gJ25vZGUgKCcgKyBjb2xvcihub2RlLnR5cGUpICsgJyknXG4gICAgfVxuXG4gICAgcmV0dXJuIHZpc2l0XG5cbiAgICAvKipcbiAgICAgKiBAcmV0dXJucyB7QWN0aW9uVHVwbGV9XG4gICAgICovXG4gICAgZnVuY3Rpb24gdmlzaXQoKSB7XG4gICAgICAvKiogQHR5cGUge0FjdGlvblR1cGxlfSAqL1xuICAgICAgY29uc3QgcmVzdWx0ID0gZW50ZXIgPyB0b1Jlc3VsdChlbnRlcihub2RlLCBrZXksIGluZGV4LCBwYXJlbnRzKSkgOiBbXVxuXG4gICAgICBpZiAocmVzdWx0WzBdID09PSBFWElUKSB7XG4gICAgICAgIHJldHVybiByZXN1bHRcbiAgICAgIH1cblxuICAgICAgaWYgKHJlc3VsdFswXSAhPT0gU0tJUCkge1xuICAgICAgICAvKiogQHR5cGUge2tleW9mIG5vZGV9ICovXG4gICAgICAgIGxldCBjS2V5XG5cbiAgICAgICAgZm9yIChjS2V5IGluIG5vZGUpIHtcbiAgICAgICAgICBpZiAoXG4gICAgICAgICAgICBvd24uY2FsbChub2RlLCBjS2V5KSAmJlxuICAgICAgICAgICAgbm9kZVtjS2V5XSAmJlxuICAgICAgICAgICAgdHlwZW9mIG5vZGVbY0tleV0gPT09ICdvYmplY3QnICYmXG4gICAgICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBjdXN0b20gZXNhc3QgZXh0ZW5zaW9uLlxuICAgICAgICAgICAgY0tleSAhPT0gJ2RhdGEnICYmXG4gICAgICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBjdXN0b20gZXNhc3QgZXh0ZW5zaW9uLlxuICAgICAgICAgICAgY0tleSAhPT0gJ3Bvc2l0aW9uJ1xuICAgICAgICAgICkge1xuICAgICAgICAgICAgY29uc3QgZ3JhbmRwYXJlbnRzID0gcGFyZW50cy5jb25jYXQobm9kZSlcbiAgICAgICAgICAgIC8qKiBAdHlwZSB7dW5rbm93bn0gKi9cbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gbm9kZVtjS2V5XVxuXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgY29uc3Qgbm9kZXMgPSAvKiogQHR5cGUge0FycmF5PHVua25vd24+fSAqLyAodmFsdWUpXG4gICAgICAgICAgICAgIGxldCBjSW5kZXggPSAwXG5cbiAgICAgICAgICAgICAgd2hpbGUgKGNJbmRleCA+IC0xICYmIGNJbmRleCA8IG5vZGVzLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHN1YnZhbHVlID0gbm9kZXNbY0luZGV4XVxuXG4gICAgICAgICAgICAgICAgaWYgKG5vZGVsaWtlKHN1YnZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgY29uc3Qgc3VicmVzdWx0ID0gYnVpbGQoXG4gICAgICAgICAgICAgICAgICAgIHN1YnZhbHVlLFxuICAgICAgICAgICAgICAgICAgICBjS2V5LFxuICAgICAgICAgICAgICAgICAgICBjSW5kZXgsXG4gICAgICAgICAgICAgICAgICAgIGdyYW5kcGFyZW50c1xuICAgICAgICAgICAgICAgICAgKSgpXG4gICAgICAgICAgICAgICAgICBpZiAoc3VicmVzdWx0WzBdID09PSBFWElUKSByZXR1cm4gc3VicmVzdWx0XG4gICAgICAgICAgICAgICAgICBjSW5kZXggPVxuICAgICAgICAgICAgICAgICAgICB0eXBlb2Ygc3VicmVzdWx0WzFdID09PSAnbnVtYmVyJyA/IHN1YnJlc3VsdFsxXSA6IGNJbmRleCArIDFcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgY0luZGV4KytcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSBpZiAobm9kZWxpa2UodmFsdWUpKSB7XG4gICAgICAgICAgICAgIGNvbnN0IHN1YnJlc3VsdCA9IGJ1aWxkKHZhbHVlLCBjS2V5LCB1bmRlZmluZWQsIGdyYW5kcGFyZW50cykoKVxuICAgICAgICAgICAgICBpZiAoc3VicmVzdWx0WzBdID09PSBFWElUKSByZXR1cm4gc3VicmVzdWx0XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBsZWF2ZSA/IHRvUmVzdWx0KGxlYXZlKG5vZGUsIGtleSwgaW5kZXgsIHBhcmVudHMpKSA6IHJlc3VsdFxuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIFR1cm4gYSByZXR1cm4gdmFsdWUgaW50byBhIGNsZWFuIHJlc3VsdC5cbiAqXG4gKiBAcGFyYW0ge0FjdGlvbiB8IEFjdGlvblR1cGxlIHwgSW5kZXggfCBudWxsIHwgdW5kZWZpbmVkIHwgdm9pZH0gdmFsdWVcbiAqICAgVmFsaWQgcmV0dXJuIHZhbHVlcyBmcm9tIHZpc2l0b3JzLlxuICogQHJldHVybnMge0FjdGlvblR1cGxlfVxuICogICBDbGVhbiByZXN1bHQuXG4gKi9cbmZ1bmN0aW9uIHRvUmVzdWx0KHZhbHVlKSB7XG4gIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgIHJldHVybiB2YWx1ZVxuICB9XG5cbiAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4gW0NPTlRJTlVFLCB2YWx1ZV1cbiAgfVxuXG4gIHJldHVybiBbdmFsdWVdXG59XG5cbi8qKlxuICogQ2hlY2sgaWYgc29tZXRoaW5nIGxvb2tzIGxpa2UgYSBub2RlLlxuICpcbiAqIEBwYXJhbSB7dW5rbm93bn0gdmFsdWVcbiAqICAgQW55dGhpbmcuXG4gKiBAcmV0dXJucyB7dmFsdWUgaXMgTm9kZX1cbiAqICAgV2hldGhlciBgdmFsdWVgIGxvb2tzIGxpa2UgYSBub2RlLlxuICovXG5mdW5jdGlvbiBub2RlbGlrZSh2YWx1ZSkge1xuICByZXR1cm4gQm9vbGVhbihcbiAgICB2YWx1ZSAmJlxuICAgICAgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJlxuICAgICAgJ3R5cGUnIGluIHZhbHVlICYmXG4gICAgICB0eXBlb2YgdmFsdWUudHlwZSA9PT0gJ3N0cmluZycgJiZcbiAgICAgIHZhbHVlLnR5cGUubGVuZ3RoID4gMFxuICApXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/estree-util-visit/lib/index.js\n");

/***/ })

};
;