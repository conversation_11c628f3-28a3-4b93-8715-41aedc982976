"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/style-mod";
exports.ids = ["vendor-chunks/style-mod"];
exports.modules = {

/***/ "(ssr)/./node_modules/style-mod/src/style-mod.js":
/*!*************************************************!*\
  !*** ./node_modules/style-mod/src/style-mod.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleModule: () => (/* binding */ StyleModule)\n/* harmony export */ });\nconst C = \"\\u037c\"\nconst COUNT = typeof Symbol == \"undefined\" ? \"__\" + C : Symbol.for(C)\nconst SET = typeof Symbol == \"undefined\" ? \"__styleSet\" + Math.floor(Math.random() * 1e8) : Symbol(\"styleSet\")\nconst top = typeof globalThis != \"undefined\" ? globalThis : typeof window != \"undefined\" ? window : {}\n\n// :: - Style modules encapsulate a set of CSS rules defined from\n// JavaScript. Their definitions are only available in a given DOM\n// root after it has been _mounted_ there with `StyleModule.mount`.\n//\n// Style modules should be created once and stored somewhere, as\n// opposed to re-creating them every time you need them. The amount of\n// CSS rules generated for a given DOM root is bounded by the amount\n// of style modules that were used. So to avoid leaking rules, don't\n// create these dynamically, but treat them as one-time allocations.\nclass StyleModule {\n  // :: (Object<Style>, ?{finish: ?(string) → string})\n  // Create a style module from the given spec.\n  //\n  // When `finish` is given, it is called on regular (non-`@`)\n  // selectors (after `&` expansion) to compute the final selector.\n  constructor(spec, options) {\n    this.rules = []\n    let {finish} = options || {}\n\n    function splitSelector(selector) {\n      return /^@/.test(selector) ? [selector] : selector.split(/,\\s*/)\n    }\n\n    function render(selectors, spec, target, isKeyframes) {\n      let local = [], isAt = /^@(\\w+)\\b/.exec(selectors[0]), keyframes = isAt && isAt[1] == \"keyframes\"\n      if (isAt && spec == null) return target.push(selectors[0] + \";\")\n      for (let prop in spec) {\n        let value = spec[prop]\n        if (/&/.test(prop)) {\n          render(prop.split(/,\\s*/).map(part => selectors.map(sel => part.replace(/&/, sel))).reduce((a, b) => a.concat(b)),\n                 value, target)\n        } else if (value && typeof value == \"object\") {\n          if (!isAt) throw new RangeError(\"The value of a property (\" + prop + \") should be a primitive value.\")\n          render(splitSelector(prop), value, local, keyframes)\n        } else if (value != null) {\n          local.push(prop.replace(/_.*/, \"\").replace(/[A-Z]/g, l => \"-\" + l.toLowerCase()) + \": \" + value + \";\")\n        }\n      }\n      if (local.length || keyframes) {\n        target.push((finish && !isAt && !isKeyframes ? selectors.map(finish) : selectors).join(\", \") +\n                    \" {\" + local.join(\" \") + \"}\")\n      }\n    }\n\n    for (let prop in spec) render(splitSelector(prop), spec[prop], this.rules)\n  }\n\n  // :: () → string\n  // Returns a string containing the module's CSS rules.\n  getRules() { return this.rules.join(\"\\n\") }\n\n  // :: () → string\n  // Generate a new unique CSS class name.\n  static newName() {\n    let id = top[COUNT] || 1\n    top[COUNT] = id + 1\n    return C + id.toString(36)\n  }\n\n  // :: (union<Document, ShadowRoot>, union<[StyleModule], StyleModule>, ?{nonce: ?string})\n  //\n  // Mount the given set of modules in the given DOM root, which ensures\n  // that the CSS rules defined by the module are available in that\n  // context.\n  //\n  // Rules are only added to the document once per root.\n  //\n  // Rule order will follow the order of the modules, so that rules from\n  // modules later in the array take precedence of those from earlier\n  // modules. If you call this function multiple times for the same root\n  // in a way that changes the order of already mounted modules, the old\n  // order will be changed.\n  //\n  // If a Content Security Policy nonce is provided, it is added to\n  // the `<style>` tag generated by the library.\n  static mount(root, modules, options) {\n    let set = root[SET], nonce = options && options.nonce\n    if (!set) set = new StyleSet(root, nonce)\n    else if (nonce) set.setNonce(nonce)\n    set.mount(Array.isArray(modules) ? modules : [modules], root)\n  }\n}\n\nlet adoptedSet = new Map //<Document, StyleSet>\n\nclass StyleSet {\n  constructor(root, nonce) {\n    let doc = root.ownerDocument || root, win = doc.defaultView\n    if (!root.head && root.adoptedStyleSheets && win.CSSStyleSheet) {\n      let adopted = adoptedSet.get(doc)\n      if (adopted) return root[SET] = adopted\n      this.sheet = new win.CSSStyleSheet\n      adoptedSet.set(doc, this)\n    } else {\n      this.styleTag = doc.createElement(\"style\")\n      if (nonce) this.styleTag.setAttribute(\"nonce\", nonce)\n    }\n    this.modules = []\n    root[SET] = this\n  }\n\n  mount(modules, root) {\n    let sheet = this.sheet\n    let pos = 0 /* Current rule offset */, j = 0 /* Index into this.modules */\n    for (let i = 0; i < modules.length; i++) {\n      let mod = modules[i], index = this.modules.indexOf(mod)\n      if (index < j && index > -1) { // Ordering conflict\n        this.modules.splice(index, 1)\n        j--\n        index = -1\n      }\n      if (index == -1) {\n        this.modules.splice(j++, 0, mod)\n        if (sheet) for (let k = 0; k < mod.rules.length; k++)\n          sheet.insertRule(mod.rules[k], pos++)\n      } else {\n        while (j < index) pos += this.modules[j++].rules.length\n        pos += mod.rules.length\n        j++\n      }\n    }\n\n    if (sheet) {\n      if (root.adoptedStyleSheets.indexOf(this.sheet) < 0)\n        root.adoptedStyleSheets = [this.sheet, ...root.adoptedStyleSheets]\n    } else {\n      let text = \"\"\n      for (let i = 0; i < this.modules.length; i++)\n        text += this.modules[i].getRules() + \"\\n\"\n      this.styleTag.textContent = text\n      let target = root.head || root\n      if (this.styleTag.parentNode != target)\n        target.insertBefore(this.styleTag, target.firstChild)\n    }\n  }\n\n  setNonce(nonce) {\n    if (this.styleTag && this.styleTag.getAttribute(\"nonce\") != nonce)\n      this.styleTag.setAttribute(\"nonce\", nonce)\n  }\n}\n\n// Style::Object<union<Style,string>>\n//\n// A style is an object that, in the simple case, maps CSS property\n// names to strings holding their values, as in `{color: \"red\",\n// fontWeight: \"bold\"}`. The property names can be given in\n// camel-case—the library will insert a dash before capital letters\n// when converting them to CSS.\n//\n// If you include an underscore in a property name, it and everything\n// after it will be removed from the output, which can be useful when\n// providing a property multiple times, for browser compatibility\n// reasons.\n//\n// A property in a style object can also be a sub-selector, which\n// extends the current context to add a pseudo-selector or a child\n// selector. Such a property should contain a `&` character, which\n// will be replaced by the current selector. For example `{\"&:before\":\n// {content: '\"hi\"'}}`. Sub-selectors and regular properties can\n// freely be mixed in a given object. Any property containing a `&` is\n// assumed to be a sub-selector.\n//\n// Finally, a property can specify an @-block to be wrapped around the\n// styles defined inside the object that's the property's value. For\n// example to create a media query you can do `{\"@media screen and\n// (min-width: 400px)\": {...}}`.\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3R5bGUtbW9kL3NyYy9zdHlsZS1tb2QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsMEJBQTBCLDJCQUEyQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLFFBQVE7O0FBRWpCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0VBQW9FO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFVBQVU7QUFDViw4R0FBOEc7QUFDOUc7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsd0JBQXdCO0FBQy9DO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZUFBZTs7QUFFZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwyRUFBMkUsZUFBZTtBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0JBQW9CO0FBQ3hDO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxzQkFBc0I7QUFDekQ7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxzQkFBc0IseUJBQXlCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esa0RBQWtEO0FBQ2xELHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJEO0FBQzNELElBQUksaUJBQWlCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQseUJBQXlCLEtBQUsiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xcc3R5bGUtbW9kXFxzcmNcXHN0eWxlLW1vZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBDID0gXCJcXHUwMzdjXCJcbmNvbnN0IENPVU5UID0gdHlwZW9mIFN5bWJvbCA9PSBcInVuZGVmaW5lZFwiID8gXCJfX1wiICsgQyA6IFN5bWJvbC5mb3IoQylcbmNvbnN0IFNFVCA9IHR5cGVvZiBTeW1ib2wgPT0gXCJ1bmRlZmluZWRcIiA/IFwiX19zdHlsZVNldFwiICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMWU4KSA6IFN5bWJvbChcInN0eWxlU2V0XCIpXG5jb25zdCB0b3AgPSB0eXBlb2YgZ2xvYmFsVGhpcyAhPSBcInVuZGVmaW5lZFwiID8gZ2xvYmFsVGhpcyA6IHR5cGVvZiB3aW5kb3cgIT0gXCJ1bmRlZmluZWRcIiA/IHdpbmRvdyA6IHt9XG5cbi8vIDo6IC0gU3R5bGUgbW9kdWxlcyBlbmNhcHN1bGF0ZSBhIHNldCBvZiBDU1MgcnVsZXMgZGVmaW5lZCBmcm9tXG4vLyBKYXZhU2NyaXB0LiBUaGVpciBkZWZpbml0aW9ucyBhcmUgb25seSBhdmFpbGFibGUgaW4gYSBnaXZlbiBET01cbi8vIHJvb3QgYWZ0ZXIgaXQgaGFzIGJlZW4gX21vdW50ZWRfIHRoZXJlIHdpdGggYFN0eWxlTW9kdWxlLm1vdW50YC5cbi8vXG4vLyBTdHlsZSBtb2R1bGVzIHNob3VsZCBiZSBjcmVhdGVkIG9uY2UgYW5kIHN0b3JlZCBzb21ld2hlcmUsIGFzXG4vLyBvcHBvc2VkIHRvIHJlLWNyZWF0aW5nIHRoZW0gZXZlcnkgdGltZSB5b3UgbmVlZCB0aGVtLiBUaGUgYW1vdW50IG9mXG4vLyBDU1MgcnVsZXMgZ2VuZXJhdGVkIGZvciBhIGdpdmVuIERPTSByb290IGlzIGJvdW5kZWQgYnkgdGhlIGFtb3VudFxuLy8gb2Ygc3R5bGUgbW9kdWxlcyB0aGF0IHdlcmUgdXNlZC4gU28gdG8gYXZvaWQgbGVha2luZyBydWxlcywgZG9uJ3Rcbi8vIGNyZWF0ZSB0aGVzZSBkeW5hbWljYWxseSwgYnV0IHRyZWF0IHRoZW0gYXMgb25lLXRpbWUgYWxsb2NhdGlvbnMuXG5leHBvcnQgY2xhc3MgU3R5bGVNb2R1bGUge1xuICAvLyA6OiAoT2JqZWN0PFN0eWxlPiwgP3tmaW5pc2g6ID8oc3RyaW5nKSDihpIgc3RyaW5nfSlcbiAgLy8gQ3JlYXRlIGEgc3R5bGUgbW9kdWxlIGZyb20gdGhlIGdpdmVuIHNwZWMuXG4gIC8vXG4gIC8vIFdoZW4gYGZpbmlzaGAgaXMgZ2l2ZW4sIGl0IGlzIGNhbGxlZCBvbiByZWd1bGFyIChub24tYEBgKVxuICAvLyBzZWxlY3RvcnMgKGFmdGVyIGAmYCBleHBhbnNpb24pIHRvIGNvbXB1dGUgdGhlIGZpbmFsIHNlbGVjdG9yLlxuICBjb25zdHJ1Y3RvcihzcGVjLCBvcHRpb25zKSB7XG4gICAgdGhpcy5ydWxlcyA9IFtdXG4gICAgbGV0IHtmaW5pc2h9ID0gb3B0aW9ucyB8fCB7fVxuXG4gICAgZnVuY3Rpb24gc3BsaXRTZWxlY3RvcihzZWxlY3Rvcikge1xuICAgICAgcmV0dXJuIC9eQC8udGVzdChzZWxlY3RvcikgPyBbc2VsZWN0b3JdIDogc2VsZWN0b3Iuc3BsaXQoLyxcXHMqLylcbiAgICB9XG5cbiAgICBmdW5jdGlvbiByZW5kZXIoc2VsZWN0b3JzLCBzcGVjLCB0YXJnZXQsIGlzS2V5ZnJhbWVzKSB7XG4gICAgICBsZXQgbG9jYWwgPSBbXSwgaXNBdCA9IC9eQChcXHcrKVxcYi8uZXhlYyhzZWxlY3RvcnNbMF0pLCBrZXlmcmFtZXMgPSBpc0F0ICYmIGlzQXRbMV0gPT0gXCJrZXlmcmFtZXNcIlxuICAgICAgaWYgKGlzQXQgJiYgc3BlYyA9PSBudWxsKSByZXR1cm4gdGFyZ2V0LnB1c2goc2VsZWN0b3JzWzBdICsgXCI7XCIpXG4gICAgICBmb3IgKGxldCBwcm9wIGluIHNwZWMpIHtcbiAgICAgICAgbGV0IHZhbHVlID0gc3BlY1twcm9wXVxuICAgICAgICBpZiAoLyYvLnRlc3QocHJvcCkpIHtcbiAgICAgICAgICByZW5kZXIocHJvcC5zcGxpdCgvLFxccyovKS5tYXAocGFydCA9PiBzZWxlY3RvcnMubWFwKHNlbCA9PiBwYXJ0LnJlcGxhY2UoLyYvLCBzZWwpKSkucmVkdWNlKChhLCBiKSA9PiBhLmNvbmNhdChiKSksXG4gICAgICAgICAgICAgICAgIHZhbHVlLCB0YXJnZXQpXG4gICAgICAgIH0gZWxzZSBpZiAodmFsdWUgJiYgdHlwZW9mIHZhbHVlID09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICBpZiAoIWlzQXQpIHRocm93IG5ldyBSYW5nZUVycm9yKFwiVGhlIHZhbHVlIG9mIGEgcHJvcGVydHkgKFwiICsgcHJvcCArIFwiKSBzaG91bGQgYmUgYSBwcmltaXRpdmUgdmFsdWUuXCIpXG4gICAgICAgICAgcmVuZGVyKHNwbGl0U2VsZWN0b3IocHJvcCksIHZhbHVlLCBsb2NhbCwga2V5ZnJhbWVzKVxuICAgICAgICB9IGVsc2UgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgICBsb2NhbC5wdXNoKHByb3AucmVwbGFjZSgvXy4qLywgXCJcIikucmVwbGFjZSgvW0EtWl0vZywgbCA9PiBcIi1cIiArIGwudG9Mb3dlckNhc2UoKSkgKyBcIjogXCIgKyB2YWx1ZSArIFwiO1wiKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAobG9jYWwubGVuZ3RoIHx8IGtleWZyYW1lcykge1xuICAgICAgICB0YXJnZXQucHVzaCgoZmluaXNoICYmICFpc0F0ICYmICFpc0tleWZyYW1lcyA/IHNlbGVjdG9ycy5tYXAoZmluaXNoKSA6IHNlbGVjdG9ycykuam9pbihcIiwgXCIpICtcbiAgICAgICAgICAgICAgICAgICAgXCIge1wiICsgbG9jYWwuam9pbihcIiBcIikgKyBcIn1cIilcbiAgICAgIH1cbiAgICB9XG5cbiAgICBmb3IgKGxldCBwcm9wIGluIHNwZWMpIHJlbmRlcihzcGxpdFNlbGVjdG9yKHByb3ApLCBzcGVjW3Byb3BdLCB0aGlzLnJ1bGVzKVxuICB9XG5cbiAgLy8gOjogKCkg4oaSIHN0cmluZ1xuICAvLyBSZXR1cm5zIGEgc3RyaW5nIGNvbnRhaW5pbmcgdGhlIG1vZHVsZSdzIENTUyBydWxlcy5cbiAgZ2V0UnVsZXMoKSB7IHJldHVybiB0aGlzLnJ1bGVzLmpvaW4oXCJcXG5cIikgfVxuXG4gIC8vIDo6ICgpIOKGkiBzdHJpbmdcbiAgLy8gR2VuZXJhdGUgYSBuZXcgdW5pcXVlIENTUyBjbGFzcyBuYW1lLlxuICBzdGF0aWMgbmV3TmFtZSgpIHtcbiAgICBsZXQgaWQgPSB0b3BbQ09VTlRdIHx8IDFcbiAgICB0b3BbQ09VTlRdID0gaWQgKyAxXG4gICAgcmV0dXJuIEMgKyBpZC50b1N0cmluZygzNilcbiAgfVxuXG4gIC8vIDo6ICh1bmlvbjxEb2N1bWVudCwgU2hhZG93Um9vdD4sIHVuaW9uPFtTdHlsZU1vZHVsZV0sIFN0eWxlTW9kdWxlPiwgP3tub25jZTogP3N0cmluZ30pXG4gIC8vXG4gIC8vIE1vdW50IHRoZSBnaXZlbiBzZXQgb2YgbW9kdWxlcyBpbiB0aGUgZ2l2ZW4gRE9NIHJvb3QsIHdoaWNoIGVuc3VyZXNcbiAgLy8gdGhhdCB0aGUgQ1NTIHJ1bGVzIGRlZmluZWQgYnkgdGhlIG1vZHVsZSBhcmUgYXZhaWxhYmxlIGluIHRoYXRcbiAgLy8gY29udGV4dC5cbiAgLy9cbiAgLy8gUnVsZXMgYXJlIG9ubHkgYWRkZWQgdG8gdGhlIGRvY3VtZW50IG9uY2UgcGVyIHJvb3QuXG4gIC8vXG4gIC8vIFJ1bGUgb3JkZXIgd2lsbCBmb2xsb3cgdGhlIG9yZGVyIG9mIHRoZSBtb2R1bGVzLCBzbyB0aGF0IHJ1bGVzIGZyb21cbiAgLy8gbW9kdWxlcyBsYXRlciBpbiB0aGUgYXJyYXkgdGFrZSBwcmVjZWRlbmNlIG9mIHRob3NlIGZyb20gZWFybGllclxuICAvLyBtb2R1bGVzLiBJZiB5b3UgY2FsbCB0aGlzIGZ1bmN0aW9uIG11bHRpcGxlIHRpbWVzIGZvciB0aGUgc2FtZSByb290XG4gIC8vIGluIGEgd2F5IHRoYXQgY2hhbmdlcyB0aGUgb3JkZXIgb2YgYWxyZWFkeSBtb3VudGVkIG1vZHVsZXMsIHRoZSBvbGRcbiAgLy8gb3JkZXIgd2lsbCBiZSBjaGFuZ2VkLlxuICAvL1xuICAvLyBJZiBhIENvbnRlbnQgU2VjdXJpdHkgUG9saWN5IG5vbmNlIGlzIHByb3ZpZGVkLCBpdCBpcyBhZGRlZCB0b1xuICAvLyB0aGUgYDxzdHlsZT5gIHRhZyBnZW5lcmF0ZWQgYnkgdGhlIGxpYnJhcnkuXG4gIHN0YXRpYyBtb3VudChyb290LCBtb2R1bGVzLCBvcHRpb25zKSB7XG4gICAgbGV0IHNldCA9IHJvb3RbU0VUXSwgbm9uY2UgPSBvcHRpb25zICYmIG9wdGlvbnMubm9uY2VcbiAgICBpZiAoIXNldCkgc2V0ID0gbmV3IFN0eWxlU2V0KHJvb3QsIG5vbmNlKVxuICAgIGVsc2UgaWYgKG5vbmNlKSBzZXQuc2V0Tm9uY2Uobm9uY2UpXG4gICAgc2V0Lm1vdW50KEFycmF5LmlzQXJyYXkobW9kdWxlcykgPyBtb2R1bGVzIDogW21vZHVsZXNdLCByb290KVxuICB9XG59XG5cbmxldCBhZG9wdGVkU2V0ID0gbmV3IE1hcCAvLzxEb2N1bWVudCwgU3R5bGVTZXQ+XG5cbmNsYXNzIFN0eWxlU2V0IHtcbiAgY29uc3RydWN0b3Iocm9vdCwgbm9uY2UpIHtcbiAgICBsZXQgZG9jID0gcm9vdC5vd25lckRvY3VtZW50IHx8IHJvb3QsIHdpbiA9IGRvYy5kZWZhdWx0Vmlld1xuICAgIGlmICghcm9vdC5oZWFkICYmIHJvb3QuYWRvcHRlZFN0eWxlU2hlZXRzICYmIHdpbi5DU1NTdHlsZVNoZWV0KSB7XG4gICAgICBsZXQgYWRvcHRlZCA9IGFkb3B0ZWRTZXQuZ2V0KGRvYylcbiAgICAgIGlmIChhZG9wdGVkKSByZXR1cm4gcm9vdFtTRVRdID0gYWRvcHRlZFxuICAgICAgdGhpcy5zaGVldCA9IG5ldyB3aW4uQ1NTU3R5bGVTaGVldFxuICAgICAgYWRvcHRlZFNldC5zZXQoZG9jLCB0aGlzKVxuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnN0eWxlVGFnID0gZG9jLmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKVxuICAgICAgaWYgKG5vbmNlKSB0aGlzLnN0eWxlVGFnLnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKVxuICAgIH1cbiAgICB0aGlzLm1vZHVsZXMgPSBbXVxuICAgIHJvb3RbU0VUXSA9IHRoaXNcbiAgfVxuXG4gIG1vdW50KG1vZHVsZXMsIHJvb3QpIHtcbiAgICBsZXQgc2hlZXQgPSB0aGlzLnNoZWV0XG4gICAgbGV0IHBvcyA9IDAgLyogQ3VycmVudCBydWxlIG9mZnNldCAqLywgaiA9IDAgLyogSW5kZXggaW50byB0aGlzLm1vZHVsZXMgKi9cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG1vZHVsZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGxldCBtb2QgPSBtb2R1bGVzW2ldLCBpbmRleCA9IHRoaXMubW9kdWxlcy5pbmRleE9mKG1vZClcbiAgICAgIGlmIChpbmRleCA8IGogJiYgaW5kZXggPiAtMSkgeyAvLyBPcmRlcmluZyBjb25mbGljdFxuICAgICAgICB0aGlzLm1vZHVsZXMuc3BsaWNlKGluZGV4LCAxKVxuICAgICAgICBqLS1cbiAgICAgICAgaW5kZXggPSAtMVxuICAgICAgfVxuICAgICAgaWYgKGluZGV4ID09IC0xKSB7XG4gICAgICAgIHRoaXMubW9kdWxlcy5zcGxpY2UoaisrLCAwLCBtb2QpXG4gICAgICAgIGlmIChzaGVldCkgZm9yIChsZXQgayA9IDA7IGsgPCBtb2QucnVsZXMubGVuZ3RoOyBrKyspXG4gICAgICAgICAgc2hlZXQuaW5zZXJ0UnVsZShtb2QucnVsZXNba10sIHBvcysrKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgd2hpbGUgKGogPCBpbmRleCkgcG9zICs9IHRoaXMubW9kdWxlc1tqKytdLnJ1bGVzLmxlbmd0aFxuICAgICAgICBwb3MgKz0gbW9kLnJ1bGVzLmxlbmd0aFxuICAgICAgICBqKytcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoc2hlZXQpIHtcbiAgICAgIGlmIChyb290LmFkb3B0ZWRTdHlsZVNoZWV0cy5pbmRleE9mKHRoaXMuc2hlZXQpIDwgMClcbiAgICAgICAgcm9vdC5hZG9wdGVkU3R5bGVTaGVldHMgPSBbdGhpcy5zaGVldCwgLi4ucm9vdC5hZG9wdGVkU3R5bGVTaGVldHNdXG4gICAgfSBlbHNlIHtcbiAgICAgIGxldCB0ZXh0ID0gXCJcIlxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLm1vZHVsZXMubGVuZ3RoOyBpKyspXG4gICAgICAgIHRleHQgKz0gdGhpcy5tb2R1bGVzW2ldLmdldFJ1bGVzKCkgKyBcIlxcblwiXG4gICAgICB0aGlzLnN0eWxlVGFnLnRleHRDb250ZW50ID0gdGV4dFxuICAgICAgbGV0IHRhcmdldCA9IHJvb3QuaGVhZCB8fCByb290XG4gICAgICBpZiAodGhpcy5zdHlsZVRhZy5wYXJlbnROb2RlICE9IHRhcmdldClcbiAgICAgICAgdGFyZ2V0Lmluc2VydEJlZm9yZSh0aGlzLnN0eWxlVGFnLCB0YXJnZXQuZmlyc3RDaGlsZClcbiAgICB9XG4gIH1cblxuICBzZXROb25jZShub25jZSkge1xuICAgIGlmICh0aGlzLnN0eWxlVGFnICYmIHRoaXMuc3R5bGVUYWcuZ2V0QXR0cmlidXRlKFwibm9uY2VcIikgIT0gbm9uY2UpXG4gICAgICB0aGlzLnN0eWxlVGFnLnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKVxuICB9XG59XG5cbi8vIFN0eWxlOjpPYmplY3Q8dW5pb248U3R5bGUsc3RyaW5nPj5cbi8vXG4vLyBBIHN0eWxlIGlzIGFuIG9iamVjdCB0aGF0LCBpbiB0aGUgc2ltcGxlIGNhc2UsIG1hcHMgQ1NTIHByb3BlcnR5XG4vLyBuYW1lcyB0byBzdHJpbmdzIGhvbGRpbmcgdGhlaXIgdmFsdWVzLCBhcyBpbiBge2NvbG9yOiBcInJlZFwiLFxuLy8gZm9udFdlaWdodDogXCJib2xkXCJ9YC4gVGhlIHByb3BlcnR5IG5hbWVzIGNhbiBiZSBnaXZlbiBpblxuLy8gY2FtZWwtY2FzZeKAlHRoZSBsaWJyYXJ5IHdpbGwgaW5zZXJ0IGEgZGFzaCBiZWZvcmUgY2FwaXRhbCBsZXR0ZXJzXG4vLyB3aGVuIGNvbnZlcnRpbmcgdGhlbSB0byBDU1MuXG4vL1xuLy8gSWYgeW91IGluY2x1ZGUgYW4gdW5kZXJzY29yZSBpbiBhIHByb3BlcnR5IG5hbWUsIGl0IGFuZCBldmVyeXRoaW5nXG4vLyBhZnRlciBpdCB3aWxsIGJlIHJlbW92ZWQgZnJvbSB0aGUgb3V0cHV0LCB3aGljaCBjYW4gYmUgdXNlZnVsIHdoZW5cbi8vIHByb3ZpZGluZyBhIHByb3BlcnR5IG11bHRpcGxlIHRpbWVzLCBmb3IgYnJvd3NlciBjb21wYXRpYmlsaXR5XG4vLyByZWFzb25zLlxuLy9cbi8vIEEgcHJvcGVydHkgaW4gYSBzdHlsZSBvYmplY3QgY2FuIGFsc28gYmUgYSBzdWItc2VsZWN0b3IsIHdoaWNoXG4vLyBleHRlbmRzIHRoZSBjdXJyZW50IGNvbnRleHQgdG8gYWRkIGEgcHNldWRvLXNlbGVjdG9yIG9yIGEgY2hpbGRcbi8vIHNlbGVjdG9yLiBTdWNoIGEgcHJvcGVydHkgc2hvdWxkIGNvbnRhaW4gYSBgJmAgY2hhcmFjdGVyLCB3aGljaFxuLy8gd2lsbCBiZSByZXBsYWNlZCBieSB0aGUgY3VycmVudCBzZWxlY3Rvci4gRm9yIGV4YW1wbGUgYHtcIiY6YmVmb3JlXCI6XG4vLyB7Y29udGVudDogJ1wiaGlcIid9fWAuIFN1Yi1zZWxlY3RvcnMgYW5kIHJlZ3VsYXIgcHJvcGVydGllcyBjYW5cbi8vIGZyZWVseSBiZSBtaXhlZCBpbiBhIGdpdmVuIG9iamVjdC4gQW55IHByb3BlcnR5IGNvbnRhaW5pbmcgYSBgJmAgaXNcbi8vIGFzc3VtZWQgdG8gYmUgYSBzdWItc2VsZWN0b3IuXG4vL1xuLy8gRmluYWxseSwgYSBwcm9wZXJ0eSBjYW4gc3BlY2lmeSBhbiBALWJsb2NrIHRvIGJlIHdyYXBwZWQgYXJvdW5kIHRoZVxuLy8gc3R5bGVzIGRlZmluZWQgaW5zaWRlIHRoZSBvYmplY3QgdGhhdCdzIHRoZSBwcm9wZXJ0eSdzIHZhbHVlLiBGb3Jcbi8vIGV4YW1wbGUgdG8gY3JlYXRlIGEgbWVkaWEgcXVlcnkgeW91IGNhbiBkbyBge1wiQG1lZGlhIHNjcmVlbiBhbmRcbi8vIChtaW4td2lkdGg6IDQwMHB4KVwiOiB7Li4ufX1gLlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-mod/src/style-mod.js\n");

/***/ })

};
;