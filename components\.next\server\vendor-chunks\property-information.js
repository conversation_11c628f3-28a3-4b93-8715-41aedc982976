"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/property-information";
exports.ids = ["vendor-chunks/property-information"];
exports.modules = {

/***/ "(ssr)/./node_modules/property-information/index.js":
/*!****************************************************!*\
  !*** ./node_modules/property-information/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* reexport safe */ _lib_find_js__WEBPACK_IMPORTED_MODULE_0__.find),\n/* harmony export */   hastToReact: () => (/* reexport safe */ _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_1__.hastToReact),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   normalize: () => (/* reexport safe */ _lib_normalize_js__WEBPACK_IMPORTED_MODULE_2__.normalize),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/util/merge.js */ \"(ssr)/./node_modules/property-information/lib/util/merge.js\");\n/* harmony import */ var _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/xlink.js */ \"(ssr)/./node_modules/property-information/lib/xlink.js\");\n/* harmony import */ var _lib_xml_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/xml.js */ \"(ssr)/./node_modules/property-information/lib/xml.js\");\n/* harmony import */ var _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/xmlns.js */ \"(ssr)/./node_modules/property-information/lib/xmlns.js\");\n/* harmony import */ var _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/aria.js */ \"(ssr)/./node_modules/property-information/lib/aria.js\");\n/* harmony import */ var _lib_html_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/html.js */ \"(ssr)/./node_modules/property-information/lib/html.js\");\n/* harmony import */ var _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/svg.js */ \"(ssr)/./node_modules/property-information/lib/svg.js\");\n/* harmony import */ var _lib_find_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/find.js */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/hast-to-react.js */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var _lib_normalize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\n\n\n\n\n\n\n\n\n\n\n\nconst html = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__.merge)([_lib_xml_js__WEBPACK_IMPORTED_MODULE_4__.xml, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__.xmlns, _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__.aria, _lib_html_js__WEBPACK_IMPORTED_MODULE_8__.html], 'html')\nconst svg = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_3__.merge)([_lib_xml_js__WEBPACK_IMPORTED_MODULE_4__.xml, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_5__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_6__.xmlns, _lib_aria_js__WEBPACK_IMPORTED_MODULE_7__.aria, _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__.svg], 'svg')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLG1DQUFtQztBQUNoRCxhQUFhLHVDQUF1QztBQUNwRDs7QUFFeUM7QUFDTDtBQUNKO0FBQ0k7QUFDRjtBQUNZO0FBQ0g7O0FBRVQ7QUFDZ0I7QUFDTjtBQUNyQyxhQUFhLHlEQUFLLEVBQUUsNENBQUcsRUFBRSxnREFBSyxFQUFFLGdEQUFLLEVBQUUsOENBQUksRUFBRSw4Q0FBUTtBQUNyRCxZQUFZLHlEQUFLLEVBQUUsNENBQUcsRUFBRSxnREFBSyxFQUFFLGdEQUFLLEVBQUUsOENBQUksRUFBRSw0Q0FBTyIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi91dGlsL2luZm8uanMnKS5JbmZvfSBJbmZvXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi91dGlsL3NjaGVtYS5qcycpLlNjaGVtYX0gU2NoZW1hXG4gKi9cblxuaW1wb3J0IHttZXJnZX0gZnJvbSAnLi9saWIvdXRpbC9tZXJnZS5qcydcbmltcG9ydCB7eGxpbmt9IGZyb20gJy4vbGliL3hsaW5rLmpzJ1xuaW1wb3J0IHt4bWx9IGZyb20gJy4vbGliL3htbC5qcydcbmltcG9ydCB7eG1sbnN9IGZyb20gJy4vbGliL3htbG5zLmpzJ1xuaW1wb3J0IHthcmlhfSBmcm9tICcuL2xpYi9hcmlhLmpzJ1xuaW1wb3J0IHtodG1sIGFzIGh0bWxCYXNlfSBmcm9tICcuL2xpYi9odG1sLmpzJ1xuaW1wb3J0IHtzdmcgYXMgc3ZnQmFzZX0gZnJvbSAnLi9saWIvc3ZnLmpzJ1xuXG5leHBvcnQge2ZpbmR9IGZyb20gJy4vbGliL2ZpbmQuanMnXG5leHBvcnQge2hhc3RUb1JlYWN0fSBmcm9tICcuL2xpYi9oYXN0LXRvLXJlYWN0LmpzJ1xuZXhwb3J0IHtub3JtYWxpemV9IGZyb20gJy4vbGliL25vcm1hbGl6ZS5qcydcbmV4cG9ydCBjb25zdCBodG1sID0gbWVyZ2UoW3htbCwgeGxpbmssIHhtbG5zLCBhcmlhLCBodG1sQmFzZV0sICdodG1sJylcbmV4cG9ydCBjb25zdCBzdmcgPSBtZXJnZShbeG1sLCB4bGluaywgeG1sbnMsIGFyaWEsIHN2Z0Jhc2VdLCAnc3ZnJylcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/aria.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/aria.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aria: () => (/* binding */ aria)\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\n\nconst aria = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaChecked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaColCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaControls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaDropEffect: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaFlowTo: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaGrabbed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaHasPopup: null,\n    ariaHidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaLevel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaLive: null,\n    ariaModal: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiLine: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiSelectable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaOrientation: null,\n    ariaOwns: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaPressed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaReadOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRelevant: null,\n    ariaRequired: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRoleDescription: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaRowCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSelected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaSetSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSort: null,\n    ariaValueMax: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueMin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueNow: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueText: null,\n    role: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/find.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/find.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/defined-info.js */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _util_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/info.js */ \"(ssr)/./node_modules/property-information/lib/util/info.js\");\n/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\n\n\n\n\nconst valid = /^data[-\\w.:]+$/i\nconst dash = /-[a-z]/g\nconst cap = /[A-Z]/g\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nfunction find(schema, value) {\n  const normal = (0,_normalize_js__WEBPACK_IMPORTED_MODULE_0__.normalize)(value)\n  let prop = value\n  let Type = _util_info_js__WEBPACK_IMPORTED_MODULE_1__.Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__.DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/hast-to-react.js":
/*!****************************************************************!*\
  !*** ./node_modules/property-information/lib/hast-to-react.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hastToReact: () => (/* binding */ hastToReact)\n/* harmony export */ });\n/**\n * `hast` is close to `React`, but differs in a couple of cases.\n *\n * To get a React property from a hast property, check if it is in\n * `hastToReact`, if it is, then use the corresponding value,\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nconst hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL2hhc3QtdG8tcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcaGFzdC10by1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGBoYXN0YCBpcyBjbG9zZSB0byBgUmVhY3RgLCBidXQgZGlmZmVycyBpbiBhIGNvdXBsZSBvZiBjYXNlcy5cbiAqXG4gKiBUbyBnZXQgYSBSZWFjdCBwcm9wZXJ0eSBmcm9tIGEgaGFzdCBwcm9wZXJ0eSwgY2hlY2sgaWYgaXQgaXMgaW5cbiAqIGBoYXN0VG9SZWFjdGAsIGlmIGl0IGlzLCB0aGVuIHVzZSB0aGUgY29ycmVzcG9uZGluZyB2YWx1ZSxcbiAqIG90aGVyd2lzZSwgdXNlIHRoZSBoYXN0IHByb3BlcnR5LlxuICpcbiAqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3QgaGFzdFRvUmVhY3QgPSB7XG4gIGNsYXNzSWQ6ICdjbGFzc0lEJyxcbiAgZGF0YVR5cGU6ICdkYXRhdHlwZScsXG4gIGl0ZW1JZDogJ2l0ZW1JRCcsXG4gIHN0cm9rZURhc2hBcnJheTogJ3N0cm9rZURhc2hhcnJheScsXG4gIHN0cm9rZURhc2hPZmZzZXQ6ICdzdHJva2VEYXNob2Zmc2V0JyxcbiAgc3Ryb2tlTGluZUNhcDogJ3N0cm9rZUxpbmVjYXAnLFxuICBzdHJva2VMaW5lSm9pbjogJ3N0cm9rZUxpbmVqb2luJyxcbiAgc3Ryb2tlTWl0ZXJMaW1pdDogJ3N0cm9rZU1pdGVybGltaXQnLFxuICB0eXBlT2Y6ICd0eXBlb2YnLFxuICB4TGlua0FjdHVhdGU6ICd4bGlua0FjdHVhdGUnLFxuICB4TGlua0FyY1JvbGU6ICd4bGlua0FyY3JvbGUnLFxuICB4TGlua0hyZWY6ICd4bGlua0hyZWYnLFxuICB4TGlua1JvbGU6ICd4bGlua1JvbGUnLFxuICB4TGlua1Nob3c6ICd4bGlua1Nob3cnLFxuICB4TGlua1RpdGxlOiAneGxpbmtUaXRsZScsXG4gIHhMaW5rVHlwZTogJ3hsaW5rVHlwZScsXG4gIHhtbG5zWExpbms6ICd4bWxuc1hsaW5rJ1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/hast-to-react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/html.js":
/*!*******************************************************!*\
  !*** ./node_modules/property-information/lib/html.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\n\nconst html = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    acceptCharset: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    accessKey: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    allowPaymentRequest: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    allowUserMedia: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    alt: null,\n    as: null,\n    async: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    autoCapitalize: null,\n    autoComplete: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    autoFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    autoPlay: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    blocking: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    cite: null,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    cols: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    colSpan: null,\n    content: null,\n    contentEditable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    controls: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    controlsList: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    coords: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number | _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    defer: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    dir: null,\n    dirName: null,\n    disabled: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.overloadedBoolean,\n    draggable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    formTarget: null,\n    headers: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    height: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    hidden: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    high: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    href: null,\n    hrefLang: null,\n    htmlFor: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    httpEquiv: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    itemId: null,\n    itemProp: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    itemRef: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    itemScope: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    itemType: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    low: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    manifest: null,\n    max: null,\n    maxLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    multiple: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    muted: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    name: null,\n    nonce: null,\n    noModule: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    noValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    optimum: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pattern: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    placeholder: null,\n    playsInline: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    referrerPolicy: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    required: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    reversed: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    rows: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    rowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    sandbox: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    scope: null,\n    scoped: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    seamless: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    selected: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootClonable: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootDelegatesFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    sizes: null,\n    slot: null,\n    span: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    spellCheck: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    step: null,\n    style: null,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    useMap: null,\n    value: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish,\n    width: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // Lists. Use CSS to reduce space between items instead\n    declare: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<img>` and `<object>`\n    leftMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    marginWidth: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    noResize: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<frame>`\n    noHref: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    disableRemotePlayback: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    prefix: null,\n    property: null,\n    results: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    security: null,\n    unselectable: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/normalize.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/normalize.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize: () => (/* binding */ normalize)\n/* harmony export */ });\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL25vcm1hbGl6ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXG5vcm1hbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG5vcm1hbGl6ZSh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/svg.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/svg.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-sensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n\n\nconst svg = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseSensitiveTransform,\n  properties: {\n    about: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    accentHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    amplitude: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    arabicForm: null,\n    ascent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    by: null,\n    calcMode: null,\n    capHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    diffuseConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    dominantBaseline: null,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    g2: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    glyphName: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    horizOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    horizOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    id: null,\n    ideographic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k1: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k2: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k3: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    k4: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    kernelMatrix: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    overlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pointsAtY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    pointsAtZ: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    rev: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFeatures: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFonts: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    requiredFormats: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    specularExponent: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strikethroughThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    string: null,\n    stroke: null,\n    strokeDashArray: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strokeOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    tableValues: null,\n    target: null,\n    targetX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    targetY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    underlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    values: null,\n    vAlphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vMathematical: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vectorEffect: null,\n    vHanging: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vIdeographic: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    version: null,\n    vertAdvY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vertOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    vertOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_2__.number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseInsensitiveTransform: () => (/* binding */ caseInsensitiveTransform)\n/* harmony export */ });\n/* harmony import */ var _case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./case-sensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return (0,_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__.caseSensitiveTransform)(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0U7O0FBRXBFO0FBQ0EsV0FBVyx3QkFBd0I7QUFDbkMsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1AsU0FBUyxvRkFBc0I7QUFDL0IiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtfSBmcm9tICcuL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1JlY29yZDxzdHJpbmcsIHN0cmluZz59IGF0dHJpYnV0ZXNcbiAqIEBwYXJhbSB7c3RyaW5nfSBwcm9wZXJ0eVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wZXJ0eSkge1xuICByZXR1cm4gY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wZXJ0eS50b0xvd2VyQ2FzZSgpKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************!*\
  !*** ./node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseSensitiveTransform: () => (/* binding */ caseSensitiveTransform)\n/* harmony export */ });\n/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFdBQVcsd0JBQXdCO0FBQ25DLFdBQVcsUUFBUTtBQUNuQixhQUFhO0FBQ2I7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBhdHRyaWJ1dGVzXG4gKiBAcGFyYW0ge3N0cmluZ30gYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBhdHRyaWJ1dGUpIHtcbiAgcmV0dXJuIGF0dHJpYnV0ZSBpbiBhdHRyaWJ1dGVzID8gYXR0cmlidXRlc1thdHRyaWJ1dGVdIDogYXR0cmlidXRlXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/create.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/create.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../normalize.js */ \"(ssr)/./node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\");\n/* harmony import */ var _defined_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defined-info.js */ \"(ssr)/./node_modules/property-information/lib/util/defined-info.js\");\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nfunction create(definition) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  /** @type {string} */\n  let prop\n\n  for (prop in definition.properties) {\n    if (own.call(definition.properties, prop)) {\n      const value = definition.properties[prop]\n      const info = new _defined_info_js__WEBPACK_IMPORTED_MODULE_0__.DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      )\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true\n      }\n\n      property[prop] = info\n\n      normal[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(prop)] = prop\n      normal[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(info.attribute)] = prop\n    }\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_2__.Schema(property, normal, definition.space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************!*\
  !*** ./node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefinedInfo: () => (/* binding */ DefinedInfo)\n/* harmony export */ });\n/* harmony import */ var _info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.js */ \"(ssr)/./node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/property-information/lib/util/types.js\");\n\n\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(_types_js__WEBPACK_IMPORTED_MODULE_0__)\n\nclass DefinedInfo extends _info_js__WEBPACK_IMPORTED_MODULE_1__.Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & _types_js__WEBPACK_IMPORTED_MODULE_0__[check]) === _types_js__WEBPACK_IMPORTED_MODULE_0__[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/info.js":
/*!************************************************************!*\
  !*** ./node_modules/property-information/lib/util/info.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Info: () => (/* binding */ Info)\n/* harmony export */ });\nclass Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property\n    /** @type {string} */\n    this.attribute = attribute\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null\nInfo.prototype.boolean = false\nInfo.prototype.booleanish = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.number = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.spaceSeparated = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.defined = false\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0E7QUFDQTs7QUFFQSxXQUFXLGFBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcaW5mby5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgSW5mbyB7XG4gIC8qKlxuICAgKiBAY29uc3RydWN0b3JcbiAgICogQHBhcmFtIHtzdHJpbmd9IHByb3BlcnR5XG4gICAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAgICovXG4gIGNvbnN0cnVjdG9yKHByb3BlcnR5LCBhdHRyaWJ1dGUpIHtcbiAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICB0aGlzLnByb3BlcnR5ID0gcHJvcGVydHlcbiAgICAvKiogQHR5cGUge3N0cmluZ30gKi9cbiAgICB0aGlzLmF0dHJpYnV0ZSA9IGF0dHJpYnV0ZVxuICB9XG59XG5cbi8qKiBAdHlwZSB7c3RyaW5nfG51bGx9ICovXG5JbmZvLnByb3RvdHlwZS5zcGFjZSA9IG51bGxcbkluZm8ucHJvdG90eXBlLmJvb2xlYW4gPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuYm9vbGVhbmlzaCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5vdmVybG9hZGVkQm9vbGVhbiA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5udW1iZXIgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuY29tbWFTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuc3BhY2VTZXBhcmF0ZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuY29tbWFPclNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLm11c3RVc2VQcm9wZXJ0eSA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5kZWZpbmVkID0gZmFsc2VcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/merge.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   merge: () => (/* binding */ merge)\n/* harmony export */ });\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/property-information/lib/util/schema.js\");\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\n\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nfunction merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  let index = -1\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property)\n    Object.assign(normal, definitions[index].normal)\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_0__.Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEsa0NBQWtDO0FBQy9DLGFBQWEsOEJBQThCO0FBQzNDOztBQUVrQzs7QUFFbEM7QUFDQSxXQUFXLFVBQVU7QUFDckIsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1AsYUFBYSxZQUFZO0FBQ3pCO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsYUFBYSw4Q0FBTTtBQUNuQiIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFxtZXJnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vc2NoZW1hLmpzJykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi9zY2hlbWEuanMnKS5Ob3JtYWx9IE5vcm1hbFxuICovXG5cbmltcG9ydCB7U2NoZW1hfSBmcm9tICcuL3NjaGVtYS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1NjaGVtYVtdfSBkZWZpbml0aW9uc1xuICogQHBhcmFtIHtzdHJpbmd9IFtzcGFjZV1cbiAqIEByZXR1cm5zIHtTY2hlbWF9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtZXJnZShkZWZpbml0aW9ucywgc3BhY2UpIHtcbiAgLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuICBjb25zdCBwcm9wZXJ0eSA9IHt9XG4gIC8qKiBAdHlwZSB7Tm9ybWFsfSAqL1xuICBjb25zdCBub3JtYWwgPSB7fVxuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgZGVmaW5pdGlvbnMubGVuZ3RoKSB7XG4gICAgT2JqZWN0LmFzc2lnbihwcm9wZXJ0eSwgZGVmaW5pdGlvbnNbaW5kZXhdLnByb3BlcnR5KVxuICAgIE9iamVjdC5hc3NpZ24obm9ybWFsLCBkZWZpbml0aW9uc1tpbmRleF0ubm9ybWFsKVxuICB9XG5cbiAgcmV0dXJuIG5ldyBTY2hlbWEocHJvcGVydHksIG5vcm1hbCwgc3BhY2UpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/schema.js":
/*!**************************************************************!*\
  !*** ./node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: () => (/* binding */ Schema)\n/* harmony export */ });\n/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nclass Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property\n    this.normal = normal\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {}\n/** @type {Normal} */\nSchema.prototype.normal = {}\n/** @type {string|null} */\nSchema.prototype.space = null\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsMEJBQTBCO0FBQ3ZDLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsd0JBQXdCO0FBQ3JDOztBQUVPO0FBQ1A7QUFDQTtBQUNBLGFBQWEsWUFBWTtBQUN6QixhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxXQUFXLFlBQVk7QUFDdkI7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxXQUFXLGFBQWE7QUFDeEIiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcc2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9pbmZvLmpzJykuSW5mb30gSW5mb1xuICogQHR5cGVkZWYge1JlY29yZDxzdHJpbmcsIEluZm8+fSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gTm9ybWFsXG4gKi9cblxuZXhwb3J0IGNsYXNzIFNjaGVtYSB7XG4gIC8qKlxuICAgKiBAY29uc3RydWN0b3JcbiAgICogQHBhcmFtIHtQcm9wZXJ0aWVzfSBwcm9wZXJ0eVxuICAgKiBAcGFyYW0ge05vcm1hbH0gbm9ybWFsXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBbc3BhY2VdXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSkge1xuICAgIHRoaXMucHJvcGVydHkgPSBwcm9wZXJ0eVxuICAgIHRoaXMubm9ybWFsID0gbm9ybWFsXG4gICAgaWYgKHNwYWNlKSB7XG4gICAgICB0aGlzLnNwYWNlID0gc3BhY2VcbiAgICB9XG4gIH1cbn1cblxuLyoqIEB0eXBlIHtQcm9wZXJ0aWVzfSAqL1xuU2NoZW1hLnByb3RvdHlwZS5wcm9wZXJ0eSA9IHt9XG4vKiogQHR5cGUge05vcm1hbH0gKi9cblNjaGVtYS5wcm90b3R5cGUubm9ybWFsID0ge31cbi8qKiBAdHlwZSB7c3RyaW5nfG51bGx9ICovXG5TY2hlbWEucHJvdG90eXBlLnNwYWNlID0gbnVsbFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/util/types.js":
/*!*************************************************************!*\
  !*** ./node_modules/property-information/lib/util/types.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   booleanish: () => (/* binding */ booleanish),\n/* harmony export */   commaOrSpaceSeparated: () => (/* binding */ commaOrSpaceSeparated),\n/* harmony export */   commaSeparated: () => (/* binding */ commaSeparated),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   overloadedBoolean: () => (/* binding */ overloadedBoolean),\n/* harmony export */   spaceSeparated: () => (/* binding */ spaceSeparated)\n/* harmony export */ });\nlet powers = 0\n\nconst boolean = increment()\nconst booleanish = increment()\nconst overloadedBoolean = increment()\nconst number = increment()\nconst spaceSeparated = increment()\nconst commaSeparated = increment()\nconst commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOztBQUVPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGNyZWF0ZS1sbGxhbWFcXHB5bGxhbWFpbmRleFxcY29tcG9uZW50c1xcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx1dGlsXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgcG93ZXJzID0gMFxuXG5leHBvcnQgY29uc3QgYm9vbGVhbiA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgYm9vbGVhbmlzaCA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3Qgb3ZlcmxvYWRlZEJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IG51bWJlciA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3Qgc3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IGNvbW1hU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBjb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBpbmNyZW1lbnQoKVxuXG5mdW5jdGlvbiBpbmNyZW1lbnQoKSB7XG4gIHJldHVybiAyICoqICsrcG93ZXJzXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xlink.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xlink.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xlink: () => (/* binding */ xlink)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xlink = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUVoQyxjQUFjLHVEQUFNO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHhsaW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuXG5leHBvcnQgY29uc3QgeGxpbmsgPSBjcmVhdGUoe1xuICBzcGFjZTogJ3hsaW5rJyxcbiAgdHJhbnNmb3JtKF8sIHByb3ApIHtcbiAgICByZXR1cm4gJ3hsaW5rOicgKyBwcm9wLnNsaWNlKDUpLnRvTG93ZXJDYXNlKClcbiAgfSxcbiAgcHJvcGVydGllczoge1xuICAgIHhMaW5rQWN0dWF0ZTogbnVsbCxcbiAgICB4TGlua0FyY1JvbGU6IG51bGwsXG4gICAgeExpbmtIcmVmOiBudWxsLFxuICAgIHhMaW5rUm9sZTogbnVsbCxcbiAgICB4TGlua1Nob3c6IG51bGwsXG4gICAgeExpbmtUaXRsZTogbnVsbCxcbiAgICB4TGlua1R5cGU6IG51bGxcbiAgfVxufSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xml.js":
/*!******************************************************!*\
  !*** ./node_modules/property-information/lib/xml.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n\n\nconst xml = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFaEMsWUFBWSx1REFBTTtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsZUFBZTtBQUNmLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFxjcmVhdGUtbGxsYW1hXFxweWxsYW1haW5kZXhcXGNvbXBvbmVudHNcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxceG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuXG5leHBvcnQgY29uc3QgeG1sID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWwnLFxuICB0cmFuc2Zvcm0oXywgcHJvcCkge1xuICAgIHJldHVybiAneG1sOicgKyBwcm9wLnNsaWNlKDMpLnRvTG93ZXJDYXNlKClcbiAgfSxcbiAgcHJvcGVydGllczoge3htbExhbmc6IG51bGwsIHhtbEJhc2U6IG51bGwsIHhtbFNwYWNlOiBudWxsfVxufSlcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/property-information/lib/xmlns.js":
/*!********************************************************!*\
  !*** ./node_modules/property-information/lib/xmlns.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xmlns: () => (/* binding */ xmlns)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\nconst xmlns = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1QztBQUNzQzs7QUFFdEUsY0FBYyx1REFBTTtBQUMzQjtBQUNBLGVBQWUsMEJBQTBCO0FBQ3pDLGFBQWEseUZBQXdCO0FBQ3JDLGVBQWU7QUFDZixDQUFDIiwic291cmNlcyI6WyJEOlxcY3JlYXRlLWxsbGFtYVxccHlsbGFtYWluZGV4XFxjb21wb25lbnRzXFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHhtbG5zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlfSBmcm9tICcuL3V0aWwvY3JlYXRlLmpzJ1xuaW1wb3J0IHtjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm19IGZyb20gJy4vdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcydcblxuZXhwb3J0IGNvbnN0IHhtbG5zID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWxucycsXG4gIGF0dHJpYnV0ZXM6IHt4bWxuc3hsaW5rOiAneG1sbnM6eGxpbmsnfSxcbiAgdHJhbnNmb3JtOiBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm0sXG4gIHByb3BlcnRpZXM6IHt4bWxuczogbnVsbCwgeG1sbnNYTGluazogbnVsbH1cbn0pXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/property-information/lib/xmlns.js\n");

/***/ })

};
;